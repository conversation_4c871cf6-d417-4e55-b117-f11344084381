#!/usr/bin/env python3
"""
Simple Complete System Test

This script demonstrates how to run a complete task through the whole system
without needing the web API. Perfect for testing the core functionality.
"""

import asyncio
import sys
import logging
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from system_integration import AgenticSystem
from utils.config_loader import load_config

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class SimpleSystemTest:
    """Simple system test that runs a complete task"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    async def run_complete_task(self, task_sentence: str, project_name: str = "test_project"):
        """Run a complete task through the whole system"""
        
        print("🚀 COMPLETE SYSTEM TEST")
        print("=" * 60)
        print(f"📝 Task: {task_sentence}")
        print(f"📁 Project: {project_name}")
        print("=" * 60)
        
        try:
            # 1. Initialize the system
            print("\n🔧 STEP 1: Initializing System...")
            config = load_config('config/config.yaml')

            # Initialize state manager first
            from shared.state_manager import init_state_manager
            init_state_manager(use_redis=False)  # Use in-memory for testing

            system = AgenticSystem(config)
            print("✅ System initialized successfully")
            
            # 2. Process the request
            print(f"\n🤖 STEP 2: Processing Task...")
            print(f"   Input: '{task_sentence}'")
            
            result = await system.process_request(
                user_input=task_sentence,
                context={"project_name": project_name}
            )
            
            print("✅ Task processing completed")
            
            # 3. Show results
            print(f"\n📊 STEP 3: Results Summary...")
            if result:
                print(f"   ✅ Success: {result.get('success', False)}")
                print(f"   📄 Files generated: {len(result.get('files', []))}")
                print(f"   🎯 Quality score: {result.get('quality_score', 'N/A')}")
                
                # Show generated files
                files = result.get('files', [])
                if files:
                    print(f"\n📁 Generated Files:")
                    for file_info in files:
                        filename = file_info.get('filename', 'unknown')
                        size = len(file_info.get('content', ''))
                        print(f"   📄 {filename} ({size} characters)")
                
                # Show project location
                project_path = result.get('project_path')
                if project_path:
                    print(f"\n📂 Project saved to: {project_path}")
                    print(f"   You can find your generated code there!")
            else:
                print("   ⚠️ No result returned")
            
            print(f"\n🎉 COMPLETE SYSTEM TEST FINISHED!")
            return result
            
        except Exception as e:
            print(f"\n❌ ERROR: {e}")
            import traceback
            traceback.print_exc()
            return None

async def main():
    """Main function to run system tests"""
    
    test = SimpleSystemTest()
    
    # Example task sentences you can test
    example_tasks = [
        "create a simple calculator with basic arithmetic operations",
        "create a todo list application with file storage",
        "create a web scraper for extracting product information",
        "create a data visualization tool for CSV files",
        "create a simple chat application with socket programming"
    ]
    
    print("🎯 AVAILABLE TEST TASKS:")
    for i, task in enumerate(example_tasks, 1):
        print(f"   {i}. {task}")
    
    print("\n" + "=" * 60)
    
    # You can change this to test different tasks
    selected_task = example_tasks[0]  # Calculator by default
    project_name = "calculator_app"
    
    print(f"🚀 Running test with: '{selected_task}'")
    
    result = await test.run_complete_task(selected_task, project_name)
    
    if result:
        print(f"\n✅ SUCCESS! Check the '{project_name}' folder for your generated code.")
    else:
        print(f"\n❌ FAILED! Check the error messages above.")

if __name__ == "__main__":
    print("🎓 SONNET MODEL - COMPLETE SYSTEM TEST")
    print("This script tests the complete feedback loop without needing the web API")
    print("=" * 70)
    
    asyncio.run(main())
