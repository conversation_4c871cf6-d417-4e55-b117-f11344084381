"""
Quick test to verify the plan conversion fix works
"""

import asyncio
import logging
from task_manager.services.orchestrator import TaskOrchestrator

logging.basicConfig(level=logging.INFO)

async def test_plan_conversion_fix():
    """Test that the plan conversion works without errors"""
    
    print("🧪 TESTING PLAN CONVERSION FIX")
    print("=" * 40)
    
    config = {
        "llm": {
            "provider": "ollama",
            "model": "deepseek-coder-v2:16b",
            "base_url": "http://localhost:11434"
        },
        "coaching_enabled": False,
        "enable_quality_gates": False,
        "auto_recovery": False
    }
    
    orchestrator = TaskOrchestrator(config)
    await orchestrator.state_manager.initialize()
    
    try:
        # Test with a simple request
        result = await orchestrator._handle_creation_request("create a simple hello world app", {})
        
        if result.get("type") == "plan_created":
            plan = result.get("plan", {})
            print("✅ Plan creation successful!")
            print(f"📋 Plan name: {plan.get('name')}")
            print(f"🎯 Goals: {len(plan.get('goals', []))}")
            print(f"📋 Requirements: {len(plan.get('requirements', []))}")
            print(f"🔧 Steps: {len(plan.get('steps', []))}")
            print(f"📄 Plan file: {result.get('plan_file_path')}")
        else:
            print(f"❌ Unexpected result: {result}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await orchestrator.state_manager.shutdown()

if __name__ == "__main__":
    asyncio.run(test_plan_conversion_fix())
