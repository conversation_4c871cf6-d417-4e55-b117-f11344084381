"""
Task models for Sonnet Model - Re-exports from shared models
"""
from shared.models import Task, TaskStatus, TaskPriority, TaskType
from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
import uuid

class Project(BaseModel):
    """Project model for backward compatibility"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique project identifier")
    name: str = Field(..., description="Project name")
    description: str = Field(..., description="Project description")
    language: str = Field(default="python", description="Primary programming language")
    framework: Optional[str] = Field(default=None, description="Framework to use")
    tasks: List[Task] = Field(default_factory=list, description="Project tasks")
    status: str = Field(default="active", description="Project status")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")

# Re-export for backward compatibility
__all__ = ['Task', 'TaskStatus', 'TaskPriority', 'TaskType', 'Project']
