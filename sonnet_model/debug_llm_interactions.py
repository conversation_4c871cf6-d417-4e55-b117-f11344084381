"""
LLM Interaction Debug - Capture all prompts and responses
This will intercept and log all LLM interactions to see exactly what's being sent and received
"""

import asyncio
import json
import os
import logging
from datetime import datetime
from pathlib import Path
from unittest.mock import patch

# Import the main components
from task_manager.services.orchestrator import TaskOrchestrator
from code_generator.services.llm_interface import LLMInterface
from critique_engine.services.llm_critic import LLMCritic

class LLMDebugCapture:
    """Capture all LLM interactions"""
    
    def __init__(self):
        self.debug_dir = Path("llm_debug_" + datetime.now().strftime("%Y%m%d_%H%M%S"))
        self.debug_dir.mkdir(exist_ok=True)
        self.interaction_counter = 0
        
    def log_llm_interaction(self, component: str, prompt: str, response: str, context: dict = None):
        """Log an LLM interaction"""
        self.interaction_counter += 1
        
        interaction_data = {
            "interaction_id": self.interaction_counter,
            "component": component,
            "timestamp": datetime.now().isoformat(),
            "prompt": prompt,
            "response": response,
            "context": context or {}
        }
        
        filename = f"llm_{self.interaction_counter:03d}_{component.lower().replace(' ', '_')}.json"
        filepath = self.debug_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(interaction_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"🤖 LLM INTERACTION #{self.interaction_counter}: {component}")
        print(f"   📄 Saved to: {filename}")
        print(f"   📝 Prompt length: {len(prompt)} chars")
        print(f"   📋 Response length: {len(response)} chars")
        print()

# Global debug capture instance
llm_debug = LLMDebugCapture()

# Monkey patch the LLM interfaces to capture interactions
original_llm_generate = None
original_critic_generate = None

async def debug_llm_generate(self, prompt: str, context: dict = None, **kwargs):
    """Debug wrapper for LLM generation"""
    global original_llm_generate
    
    # Log the input
    component = getattr(self, '_debug_component_name', 'Code Generator')
    llm_debug.log_llm_interaction(
        component=f"{component} - INPUT",
        prompt=prompt,
        response="[PENDING]",
        context=context
    )
    
    # Call original method
    response = await original_llm_generate(self, prompt, context, **kwargs)
    
    # Log the output
    llm_debug.log_llm_interaction(
        component=f"{component} - OUTPUT",
        prompt="[LOGGED ABOVE]",
        response=response,
        context={"kwargs": kwargs}
    )
    
    return response

async def debug_critic_generate(self, prompt: str, context: dict = None, **kwargs):
    """Debug wrapper for Critic generation"""
    global original_critic_generate
    
    # Log the input
    llm_debug.log_llm_interaction(
        component="Critique Engine - INPUT",
        prompt=prompt,
        response="[PENDING]",
        context=context
    )
    
    # Call original method
    response = await original_critic_generate(self, prompt, context, **kwargs)
    
    # Log the output
    llm_debug.log_llm_interaction(
        component="Critique Engine - OUTPUT", 
        prompt="[LOGGED ABOVE]",
        response=response,
        context={"kwargs": kwargs}
    )
    
    return response

def setup_llm_debugging():
    """Setup LLM debugging by patching the interfaces"""
    global original_llm_generate, original_critic_generate
    
    # Patch LLMInterface
    if hasattr(LLMInterface, 'generate_code'):
        original_llm_generate = LLMInterface.generate_code
        LLMInterface.generate_code = debug_llm_generate
        LLMInterface._debug_component_name = 'Code Generator'
    
    # Patch LLMCritic
    if hasattr(LLMCritic, 'analyze_code'):
        original_critic_generate = LLMCritic.analyze_code
        LLMCritic.analyze_code = debug_critic_generate

def restore_llm_interfaces():
    """Restore original LLM interfaces"""
    global original_llm_generate, original_critic_generate
    
    if original_llm_generate:
        LLMInterface.generate_code = original_llm_generate
    if original_critic_generate:
        LLMCritic.analyze_code = original_critic_generate

async def debug_llm_flow():
    """Run debug session with LLM interaction capture"""
    
    print("🔍 STARTING LLM INTERACTION DEBUG")
    print("=" * 50)
    
    # Setup debugging
    setup_llm_debugging()
    
    try:
        # Create orchestrator
        config = {
            "llm": {
                "provider": "ollama",
                "model": "deepseek-coder-v2:16b",
                "base_url": "http://localhost:11434"
            },
            "coaching_enabled": True,
            "enable_quality_gates": True,
            "max_iterations": 2  # Limit for debug
        }
        
        orchestrator = TaskOrchestrator(config)
        await orchestrator.state_manager.initialize()
        
        # Simple but meaningful task
        task = "create a Python function that reads a CSV file and returns the data as a list of dictionaries"
        
        print(f"🎯 TASK: {task}")
        print()
        
        # Process the task
        result = await orchestrator.process_request(task, {})
        
        print("✅ Task processing completed!")
        print(f"📊 Total LLM interactions captured: {llm_debug.interaction_counter}")
        print(f"📁 Debug data saved to: {llm_debug.debug_dir}")
        
        # Create interaction summary
        summary = {
            "task": task,
            "total_interactions": llm_debug.interaction_counter,
            "debug_directory": str(llm_debug.debug_dir),
            "interactions": []
        }
        
        # List all interaction files
        for i in range(1, llm_debug.interaction_counter + 1):
            interaction_files = list(llm_debug.debug_dir.glob(f"llm_{i:03d}_*.json"))
            if interaction_files:
                summary["interactions"].append({
                    "id": i,
                    "file": interaction_files[0].name
                })
        
        with open(llm_debug.debug_dir / "interaction_summary.json", 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n📋 INTERACTION SUMMARY:")
        for interaction in summary["interactions"]:
            print(f"   🤖 #{interaction['id']}: {interaction['file']}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Restore original interfaces
        restore_llm_interfaces()
        await orchestrator.state_manager.shutdown()

if __name__ == "__main__":
    asyncio.run(debug_llm_flow())
