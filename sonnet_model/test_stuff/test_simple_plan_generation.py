"""
Simple Test for Plan Generation

This test directly tests the TaskPlannerLLM and plan saving functionality
without the full orchestrator initialization.
"""

import asyncio
import logging
import os
import json
from task_planner_llm import TaskPlannerLLM

logging.basicConfig(level=logging.INFO)

async def test_simple_plan_generation():
    """Test TaskPlannerLLM directly"""
    
    print("🧪 TESTING TaskPlannerLLM DIRECTLY")
    print("=" * 50)
    
    # Create the task planner
    planner = TaskPlannerLLM()
    
    try:
        # Test plan creation
        test_request = "create a simple calculator application"
        
        print(f"📝 Testing request: {test_request}")
        print("-" * 40)
        
        # Generate the plan
        plan_data = await planner.create_project_plan(test_request, "application")
        
        if plan_data:
            print("✅ Plan generated successfully!")
            print(f"📋 Project name: {plan_data.get('project_name', 'Unknown')}")
            print(f"📝 Description: {plan_data.get('project_description', 'No description')}")
            print(f"🔧 Tasks: {len(plan_data.get('tasks', []))} tasks")
            
            # Save the plan to see it
            os.makedirs("generated_plans", exist_ok=True)
            plan_file = "generated_plans/test_calculator_plan.json"
            
            with open(plan_file, 'w', encoding='utf-8') as f:
                json.dump(plan_data, f, indent=2, ensure_ascii=False)
            
            print(f"📄 Plan saved to: {plan_file}")
            
            # Show some task details
            tasks = plan_data.get('tasks', [])
            if tasks:
                print("\n📋 Task breakdown:")
                for i, task in enumerate(tasks[:3]):  # Show first 3 tasks
                    print(f"   {i+1}. {task.get('name', 'Unnamed task')}")
                    print(f"      {task.get('description', 'No description')}")
                if len(tasks) > 3:
                    print(f"   ... and {len(tasks) - 3} more tasks")
        else:
            print("❌ Plan generation failed")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simple_plan_generation())
