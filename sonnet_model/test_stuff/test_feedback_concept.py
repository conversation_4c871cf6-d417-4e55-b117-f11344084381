"""
Demonstrate the Feedback Loop Concept

This shows the exact workflow you wanted to see:
1. Code Generator implements Task 1
2. Critique Engine reviews and gives feedback
3. Code Generator fixes issues if any
4. Repeat until Task 1 is perfect
5. Move to Task 2 and repeat
6. Continue until all 3 tasks complete
"""

import asyncio
import time
from typing import List, Dict, Any

class FeedbackLoopDemo:
    """Demonstrate the complete feedback loop workflow"""
    
    def __init__(self):
        # Our 3 test tasks
        self.tasks = [
            {
                "id": 1,
                "name": "Fibonacci Calculator",
                "description": "Create a function to calculate nth Fibonacci number with memoization",
                "requirements": ["Memoization", "Error handling", "Documentation", "Type hints"]
            },
            {
                "id": 2,
                "name": "Stack Data Structure", 
                "description": "Implement a stack with push, pop, peek operations",
                "requirements": ["Empty stack handling", "Size tracking", "Error handling", "String representation"]
            },
            {
                "id": 3,
                "name": "List Merger",
                "description": "Merge two sorted lists into one sorted list",
                "requirements": ["Handle different lengths", "Maintain order", "Edge cases", "Performance"]
            }
        ]
        
        self.current_task = 0
        self.iteration_count = 0
        
    async def run_feedback_loop(self):
        """Run the complete feedback loop for all 3 tasks"""
        
        print("🚀 STARTING FEEDBACK LOOP DEMONSTRATION")
        print("=" * 70)
        print("Workflow: Code Generator → Critique Engine → Feedback → Improvement")
        print("=" * 70)
        
        for task in self.tasks:
            print(f"\n📋 PROCESSING TASK {task['id']}: {task['name']}")
            print(f"Description: {task['description']}")
            print("-" * 70)
            
            # Process this task through the feedback loop
            await self._process_task_with_feedback_loop(task)
            
            print(f"✅ TASK {task['id']} COMPLETED SUCCESSFULLY!")
            print(f"Moving to next task...\n")
        
        print("🎉 ALL 3 TASKS COMPLETED!")
        print("✅ Feedback loop demonstration successful!")
        
    async def _process_task_with_feedback_loop(self, task: Dict):
        """Process a single task through the feedback loop"""
        
        iteration = 1
        max_iterations = 4
        current_code = ""
        issues_found = True
        
        while iteration <= max_iterations and issues_found:
            print(f"\n🔄 ITERATION {iteration}")
            print("-" * 30)
            
            # STEP 1: CODE GENERATOR
            print("🤖 CODE GENERATOR: Working on implementation...")
            await asyncio.sleep(1)  # Simulate processing time
            
            generated_code = await self._simulate_code_generation(task, iteration)
            print(f"   ✅ Generated {len(generated_code)} characters of code")
            
            # Show code preview
            lines = generated_code.split('\n')[:3]
            print("   📝 Code Preview:")
            for line in lines:
                if line.strip():
                    print(f"      {line}")
            if len(generated_code.split('\n')) > 3:
                print("      ...")
            
            # STEP 2: CRITIQUE ENGINE
            print("\n🔍 CRITIQUE ENGINE: Analyzing code quality...")
            await asyncio.sleep(1)  # Simulate analysis time
            
            critique_result = await self._simulate_critique_analysis(generated_code, task, iteration)
            
            print(f"   📊 Quality Score: {critique_result['quality_score']}/10")
            print(f"   🔍 Issues Found: {len(critique_result['issues'])}")
            
            if critique_result['issues']:
                print("   ⚠️  Issues Identified:")
                for issue in critique_result['issues']:
                    print(f"      - {issue}")
            
            # STEP 3: DECISION POINT
            if critique_result['quality_score'] >= 8 and len(critique_result['issues']) == 0:
                print("\n✅ QUALITY THRESHOLD REACHED!")
                print("   Code meets all requirements - Task complete!")
                issues_found = False
            else:
                print(f"\n🔧 FEEDBACK FOR NEXT ITERATION:")
                for suggestion in critique_result['suggestions']:
                    print(f"   → {suggestion}")
                
                current_code = generated_code
                iteration += 1
                
                if iteration <= max_iterations:
                    print(f"\n🔄 Preparing iteration {iteration}...")
                    await asyncio.sleep(0.5)
        
        if iteration > max_iterations:
            print(f"\n⚠️  Reached maximum iterations ({max_iterations})")
            print("   Task completed with current quality level")
    
    async def _simulate_code_generation(self, task: Dict, iteration: int) -> str:
        """Simulate progressive code generation"""
        
        task_id = task['id']
        
        if task_id == 1:  # Fibonacci
            if iteration == 1:
                return """def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)"""
            elif iteration == 2:
                return """def fibonacci(n):
    if n < 0:
        raise ValueError("n must be non-negative")
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)"""
            elif iteration == 3:
                return """def fibonacci(n):
    \"\"\"Calculate nth Fibonacci number with basic recursion.\"\"\"
    if n < 0:
        raise ValueError("n must be non-negative")
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)"""
            else:
                return """def fibonacci(n: int, memo: dict = None) -> int:
    \"\"\"
    Calculate nth Fibonacci number using memoization.
    
    Args:
        n (int): The position in Fibonacci sequence
        memo (dict): Memoization cache
        
    Returns:
        int: The nth Fibonacci number
        
    Raises:
        ValueError: If n is negative
    \"\"\"
    if memo is None:
        memo = {}
    
    if n < 0:
        raise ValueError("n must be non-negative")
    if n <= 1:
        return n
    if n in memo:
        return memo[n]
    
    memo[n] = fibonacci(n-1, memo) + fibonacci(n-2, memo)
    return memo[n]"""
        
        elif task_id == 2:  # Stack
            if iteration == 1:
                return """class Stack:
    def __init__(self):
        self.items = []
    
    def push(self, item):
        self.items.append(item)
    
    def pop(self):
        return self.items.pop()"""
            elif iteration == 2:
                return """class Stack:
    def __init__(self):
        self.items = []
    
    def push(self, item):
        self.items.append(item)
    
    def pop(self):
        if not self.items:
            raise IndexError("pop from empty stack")
        return self.items.pop()
    
    def peek(self):
        if not self.items:
            raise IndexError("peek from empty stack")
        return self.items[-1]"""
            else:
                return """from typing import Any, Optional

class Stack:
    \"\"\"A simple stack implementation using a list.\"\"\"
    
    def __init__(self):
        self._items = []
    
    def push(self, item: Any) -> None:
        \"\"\"Push an item onto the stack.\"\"\"
        self._items.append(item)
    
    def pop(self) -> Any:
        \"\"\"Pop and return the top item from the stack.\"\"\"
        if self.is_empty():
            raise IndexError("pop from empty stack")
        return self._items.pop()
    
    def peek(self) -> Any:
        \"\"\"Return the top item without removing it.\"\"\"
        if self.is_empty():
            raise IndexError("peek from empty stack")
        return self._items[-1]
    
    def is_empty(self) -> bool:
        \"\"\"Check if the stack is empty.\"\"\"
        return len(self._items) == 0
    
    def size(self) -> int:
        \"\"\"Return the number of items in the stack.\"\"\"
        return len(self._items)
    
    def __str__(self) -> str:
        return f"Stack({self._items})"
    
    def __repr__(self) -> str:
        return self.__str__()"""
        
        else:  # List merger
            if iteration == 1:
                return """def merge_lists(list1, list2):
    result = []
    i = j = 0
    while i < len(list1) and j < len(list2):
        if list1[i] <= list2[j]:
            result.append(list1[i])
            i += 1
        else:
            result.append(list2[j])
            j += 1
    result.extend(list1[i:])
    result.extend(list2[j:])
    return result"""
            else:
                return """from typing import List, TypeVar

T = TypeVar('T')

def merge_sorted_lists(list1: List[T], list2: List[T]) -> List[T]:
    \"\"\"
    Merge two sorted lists into one sorted list.
    
    Args:
        list1: First sorted list
        list2: Second sorted list
        
    Returns:
        A new sorted list containing all elements from both input lists
        
    Raises:
        TypeError: If inputs are not lists
    \"\"\"
    if not isinstance(list1, list) or not isinstance(list2, list):
        raise TypeError("Both arguments must be lists")
    
    result = []
    i = j = 0
    
    # Merge while both lists have elements
    while i < len(list1) and j < len(list2):
        if list1[i] <= list2[j]:
            result.append(list1[i])
            i += 1
        else:
            result.append(list2[j])
            j += 1
    
    # Add remaining elements
    result.extend(list1[i:])
    result.extend(list2[j:])
    
    return result"""
    
    async def _simulate_critique_analysis(self, code: str, task: Dict, iteration: int) -> Dict[str, Any]:
        """Simulate critique engine analysis"""
        
        issues = []
        suggestions = []
        quality_score = 5  # Base score
        
        # Check for documentation
        if '"""' not in code:
            issues.append("Missing docstring documentation")
            suggestions.append("Add comprehensive docstring with Args, Returns, and Raises")
            quality_score -= 1
        else:
            quality_score += 1
        
        # Check for type hints
        if '->' not in code or ':' not in code.split('def')[1].split(')')[0]:
            issues.append("Missing type hints")
            suggestions.append("Add type hints for parameters and return values")
            quality_score -= 1
        else:
            quality_score += 1
        
        # Check for error handling
        if 'raise' not in code and task['id'] in [1, 2]:
            issues.append("Missing error handling")
            suggestions.append("Add proper error handling for edge cases")
            quality_score -= 1
        else:
            quality_score += 1
        
        # Task-specific checks
        if task['id'] == 1:  # Fibonacci
            if 'memo' not in code:
                issues.append("Missing memoization optimization")
                suggestions.append("Implement memoization to improve performance")
                quality_score -= 1
            else:
                quality_score += 1
        
        elif task['id'] == 2:  # Stack
            if 'size' not in code:
                issues.append("Missing size tracking method")
                suggestions.append("Add size() method to track stack size")
                quality_score -= 1
            else:
                quality_score += 1
        
        # Ensure score is in valid range
        quality_score = max(1, min(10, quality_score))
        
        return {
            'issues': issues,
            'suggestions': suggestions,
            'quality_score': quality_score
        }


async def main():
    """Run the feedback loop demonstration"""
    
    demo = FeedbackLoopDemo()
    await demo.run_feedback_loop()
    
    print("\n" + "=" * 70)
    print("🎯 FEEDBACK LOOP CONCEPT DEMONSTRATED!")
    print("=" * 70)
    print("✅ Code Generator → Critique Engine → Feedback → Improvement")
    print("✅ Iterative refinement until quality threshold reached")
    print("✅ Sequential task processing with quality gates")
    print("✅ Complete workflow from start to finish")
    print("\nThis is exactly how the real system works with LLMs! 🚀")


if __name__ == "__main__":
    asyncio.run(main())
