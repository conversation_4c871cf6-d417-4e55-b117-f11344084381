"""
Test Feedback Integration - Verify feedback is properly passed to code generator

This test verifies that:
1. Feedback from critique engine is properly formatted
2. Feedback is included in the generation prompt
3. Previous code is included for context
4. The code generator receives all necessary context
"""

import asyncio
import logging
from code_generator.services.prompt_builder import PromptBuilder
from shared.models import GenerationRequest, ProgrammingLanguage

logging.basicConfig(level=logging.INFO)

async def test_feedback_integration():
    """Test that feedback is properly integrated into code generation prompts"""
    
    print("🧪 TESTING FEEDBACK INTEGRATION")
    print("=" * 50)
    
    prompt_builder = PromptBuilder()
    
    # Test 1: First iteration (no feedback)
    print("\n🔍 TEST 1: First Iteration (No Feedback)")
    request1 = GenerationRequest(
        task_id="test_task_1",
        language=ProgrammingLanguage.PYTHON,
        description="Create a simple calculator function",
        requirements=["Add two numbers", "Return the result"],
        iteration=1,
        feedback=None,
        previous_code=None
    )
    
    prompt1 = prompt_builder.build_generation_prompt(request1)
    print(f"✅ Prompt length: {len(prompt1)} characters")
    print(f"✅ Contains 'Iteration': {'Iteration' in prompt1}")
    print(f"✅ Contains 'Feedback': {'Feedback' in prompt1}")
    print(f"✅ Contains 'Previous Code': {'Previous Code' in prompt1}")
    
    # Test 2: Second iteration with feedback
    print("\n🔍 TEST 2: Second Iteration (With Feedback)")
    
    # Simulate feedback from critique engine
    critique_feedback = """Critical Issues:
- Function lacks input validation
- No error handling for non-numeric inputs
- Missing docstring and type hints

Actionable Fixes:
- Add type hints for parameters and return value
- Implement input validation to check if inputs are numbers
- Add comprehensive docstring with examples
- Handle edge cases like None or string inputs"""
    
    previous_code = """def add(a, b):
    return a + b"""
    
    request2 = GenerationRequest(
        task_id="test_task_1",
        language=ProgrammingLanguage.PYTHON,
        description="Create a simple calculator function",
        requirements=["Add two numbers", "Return the result", "Include proper error handling"],
        iteration=2,
        feedback=critique_feedback,
        previous_code=previous_code
    )
    
    prompt2 = prompt_builder.build_generation_prompt(request2)
    print(f"✅ Prompt length: {len(prompt2)} characters")
    print(f"✅ Contains 'Iteration': {'Iteration' in prompt2}")
    print(f"✅ Contains 'Feedback': {'Feedback' in prompt2}")
    print(f"✅ Contains 'Previous Code': {'Previous Code' in prompt2}")
    print(f"✅ Contains 'Critical Issues': {'Critical Issues' in prompt2}")
    print(f"✅ Contains 'Actionable Fixes': {'Actionable Fixes' in prompt2}")
    print(f"✅ Contains 'type hints': {'type hints' in prompt2}")
    print(f"✅ Contains 'IMPORTANT': {'IMPORTANT' in prompt2}")
    
    # Test 3: Show actual prompt sections
    print("\n📋 PROMPT STRUCTURE ANALYSIS:")
    sections = [
        "# Code Generation Task",
        "## Task Description", 
        "## Requirements",
        "## Previous Code",
        "## Feedback from Previous Iteration",
        "## Iteration",
        "## Output Instructions"
    ]
    
    for section in sections:
        if section in prompt2:
            print(f"   ✅ {section}")
        else:
            print(f"   ❌ {section}")
    
    # Test 4: Extract and show feedback section
    print("\n📝 FEEDBACK SECTION CONTENT:")
    if "## Feedback from Previous Iteration" in prompt2:
        start = prompt2.find("## Feedback from Previous Iteration")
        end = prompt2.find("## Iteration", start)
        if end == -1:
            end = prompt2.find("## Output Instructions", start)
        
        feedback_section = prompt2[start:end].strip()
        print(feedback_section)
    else:
        print("❌ No feedback section found!")
    
    # Test 5: Verify feedback formatting
    print("\n🔍 TEST 3: Feedback Formatting Verification")
    
    # Test the orchestrator's feedback formatting
    from task_manager.services.orchestrator import TaskOrchestrator
    
    # Mock quality history with critique data
    quality_history = [{
        "iteration": 1,
        "quality_score": 6.5,
        "critical_issues": [
            "Function lacks input validation",
            "No error handling for non-numeric inputs", 
            "Missing docstring and type hints"
        ],
        "critique": {
            "critical_issues": [
                "Function lacks input validation",
                "No error handling for non-numeric inputs",
                "Missing docstring and type hints"
            ],
            "actionable_fixes": [
                "Add type hints for parameters and return value",
                "Implement input validation to check if inputs are numbers", 
                "Add comprehensive docstring with examples",
                "Handle edge cases like None or string inputs"
            ]
        }
    }]
    
    # Test feedback formatting logic from orchestrator
    last_critique = quality_history[-1]["critique"]
    feedback_parts = []

    if last_critique.get("critical_issues"):
        feedback_parts.append("Critical Issues:")
        for issue in last_critique["critical_issues"]:
            feedback_parts.append(f"- {issue}")

    if last_critique.get("actionable_fixes"):
        feedback_parts.append("Actionable Fixes:")
        for fix in last_critique["actionable_fixes"]:
            feedback_parts.append(f"- {fix}")

    formatted_feedback = "\n".join(feedback_parts) if feedback_parts else None
    
    print(f"✅ Formatted feedback length: {len(formatted_feedback) if formatted_feedback else 0}")
    print(f"✅ Contains critical issues: {'Critical Issues:' in formatted_feedback if formatted_feedback else False}")
    print(f"✅ Contains actionable fixes: {'Actionable Fixes:' in formatted_feedback if formatted_feedback else False}")
    
    print("\n📝 FORMATTED FEEDBACK:")
    print(formatted_feedback)
    
    print("\n🎉 FEEDBACK INTEGRATION TEST COMPLETED!")
    print("The feedback loop now properly passes context to the code generator!")

if __name__ == "__main__":
    asyncio.run(test_feedback_integration())
