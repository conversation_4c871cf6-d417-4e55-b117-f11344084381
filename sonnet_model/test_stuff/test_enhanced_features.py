"""
Test Enhanced Features: Dynamic Conversation Management and Cloud LLM Support

Tests the new features that address user concerns:
1. Dynamic conversation length management based on LLM signals
2. Cloud LLM provider support (OpenAI, Anthropic, etc.)
3. Intelligent context preservation during conversation resets
"""

import asyncio
import pytest
import os
from unittest.mock import Mock, patch, AsyncMock

from shared.conversation_manager import EnhancedConversationManager, ConversationLengthMode
from code_generator.llm_interface import create_llm_interface, CloudLLMInterfaceWrapper


class TestEnhancedConversationManagement:
    """Test enhanced conversation management features"""
    
    @pytest.fixture
    def conversation_config(self):
        """Configuration for conversation manager"""
        return {
            "max_conversation_length_mode": "signal_based",
            "max_conversation_length_fixed": 50,
            "max_conversation_length_dynamic_min": 20,
            "max_conversation_length_dynamic_max": 80,
            "reset_on_signals": True,
            "conversation_reset_signals": [
                "conversation is getting long",
                "context is full",
                "running out of space",
                "message limit",
                "too many messages"
            ],
            "context_preservation": {
                "enabled": True,
                "method": "intelligent_summary",
                "summary_max_tokens": 1500,
                "preserve_recent_exchanges": 5,
                "preserve_critical_info": True
            }
        }
    
    @pytest.fixture
    def conversation_manager(self, conversation_config):
        """Create conversation manager instance"""
        return EnhancedConversationManager(conversation_config)
    
    def test_dynamic_conversation_length_modes(self, conversation_manager):
        """Test different conversation length management modes"""
        
        # Test mode setting
        assert conversation_manager.length_mode == ConversationLengthMode.SIGNAL_BASED
        
        # Test configuration values
        assert conversation_manager.dynamic_min == 20
        assert conversation_manager.dynamic_max == 80
        assert conversation_manager.reset_on_signals == True
        
        print("✅ Conversation length modes configured correctly")
    
    def test_signal_based_reset_detection(self, conversation_manager):
        """Test LLM signal detection for conversation reset"""
        
        # Test various LLM signals that should trigger reset
        test_signals = [
            "The conversation is getting long, should I continue?",
            "I think the context is full now",
            "We're running out of space in this conversation",
            "Approaching message limit",
            "Too many messages in this thread"
        ]
        
        for signal in test_signals:
            should_reset, reason = conversation_manager.should_reset_conversation("test_session", signal)
            assert should_reset, f"Should reset on signal: {signal}"
            assert "signal detected" in reason.lower(), f"Reason should mention signal detection: {reason}"
        
        print("✅ LLM signal detection working correctly")
    
    def test_dynamic_conversation_assessment(self, conversation_manager):
        """Test dynamic conversation length assessment"""
        
        session_id = "dynamic_test"
        
        # Add exchanges below minimum threshold
        for i in range(15):
            conversation_manager.add_exchange(session_id, "user", f"Message {i}", tokens_used=50)
        
        # Should not reset below minimum
        should_reset, reason = conversation_manager.should_reset_conversation(session_id)
        assert not should_reset, "Should not reset below minimum threshold"
        
        # Add more exchanges to reach dynamic assessment range
        for i in range(15, 35):
            conversation_manager.add_exchange(session_id, "assistant", f"Response {i}", tokens_used=100)
        
        # Should consider reset based on complexity
        should_reset, reason = conversation_manager.should_reset_conversation(session_id)
        # May or may not reset depending on complexity assessment
        
        print("✅ Dynamic conversation assessment working")
    
    def test_context_preservation_during_reset(self, conversation_manager):
        """Test context preservation during conversation reset"""
        
        session_id = "preservation_test"
        
        # Add meaningful exchanges
        conversation_manager.add_exchange(session_id, "user", "I need to implement a REST API", tokens_used=20)
        conversation_manager.add_exchange(session_id, "assistant", "I'll help you create a FastAPI application", tokens_used=30)
        conversation_manager.add_exchange(session_id, "user", "Add authentication and database integration", tokens_used=25)
        conversation_manager.add_exchange(session_id, "assistant", "Implementing JWT auth and SQLAlchemy models", tokens_used=40)
        
        # Trigger reset
        reset_info = conversation_manager.reset_conversation(session_id, "Test reset")
        
        # Verify context preservation
        assert reset_info["preserved_context"]["summary"], "Summary should be generated"
        assert reset_info["preserved_context"]["recent_exchanges"], "Recent exchanges should be preserved"
        assert reset_info["preserved_context"]["critical_info"], "Critical info should be extracted"
        
        # Verify conversation state reset
        conversation = conversation_manager.get_conversation(session_id)
        assert len(conversation.exchanges) == 0, "Exchanges should be cleared"
        assert conversation.reset_count == 1, "Reset count should be incremented"
        assert conversation.context_summary, "Context summary should be stored"
        
        print("✅ Context preservation during reset working correctly")
    
    def test_conversation_statistics(self, conversation_manager):
        """Test conversation statistics and monitoring"""
        
        session_id = "stats_test"
        
        # Add some exchanges
        for i in range(10):
            conversation_manager.add_exchange(session_id, "user", f"Question {i}", tokens_used=20)
            conversation_manager.add_exchange(session_id, "assistant", f"Answer {i}", tokens_used=50)
        
        # Get statistics
        stats = conversation_manager.get_conversation_stats(session_id)
        
        assert stats["session_id"] == session_id
        assert stats["exchange_count"] == 20
        assert stats["total_tokens"] == 700  # (20 + 50) * 10
        assert stats["length_mode"] == "signal_based"
        
        print("✅ Conversation statistics working correctly")


class TestCloudLLMSupport:
    """Test cloud LLM provider support"""
    
    def test_openai_configuration(self):
        """Test OpenAI provider configuration"""
        
        config = {
            "type": "openai",
            "api_key": "test-key",
            "model": "gpt-4",
            "temperature": 0.2,
            "max_tokens": 4096
        }
        
        # Test interface creation
        interface = create_llm_interface(config)
        assert isinstance(interface, CloudLLMInterfaceWrapper)
        
        print("✅ OpenAI configuration working")
    
    def test_anthropic_configuration(self):
        """Test Anthropic provider configuration"""
        
        config = {
            "type": "anthropic",
            "api_key": "test-key",
            "model": "claude-3-sonnet-20240229",
            "temperature": 0.2,
            "max_tokens": 4096
        }
        
        # Test interface creation
        interface = create_llm_interface(config)
        assert isinstance(interface, CloudLLMInterfaceWrapper)
        
        print("✅ Anthropic configuration working")
    
    def test_azure_openai_configuration(self):
        """Test Azure OpenAI provider configuration"""
        
        config = {
            "type": "azure_openai",
            "api_key": "test-key",
            "api_base": "https://test.openai.azure.com",
            "deployment_name": "test-deployment",
            "api_version": "2023-12-01-preview",
            "temperature": 0.2,
            "max_tokens": 4096
        }
        
        # Test interface creation
        interface = create_llm_interface(config)
        assert isinstance(interface, CloudLLMInterfaceWrapper)
        
        print("✅ Azure OpenAI configuration working")
    
    @patch.dict(os.environ, {
        "OPENAI_API_KEY": "test-openai-key",
        "ANTHROPIC_API_KEY": "test-anthropic-key",
        "AZURE_OPENAI_API_KEY": "test-azure-key"
    })
    def test_environment_variable_support(self):
        """Test environment variable support for API keys"""
        
        # Test OpenAI with env var
        config = {"type": "openai", "model": "gpt-4"}
        interface = create_llm_interface(config)
        assert isinstance(interface, CloudLLMInterfaceWrapper)
        
        # Test Anthropic with env var
        config = {"type": "anthropic", "model": "claude-3-sonnet-20240229"}
        interface = create_llm_interface(config)
        assert isinstance(interface, CloudLLMInterfaceWrapper)
        
        print("✅ Environment variable support working")
    
    def test_unsupported_provider_error(self):
        """Test error handling for unsupported providers"""
        
        config = {
            "type": "unsupported_provider",
            "model": "some-model"
        }
        
        with pytest.raises(ValueError) as exc_info:
            create_llm_interface(config)
        
        assert "Unsupported LLM type" in str(exc_info.value)
        assert "unsupported_provider" in str(exc_info.value)
        
        print("✅ Unsupported provider error handling working")


class TestIntegratedFeatures:
    """Test integration of enhanced features"""
    
    def test_conversation_management_with_cloud_llm(self):
        """Test conversation management integrated with cloud LLM"""
        
        # Configuration combining both features
        config = {
            "llm": {
                "type": "openai",
                "api_key": "test-key",
                "model": "gpt-4"
            },
            "conversation_management": {
                "max_conversation_length_mode": "signal_based",
                "reset_on_signals": True,
                "context_preservation": {"enabled": True}
            }
        }
        
        # Test that both components can be initialized together
        conversation_manager = EnhancedConversationManager(config["conversation_management"])
        llm_interface = create_llm_interface(config["llm"])
        
        assert conversation_manager.length_mode == ConversationLengthMode.SIGNAL_BASED
        assert isinstance(llm_interface, CloudLLMInterfaceWrapper)
        
        print("✅ Integrated features working together")
    
    def test_configuration_flexibility(self):
        """Test configuration flexibility and backward compatibility"""
        
        # Test legacy fixed mode
        legacy_config = {
            "max_conversation_length_mode": "fixed",
            "max_conversation_length_fixed": 50
        }
        
        manager = EnhancedConversationManager(legacy_config)
        assert manager.length_mode == ConversationLengthMode.FIXED
        assert manager.fixed_length == 50
        
        # Test new dynamic mode
        dynamic_config = {
            "max_conversation_length_mode": "dynamic",
            "max_conversation_length_dynamic_min": 30,
            "max_conversation_length_dynamic_max": 100
        }
        
        manager = EnhancedConversationManager(dynamic_config)
        assert manager.length_mode == ConversationLengthMode.DYNAMIC
        assert manager.dynamic_min == 30
        assert manager.dynamic_max == 100
        
        print("✅ Configuration flexibility and backward compatibility working")


async def run_enhanced_features_tests():
    """Run all enhanced features tests"""
    print("🚀 Testing Enhanced Features...")
    print("=" * 60)
    
    # Test conversation management
    print("\n📝 Testing Enhanced Conversation Management...")
    conv_test = TestEnhancedConversationManagement()
    
    # Create fixtures
    conversation_config = {
        "max_conversation_length_mode": "signal_based",
        "max_conversation_length_dynamic_min": 20,
        "max_conversation_length_dynamic_max": 80,
        "reset_on_signals": True,
        "conversation_reset_signals": [
            "conversation is getting long",
            "context is full",
            "running out of space"
        ],
        "context_preservation": {
            "enabled": True,
            "method": "intelligent_summary",
            "preserve_recent_exchanges": 5
        }
    }
    
    conversation_manager = EnhancedConversationManager(conversation_config)
    
    # Run conversation tests
    conv_test.test_dynamic_conversation_length_modes(conversation_manager)
    conv_test.test_signal_based_reset_detection(conversation_manager)
    conv_test.test_dynamic_conversation_assessment(conversation_manager)
    conv_test.test_context_preservation_during_reset(conversation_manager)
    conv_test.test_conversation_statistics(conversation_manager)
    
    # Test cloud LLM support
    print("\n☁️ Testing Cloud LLM Support...")
    cloud_test = TestCloudLLMSupport()
    
    cloud_test.test_openai_configuration()
    cloud_test.test_anthropic_configuration()
    cloud_test.test_azure_openai_configuration()
    cloud_test.test_environment_variable_support()
    cloud_test.test_unsupported_provider_error()
    
    # Test integrated features
    print("\n🔗 Testing Integrated Features...")
    integrated_test = TestIntegratedFeatures()
    
    integrated_test.test_conversation_management_with_cloud_llm()
    integrated_test.test_configuration_flexibility()
    
    print("\n" + "=" * 60)
    print("🎉 ALL ENHANCED FEATURES TESTS PASSED!")
    print("✅ Dynamic conversation management working")
    print("✅ Cloud LLM provider support working")
    print("✅ Context preservation working")
    print("✅ Signal-based conversation reset working")
    print("✅ Multi-provider LLM support working")
    print("✅ Environment variable configuration working")
    
    return True


if __name__ == "__main__":
    success = asyncio.run(run_enhanced_features_tests())
    exit(0 if success else 1)
