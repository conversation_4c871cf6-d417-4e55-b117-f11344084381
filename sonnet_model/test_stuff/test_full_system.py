#!/usr/bin/env python3
"""
Test Full System Integration
Test the complete system: CodeGenerator + CritiqueEngine + Ollama
"""
import asyncio
import sys
import os

# Add the sonnet_model directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from code_generator.services.code_generator import CodeGenerator
from critique_engine.services.critique_engine import CritiqueEngine
from shared.models import GenerationRequest, CritiqueRequest, ProgrammingLanguage
from utils.config_loader import load_config


async def test_full_system():
    """Test the complete system integration"""
    print("🚀 Testing Full System Integration")
    print("=" * 60)
    
    # Load configuration
    try:
        config = load_config()
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return False
    
    # Initialize components
    try:
        code_gen_config = config.get("code_generator", {})
        code_generator = CodeGenerator(code_gen_config)
        await code_generator.initialize()
        print("✅ CodeGenerator initialized successfully")
        
        critique_config = config.get("critique_engine", {})
        critique_engine = CritiqueEngine(critique_config)
        print("✅ CritiqueEngine initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize components: {e}")
        return False
    
    # Test 1: Generate code
    print("\n📝 Step 1: Generating code with Ollama...")
    generation_request = GenerationRequest(
        task_id="full-test-001",
        language=ProgrammingLanguage.PYTHON,
        description="Create a Python function that calculates the Fibonacci sequence",
        requirements=[
            "Function should be named 'fibonacci'",
            "Should take a number n as parameter",
            "Should return the nth Fibonacci number",
            "Should handle edge cases (n=0, n=1)",
            "Should include proper docstring and type hints"
        ]
    )
    
    try:
        generation_result = await code_generator.generate_code(generation_request)
        
        if generation_result.success:
            print("✅ Code generation successful!")
            print(f"📊 Model used: {generation_result.model_used}")
            print(f"⏱️  Generation time: {generation_result.generation_time:.2f}s")
            print(f"🔢 Tokens used: {generation_result.tokens_used}")
            print("\n📄 Generated Code:")
            print("=" * 50)
            print(generation_result.code)
            print("=" * 50)
        else:
            print(f"❌ Code generation failed: {generation_result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during code generation: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 2: Critique the generated code
    print("\n🔍 Step 2: Critiquing generated code...")
    critique_request = CritiqueRequest(
        task_id="full-test-001",
        code=generation_result.code,
        language=ProgrammingLanguage.PYTHON,
        categories=set()  # Check all categories
    )
    
    try:
        critique_result = await critique_engine.critique_code(critique_request)
        
        print("✅ Code critique successful!")
        print(f"📊 Quality score: {critique_result.quality_score:.2f}")
        print(f"✅ Meets threshold: {critique_result.meets_threshold}")
        print(f"🔍 Found {len(critique_result.issues)} issues")
        print(f"⏱️  Analysis time: {critique_result.analysis_time:.2f}s")
        
        if critique_result.issues:
            print("\n📋 Issues found:")
            for i, issue in enumerate(critique_result.issues[:5], 1):  # Show first 5 issues
                print(f"  {i}. {issue.title} ({issue.severity.value})")
                print(f"     {issue.description}")
                print(f"     Location: Line {issue.line_start}")
        
        if critique_result.suggestions:
            print("\n💡 Suggestions:")
            for i, suggestion in enumerate(critique_result.suggestions, 1):
                print(f"  {i}. {suggestion}")
                
    except Exception as e:
        print(f"❌ Exception during code critique: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 3: System integration summary
    print("\n📊 System Integration Summary:")
    print("=" * 60)
    print(f"✅ Code Generation: SUCCESS")
    print(f"✅ Code Critique: SUCCESS")
    print(f"📈 Overall Quality: {critique_result.quality_score:.2f}")
    print(f"🎯 System Status: FULLY OPERATIONAL")
    
    # Cleanup
    try:
        await code_generator.shutdown()
        print("✅ Components shutdown successfully")
    except:
        pass
    
    return True


async def main():
    """Main test function"""
    print("🧪 Full System Integration Test")
    print("Testing: Ollama + CodeGenerator + CritiqueEngine")
    print("=" * 60)
    
    success = await test_full_system()
    
    if success:
        print("\n🎉 ALL TESTS PASSED! System is fully operational.")
        print("🚀 Ready for production use with local LLM (Ollama)")
        return 0
    else:
        print("\n⚠️  Some tests failed. Check the logs above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
