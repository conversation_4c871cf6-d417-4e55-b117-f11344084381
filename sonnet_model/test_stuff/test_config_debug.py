#!/usr/bin/env python3
"""
Debug script to test configuration loading and LLM interface creation
"""

import asyncio
import yaml
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)

async def test_config_loading():
    """Test configuration loading and LLM interface creation"""
    
    print("🔍 CONFIGURATION DEBUG")
    print("=" * 50)
    
    # Load config
    config_path = Path("config/config.yaml")
    print(f"📁 Loading config from: {config_path}")
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    print(f"✅ Config loaded successfully")
    
    # Check code_generator section
    code_gen_config = config.get("code_generator", {})
    print(f"\n📋 Code Generator Config:")
    print(f"   Keys: {list(code_gen_config.keys())}")
    
    # Check LLM section
    llm_config = code_gen_config.get("llm", {})
    print(f"\n🤖 LLM Config:")
    print(f"   Type: {llm_config.get('type')}")
    print(f"   API URL: {llm_config.get('api_url')}")
    print(f"   Model: {llm_config.get('model')}")
    print(f"   Temperature: {llm_config.get('temperature')}")
    print(f"   Max Tokens: {llm_config.get('max_tokens')}")
    
    # Test LLM interface creation
    print(f"\n🔧 Testing LLM Interface Creation:")
    
    try:
        from code_generator.llm_interface import create_llm_interface
        
        llm_interface = create_llm_interface(llm_config)
        print(f"✅ LLM Interface created: {type(llm_interface).__name__}")
        
        # Test initialization
        await llm_interface.initialize()
        print(f"✅ LLM Interface initialized")
        
        # Test generation
        print(f"\n🚀 Testing code generation:")
        result = await llm_interface.generate(
            prompt="Write a simple Python function that adds two numbers:",
            max_tokens=100,
            temperature=0.2
        )
        
        print(f"📊 Generation Result:")
        print(f"   Success: {result.get('success')}")
        print(f"   Model: {result.get('model')}")
        print(f"   Tokens: {result.get('tokens_used')}")
        if result.get('success'):
            print(f"   Text: {result.get('text', '')[:100]}...")
        else:
            print(f"   Error: {result.get('error')}")
        
        await llm_interface.shutdown()
        print(f"✅ LLM Interface shutdown")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_config_loading())
