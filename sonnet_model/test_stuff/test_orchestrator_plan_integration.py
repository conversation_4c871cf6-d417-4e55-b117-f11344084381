"""
Test Orchestrator Plan Integration

This test simulates what happens when the API calls the orchestrator
to verify that our TaskPlannerLLM integration is working.
"""

import asyncio
import logging
import os
import json
from task_manager.services.orchestrator import TaskOrchestrator

logging.basicConfig(level=logging.INFO)

async def test_orchestrator_plan_integration():
    """Test that the orchestrator creates plans using TaskPlannerLLM"""
    
    print("🧪 TESTING ORCHESTRATOR PLAN INTEGRATION")
    print("=" * 60)
    
    # Create orchestrator with minimal config (no Redis needed)
    config = {
        "llm": {
            "provider": "ollama",
            "model": "deepseek-coder-v2:16b",
            "base_url": "http://localhost:11434"
        },
        "task_manager": {
            "max_concurrent_tasks": 3,
            "task_timeout_seconds": 300
        },
        "coaching_enabled": False,  # Disable coaching to avoid complex initialization
        "enable_quality_gates": False,  # Disable quality gates
        "auto_recovery": False  # Disable auto recovery
    }
    
    # Create a minimal orchestrator that doesn't need full initialization
    orchestrator = TaskOrchestrator(config)
    
    # Initialize only the state manager (skip others that need complex setup)
    await orchestrator.state_manager.initialize()
    
    try:
        # Test the request routing logic directly
        test_requests = [
            "create a simple calculator application",
            "create a web scraper tool",
            "create a todo list app"
        ]
        
        for i, request in enumerate(test_requests):
            print(f"\n📝 Test {i+1}: {request}")
            print("-" * 50)
            
            # Test the routing logic
            user_input_lower = request.lower()
            
            # Check if it would route to plan creation
            if "create" in user_input_lower:
                print("✅ Request would route to plan creation")
                
                # Test the plan creation method directly
                try:
                    result = await orchestrator._handle_creation_request(request, {})
                    
                    print(f"📋 Result type: {result.get('type')}")
                    
                    if result.get("type") == "plan_created":
                        plan = result.get("plan", {})
                        print(f"📄 Plan name: {plan.get('name', 'Unknown')}")
                        print(f"🎯 Goals: {len(plan.get('goals', []))} goals")
                        print(f"📋 Requirements: {len(plan.get('requirements', []))} requirements")
                        print(f"🔧 Steps: {len(plan.get('steps', []))} steps")
                        
                        # Check if plan file was created
                        plan_file = result.get('plan_file_path')
                        if plan_file and os.path.exists(plan_file):
                            print(f"📁 Plan file created: {plan_file}")
                        else:
                            print(f"❌ Plan file not found: {plan_file}")
                    else:
                        print(f"❌ Unexpected result type: {result.get('type')}")
                        
                except Exception as e:
                    print(f"❌ Plan creation failed: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                print("❌ Request would NOT route to plan creation")
        
        # Check generated plans directory
        if os.path.exists("generated_plans"):
            plan_files = [f for f in os.listdir("generated_plans") if f.endswith('.json')]
            print(f"\n📁 Total plan files created: {len(plan_files)}")
            for file in plan_files:
                print(f"   - {file}")
        else:
            print("\n❌ No generated_plans directory found")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        await orchestrator.state_manager.shutdown()

if __name__ == "__main__":
    asyncio.run(test_orchestrator_plan_integration())
