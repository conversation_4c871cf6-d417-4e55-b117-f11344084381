"""
Test Plan Parser Integration with Orchestrator

This test verifies that the orchestrator now properly uses the plan parser
instead of hardcoded plan extraction.
"""

import asyncio
import logging
from task_manager.services.orchestrator import TaskOrchestrator

logging.basicConfig(level=logging.INFO)

async def test_plan_parser_integration():
    """Test that the orchestrator uses the real plan parser"""
    
    print("🧪 TESTING PLAN PARSER INTEGRATION")
    print("=" * 50)
    
    # Create orchestrator with minimal config
    config = {
        "llm": {
            "provider": "ollama",
            "model": "deepseek-coder-v2:16b",
            "base_url": "http://localhost:11434"
        },
        "task_manager": {
            "max_concurrent_tasks": 3,
            "task_timeout_seconds": 300
        }
    }
    
    orchestrator = TaskOrchestrator(config)
    
    # Test user input that should be parsed
    user_input = """
    Create a Python web application using FastAPI framework.
    
    Requirements:
    - User authentication system
    - REST API endpoints
    - Database integration with SQLAlchemy
    - Unit tests with pytest
    
    Tasks:
    1. Set up FastAPI project structure
    2. Implement user authentication
    3. Create API endpoints for CRUD operations
    4. Add database models and migrations
    5. Write comprehensive tests
    """
    
    print(f"📝 Input: {user_input[:100]}...")
    print()
    
    # Test plan extraction
    print("🔍 Testing Plan Extraction...")
    plan_data = await orchestrator._extract_plan_data(user_input)
    
    print(f"✅ Plan Title: {plan_data['title']}")
    print(f"✅ Language: {plan_data.get('language', 'Not detected')}")
    print(f"✅ Frameworks: {plan_data.get('frameworks', [])}")
    print(f"✅ Requirements: {len(plan_data.get('requirements', []))} found")
    print(f"✅ Steps: {len(plan_data.get('steps', []))} extracted")
    
    print("\n📋 Extracted Steps:")
    for i, step in enumerate(plan_data.get('steps', []), 1):
        print(f"   {i}. {step.get('title', 'Untitled')}")
        print(f"      Priority: {step.get('priority', 'unknown')}")
        print(f"      Duration: {step.get('estimated_duration', 'unknown')} min")
    
    # Test task extraction
    print("\n🔍 Testing Task Extraction...")
    task_input = "Create a user registration endpoint with email validation using FastAPI"
    task_data = await orchestrator._extract_task_data(task_input)
    
    print(f"✅ Task Title: {task_data['title']}")
    print(f"✅ Priority: {task_data.get('priority', 'unknown')}")
    print(f"✅ Language: {task_data.get('language', 'Not detected')}")
    print(f"✅ Frameworks: {task_data.get('frameworks', [])}")
    
    print("\n🎉 PLAN PARSER INTEGRATION TEST COMPLETED!")
    print("The orchestrator now uses the sophisticated plan parser!")

if __name__ == "__main__":
    asyncio.run(test_plan_parser_integration())
