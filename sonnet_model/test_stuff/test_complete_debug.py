"""
Simple Complete Debug Test
Run a Python task and see the complete flow with file outputs
"""

import asyncio
import json
import os
from datetime import datetime
from pathlib import Path

from task_manager.services.orchestrator import TaskOrchestrator

async def test_complete_debug():
    """Test the complete flow with a Python task"""
    
    print("🔍 COMPLETE DEBUG TEST")
    print("=" * 50)
    
    # Create debug directory
    debug_dir = Path(f"debug_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    debug_dir.mkdir(exist_ok=True)
    
    print(f"📁 Debug directory: {debug_dir}")
    print()
    
    # Configuration
    config = {
        "llm": {
            "provider": "ollama", 
            "model": "deepseek-coder-v2:16b",
            "base_url": "http://localhost:11434"
        },
        "coaching_enabled": True,
        "enable_quality_gates": True,
        "auto_recovery": True,
        "max_iterations": 3,
        "quality_threshold": 0.7
    }
    
    # Save config
    with open(debug_dir / "config.json", 'w') as f:
        json.dump(config, f, indent=2)
    
    # Initialize orchestrator
    orchestrator = TaskOrchestrator(config)
    await orchestrator.state_manager.initialize()
    
    try:
        # Python task - decent complexity
        task = "create a Python web scraper that extracts article titles and URLs from a news website, saves data to CSV, and includes proper error handling"
        
        print(f"🎯 TASK: {task}")
        print()
        
        # Save task
        with open(debug_dir / "task.txt", 'w') as f:
            f.write(task)
        
        # STEP 1: Process the request (this will create the plan)
        print("📋 STEP 1: Creating plan...")
        result = await orchestrator.process_user_request(task, {})
        
        # Save the result
        with open(debug_dir / "step1_plan_creation.json", 'w') as f:
            json.dump(result, f, indent=2, default=str)
        
        print(f"✅ Plan created: {result.get('type')}")
        
        if result.get("type") == "plan_created":
            plan = result.get("plan", {})
            print(f"📋 Plan name: {plan.get('name')}")
            print(f"🔧 Steps: {len(plan.get('steps', []))}")
            print(f"📋 Requirements: {len(plan.get('requirements', []))}")
            print()
            
            # Check if plan file was created
            plan_files = list(Path("generated_plans").glob("plan_*.json"))
            if plan_files:
                latest_plan = max(plan_files, key=lambda p: p.stat().st_mtime)
                print(f"📄 Latest plan file: {latest_plan}")
                
                # Copy plan file to debug directory
                import shutil
                shutil.copy2(latest_plan, debug_dir / "initial_plan.json")
                print(f"📋 Plan copied to debug directory")
                print()
            
            # STEP 2: Start execution
            print("🚀 STEP 2: Starting execution...")
            plan_id = plan.get("id")
            
            if plan_id:
                # Continue processing to start execution
                exec_result = await orchestrator.process_user_request("continue", {})
                
                with open(debug_dir / "step2_execution_start.json", 'w') as f:
                    json.dump(exec_result, f, indent=2, default=str)
                
                print(f"✅ Execution result: {exec_result.get('type')}")
                print()
                
                # STEP 3: Monitor iterations
                print("🔄 STEP 3: Running iterations...")
                
                for i in range(3):  # Max 3 iterations for debug
                    print(f"   🔄 Iteration {i+1}")
                    
                    # Get current state
                    state = await orchestrator.state_manager.get_state_summary()
                    with open(debug_dir / f"iteration_{i+1}_state.json", 'w') as f:
                        json.dump(state, f, indent=2, default=str)
                    
                    # Continue processing
                    iter_result = await orchestrator.process_user_request("continue", {})
                    with open(debug_dir / f"iteration_{i+1}_result.json", 'w') as f:
                        json.dump(iter_result, f, indent=2, default=str)
                    
                    print(f"      📊 Result: {iter_result.get('type')}")
                    
                    # Check for generated code
                    if "code" in iter_result or "generated_code" in iter_result:
                        code_data = {
                            "code": iter_result.get("code", iter_result.get("generated_code")),
                            "language": iter_result.get("language", "python"),
                            "quality_score": iter_result.get("quality_score", 0)
                        }
                        with open(debug_dir / f"iteration_{i+1}_code.json", 'w') as f:
                            json.dump(code_data, f, indent=2, default=str)
                        print(f"      💻 Code generated (quality: {code_data['quality_score']})")
                    
                    # Check for critique feedback
                    if "feedback" in iter_result or "critique" in iter_result:
                        feedback_data = {
                            "feedback": iter_result.get("feedback", iter_result.get("critique")),
                            "suggestions": iter_result.get("suggestions", []),
                            "quality_score": iter_result.get("quality_score", 0)
                        }
                        with open(debug_dir / f"iteration_{i+1}_critique.json", 'w') as f:
                            json.dump(feedback_data, f, indent=2, default=str)
                        print(f"      🔍 Critique provided")
                    
                    # Break if completed
                    if iter_result.get("type") in ["completed", "success"]:
                        print(f"      ✅ Task completed!")
                        break
                
                print()
        
        # STEP 4: Final state
        print("📊 STEP 4: Final state...")
        final_state = await orchestrator.state_manager.get_state_summary()
        with open(debug_dir / "final_state.json", 'w') as f:
            json.dump(final_state, f, indent=2, default=str)
        
        # Check for generated files in the project
        project_files = []
        if os.path.exists("generated_projects"):
            for root, dirs, files in os.walk("generated_projects"):
                for file in files:
                    project_files.append(os.path.join(root, file))
        
        if project_files:
            print(f"📁 Generated project files: {len(project_files)}")
            with open(debug_dir / "generated_files.json", 'w') as f:
                json.dump({"files": project_files}, f, indent=2)
        
        # Create summary
        summary = {
            "task": task,
            "debug_directory": str(debug_dir),
            "timestamp": datetime.now().isoformat(),
            "files_created": [f.name for f in debug_dir.glob("*.json")],
            "project_files": project_files
        }
        
        with open(debug_dir / "summary.json", 'w') as f:
            json.dump(summary, f, indent=2)
        
        print("✅ Debug test completed!")
        print(f"📁 All debug data in: {debug_dir}")
        print(f"📄 Files created: {len(summary['files_created'])}")
        
        # Show what files were created
        print("\n📋 DEBUG FILES CREATED:")
        for file in sorted(debug_dir.glob("*.json")):
            print(f"   📄 {file.name}")
        
        if project_files:
            print(f"\n💻 PROJECT FILES GENERATED:")
            for file in project_files:
                print(f"   📁 {file}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
        # Save error info
        with open(debug_dir / "error.json", 'w') as f:
            json.dump({
                "error": str(e),
                "type": type(e).__name__,
                "traceback": traceback.format_exc()
            }, f, indent=2)
    
    finally:
        await orchestrator.state_manager.shutdown()

if __name__ == "__main__":
    asyncio.run(test_complete_debug())
