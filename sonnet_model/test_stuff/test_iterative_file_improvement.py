"""
Test Iterative File Improvement

This test shows files being improved over multiple iterations:
1. <PERSON><PERSON>s initial file with basic requirements
2. LLM critiques and finds issues
3. <PERSON>M improves the file based on feedback
4. File is updated on disk
5. Process repeats until high quality is achieved

You can see the actual file changing iteration by iteration!
"""

import asyncio
import json
import logging
import os
import shutil
from pathlib import Path
from typing import Dict, Any, List
import httpx

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class IterativeFileImprovement:
    """Test iterative improvement of actual files"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model = "deepseek-coder-v2:16b"
        
        # Project setup
        self.project_name = "improved_calendar"
        self.project_path = Path(self.project_name)
        
        # Single task with progressive requirements
        self.task = {
            "name": "Advanced Event Manager",
            "description": "Create a comprehensive event management system",
            "filename": "event_manager.py",
            "base_requirements": [
                "Event class with basic properties",
                "Add and list events functionality"
            ],
            "progressive_requirements": [
                # Each iteration adds more requirements
                [
                    "Event class with title, date, time",
                    "Basic add and list events"
                ],
                [
                    "Event class with title, date, time, description",
                    "Add, list, and remove events",
                    "Input validation for dates"
                ],
                [
                    "Event class with title, date, time, description, priority",
                    "Add, list, remove, and update events",
                    "Input validation for dates and times",
                    "Event conflict detection"
                ],
                [
                    "Event class with title, date, time, description, priority, attendees",
                    "Full CRUD operations (Create, Read, Update, Delete)",
                    "Comprehensive input validation",
                    "Event conflict detection and resolution",
                    "Save/load events to/from JSON file",
                    "Search and filter functionality"
                ]
            ]
        }
        
    async def run_iterative_improvement(self):
        """Run iterative file improvement test"""
        
        print("🚀 ITERATIVE FILE IMPROVEMENT TEST")
        print("=" * 60)
        print(f"📁 Project: {self.project_name}")
        print(f"📄 File: {self.task['filename']}")
        print("🎯 Watching file improve iteration by iteration")
        print("=" * 60)
        
        # Test Ollama connection
        if not await self._test_ollama_connection():
            print("❌ Cannot connect to Ollama")
            return
        
        print("✅ Connected to Ollama successfully!")
        
        # Setup project folder
        self._setup_project_folder()
        
        file_path = self.project_path / self.task['filename']
        
        # Process through iterations
        for iteration in range(1, len(self.task['progressive_requirements']) + 1):
            print(f"\n📋 ITERATION {iteration}")
            print("=" * 50)
            
            requirements = self.task['progressive_requirements'][iteration - 1]
            
            print(f"📝 Requirements for iteration {iteration}:")
            for i, req in enumerate(requirements, 1):
                print(f"   {i}. {req}")
            
            # Read existing file if it exists
            existing_content = ""
            if file_path.exists():
                with open(file_path, 'r') as f:
                    existing_content = f.read()
                print(f"📄 Current file size: {len(existing_content)} characters")
            
            print(f"\n🔄 PROCESSING ITERATION {iteration}")
            print("-" * 30)
            
            # Generate improved content
            print("🤖 CODE GENERATOR: Improving file...")
            new_content = await self._generate_improved_content(
                self.task, requirements, existing_content, iteration
            )
            
            if not new_content:
                print("❌ Code generation failed")
                break
            
            # Write to file
            with open(file_path, 'w') as f:
                f.write(new_content)
            
            print(f"📄 File updated: {file_path}")
            print(f"📊 New file size: {len(new_content)} characters")
            
            if existing_content:
                growth = len(new_content) - len(existing_content)
                print(f"📈 Size change: {growth:+d} characters")
            
            # Show file preview
            self._show_file_preview(new_content, iteration)
            
            # Critique the file
            print("\n🔍 CRITIQUE ENGINE: Analyzing improvements...")
            critique = await self._critique_file_improvements(new_content, requirements)
            
            if critique:
                print(f"📊 Quality Score: {critique['quality_score']}/10")
                print(f"🔍 Issues Found: {len(critique['issues'])}")
                print(f"✅ Requirements Met: {critique.get('requirements_met', 'N/A')}")
                
                if critique['issues']:
                    print("⚠️  Remaining Issues:")
                    for issue in critique['issues'][:2]:
                        print(f"   - {issue}")
                
                if critique.get('improvements'):
                    print("🎯 Improvements Made:")
                    for improvement in critique['improvements'][:2]:
                        print(f"   + {improvement}")
            
            print(f"\n✅ ITERATION {iteration} COMPLETED")
            
            # Save iteration snapshot
            self._save_iteration_snapshot(new_content, iteration)
            
            # Pause between iterations
            await asyncio.sleep(2)
        
        # Show final comparison
        self._show_final_comparison()
        
        print("\n🎉 ITERATIVE IMPROVEMENT COMPLETED!")
        print(f"📁 Check '{self.project_name}' folder to see the evolution!")
        
    def _setup_project_folder(self):
        """Setup project folder"""
        if self.project_path.exists():
            shutil.rmtree(self.project_path)
        
        self.project_path.mkdir()
        
        # Create snapshots folder
        (self.project_path / "snapshots").mkdir()
        
        print(f"📁 Created project folder: {self.project_path}")
        
    async def _test_ollama_connection(self) -> bool:
        """Test Ollama connection"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:11434/api/tags", timeout=5.0)
                return response.status_code == 200
        except:
            return False
    
    async def _generate_improved_content(self, task: Dict, requirements: List[str], 
                                       existing_content: str, iteration: int) -> str:
        """Generate improved file content"""
        
        if iteration == 1:
            prompt = f"""You are an expert Python developer. Create a Python file for this task:

Task: {task['description']}
Filename: {task['filename']}

Requirements for iteration {iteration}:
{chr(10).join(f"- {req}" for req in requirements)}

Please provide a complete, working Python implementation.

Python code:"""
        else:
            prompt = f"""You are an expert Python developer. Improve this Python file to meet the new requirements:

Task: {task['description']}
Filename: {task['filename']}

New requirements for iteration {iteration}:
{chr(10).join(f"- {req}" for req in requirements)}

Current file content:
```python
{existing_content}
```

Please provide an improved version that meets ALL the new requirements while maintaining existing functionality.

Improved Python code:"""
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.2,
                            "top_p": 0.9,
                            "num_predict": 4096
                        }
                    },
                    timeout=240.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    generated_text = result.get("response", "")
                    return self._extract_code_from_response(generated_text)
                else:
                    return ""
                    
        except Exception as e:
            self.logger.error(f"Content generation failed: {e}")
            return ""
    
    async def _critique_file_improvements(self, content: str, requirements: List[str]) -> Dict[str, Any]:
        """Critique file improvements"""
        
        prompt = f"""You are an expert code reviewer. Analyze this Python file against the requirements:

Requirements:
{chr(10).join(f"- {req}" for req in requirements)}

File content:
```python
{content}
```

Provide analysis in JSON format:
{{
    "quality_score": <number 1-10>,
    "issues": ["issue1", "issue2"],
    "improvements": ["improvement1", "improvement2"],
    "requirements_met": <number of requirements satisfied>,
    "suggestions": ["suggestion1", "suggestion2"]
}}

Analysis:"""
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.1,
                            "top_p": 0.8,
                            "num_predict": 1024
                        }
                    },
                    timeout=120.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    critique_text = result.get("response", "")
                    return self._parse_critique_response(critique_text)
                else:
                    return None
                    
        except Exception as e:
            self.logger.error(f"Critique failed: {e}")
            return None
    
    def _extract_code_from_response(self, response: str) -> str:
        """Extract Python code from LLM response"""
        if "```python" in response:
            start = response.find("```python") + 9
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()
        
        elif "```" in response:
            start = response.find("```") + 3
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()
        
        return response.strip()
    
    def _parse_critique_response(self, response: str) -> Dict[str, Any]:
        """Parse critique JSON response"""
        try:
            start = response.find('{')
            end = response.rfind('}') + 1
            
            if start != -1 and end > start:
                json_str = response[start:end]
                return json.loads(json_str)
        except:
            pass
        
        return {
            "quality_score": 7,
            "issues": ["Analysis incomplete"],
            "improvements": ["Code structure improved"],
            "requirements_met": 3
        }
    
    def _show_file_preview(self, content: str, iteration: int):
        """Show preview of file content"""
        lines = content.split('\n')[:12]
        print(f"\n📝 File Preview (Iteration {iteration}):")
        for i, line in enumerate(lines, 1):
            print(f"   {i:2d}: {line}")
        if len(content.split('\n')) > 12:
            print("   ...: (more lines)")
    
    def _save_iteration_snapshot(self, content: str, iteration: int):
        """Save snapshot of each iteration"""
        snapshot_path = self.project_path / "snapshots" / f"iteration_{iteration}.py"
        with open(snapshot_path, 'w') as f:
            f.write(f"# Iteration {iteration} Snapshot\n")
            f.write(f"# Generated by LLM Feedback Loop\n\n")
            f.write(content)
        
        print(f"💾 Snapshot saved: {snapshot_path}")
    
    def _show_final_comparison(self):
        """Show comparison of all iterations"""
        print(f"\n📊 ITERATION COMPARISON:")
        print("=" * 50)
        
        snapshots_dir = self.project_path / "snapshots"
        if snapshots_dir.exists():
            for snapshot_file in sorted(snapshots_dir.glob("iteration_*.py")):
                size = snapshot_file.stat().st_size
                iteration = snapshot_file.stem.split('_')[1]
                print(f"📄 Iteration {iteration}: {size} bytes")


async def main():
    """Run the iterative file improvement test"""
    
    print("🎯 ITERATIVE FILE IMPROVEMENT TEST")
    print("Watch a single file evolve through multiple iterations")
    print("=" * 60)
    
    tester = IterativeFileImprovement()
    await tester.run_iterative_improvement()


if __name__ == "__main__":
    asyncio.run(main())
