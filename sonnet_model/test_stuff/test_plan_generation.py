"""
Test Plan Generation with TaskPlannerLLM Integration

This test verifies that the orchestrator now properly creates plans using TaskPlannerLLM
and saves them to files for user visibility.
"""

import asyncio
import logging
import os
from task_manager.services.orchestrator import TaskOrchestrator

logging.basicConfig(level=logging.INFO)

async def test_plan_generation():
    """Test that the orchestrator creates and saves plans properly"""
    
    print("🧪 TESTING PLAN GENERATION WITH TaskPlannerLLM")
    print("=" * 60)
    
    # Create orchestrator with minimal config
    config = {
        "llm": {
            "provider": "ollama",
            "model": "deepseek-coder-v2:16b",
            "base_url": "http://localhost:11434"
        },
        "task_manager": {
            "max_concurrent_tasks": 3,
            "task_timeout_seconds": 300
        }
    }
    
    orchestrator = TaskOrchestrator(config)
    
    try:
        # Initialize the orchestrator
        await orchestrator.initialize()
        
        # Test plan creation with a simple request
        test_request = "create a simple calculator application"
        
        print(f"📝 Testing request: {test_request}")
        print("-" * 40)
        
        # Process the request
        result = await orchestrator.process_user_request(test_request)
        
        print(f"✅ Result type: {result.get('type')}")
        print(f"📄 Plan file: {result.get('plan_file_path', 'Not specified')}")
        
        # Check if plan was created
        if result.get("type") == "plan_created":
            plan = result.get("plan", {})
            print(f"📋 Plan name: {plan.get('name', 'Unknown')}")
            print(f"📝 Plan description: {plan.get('description', 'No description')}")
            print(f"🎯 Goals: {len(plan.get('goals', []))} goals")
            print(f"📋 Requirements: {len(plan.get('requirements', []))} requirements")
            print(f"🔧 Steps: {len(plan.get('steps', []))} steps")
            
            # Check if plan file exists
            if os.path.exists("generated_plans"):
                plan_files = os.listdir("generated_plans")
                print(f"📁 Plan files created: {len(plan_files)}")
                for file in plan_files:
                    print(f"   - {file}")
            else:
                print("❌ No generated_plans directory found")
        else:
            print(f"❌ Unexpected result type: {result.get('type')}")
            print(f"📄 Full result: {result}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        await orchestrator.shutdown()

if __name__ == "__main__":
    asyncio.run(test_plan_generation())
