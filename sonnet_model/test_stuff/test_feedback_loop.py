"""
Test the Complete Feedback Loop

Tests the core workflow:
1. Code Generator implements Task 1
2. Critique Engine reviews and gives feedback  
3. Code Generator fixes issues if any
4. Repeat until Task 1 is perfect
5. Move to Task 2 and repeat
6. Continue until all 3 tasks complete
"""

import asyncio
import logging
from typing import List, Dict, Any

# Configure logging to see the feedback loop in action
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

class FeedbackLoopTester:
    """Test the complete feedback loop with 3 tasks"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Define our 3-task test project
        self.test_tasks = [
            {
                "id": "task_1",
                "description": "Create a Python function to calculate factorial of a number with proper error handling and documentation",
                "requirements": [
                    "Handle negative numbers appropriately",
                    "Include comprehensive docstring",
                    "Add type hints",
                    "Handle edge cases (0, 1)"
                ]
            },
            {
                "id": "task_2", 
                "description": "Create a class to manage a simple bank account with deposit, withdraw, and balance operations",
                "requirements": [
                    "Prevent negative balance",
                    "Track transaction history",
                    "Include proper validation",
                    "Add string representation"
                ]
            },
            {
                "id": "task_3",
                "description": "Create a utility function to validate email addresses using regex with comprehensive testing",
                "requirements": [
                    "Use proper regex pattern",
                    "Handle various email formats",
                    "Include test cases",
                    "Return meaningful error messages"
                ]
            }
        ]
        
        self.results = {}
        
    async def test_feedback_loop(self):
        """Test the complete feedback loop for all 3 tasks"""
        
        print("🚀 Starting Feedback Loop Test with 3 Tasks")
        print("=" * 60)
        
        for i, task in enumerate(self.test_tasks, 1):
            print(f"\n📋 TASK {i}: {task['description']}")
            print("-" * 60)
            
            # Process this task through the feedback loop
            task_result = await self._process_task_with_feedback(task, i)
            self.results[task['id']] = task_result
            
            print(f"✅ TASK {i} COMPLETED!")
            print(f"   Iterations: {task_result['iterations']}")
            print(f"   Final Quality: {task_result['final_quality']}")
            
        # Print final summary
        self._print_final_summary()
        
    async def _process_task_with_feedback(self, task: Dict, task_num: int) -> Dict[str, Any]:
        """Process a single task through the feedback loop"""
        
        iteration = 1
        max_iterations = 5  # Prevent infinite loops
        current_code = ""
        
        while iteration <= max_iterations:
            print(f"\n🔄 ITERATION {iteration} for Task {task_num}")
            
            # Step 1: Code Generation
            print("  🤖 Code Generator: Implementing task...")
            generated_code = await self._simulate_code_generation(task, current_code, iteration)
            
            print(f"  📝 Generated {len(generated_code)} characters of code")
            
            # Step 2: Critique Analysis
            print("  🔍 Critique Engine: Analyzing code...")
            critique_result = await self._simulate_critique_analysis(generated_code, task)
            
            print(f"  📊 Found {len(critique_result['issues'])} issues")
            print(f"  ⭐ Quality Score: {critique_result['quality_score']}/10")
            
            # Step 3: Decision - Continue or Move to Next Task?
            if critique_result['quality_score'] >= 8 and len(critique_result['issues']) == 0:
                print("  ✅ Code quality acceptable - Task complete!")
                return {
                    "iterations": iteration,
                    "final_code": generated_code,
                    "final_quality": critique_result['quality_score'],
                    "issues_resolved": iteration - 1
                }
            
            # Step 4: Feedback for Next Iteration
            print("  🔧 Issues found - preparing feedback for next iteration...")
            for issue in critique_result['issues']:
                print(f"    - {issue}")
            
            current_code = generated_code
            iteration += 1
            
            # Small delay to simulate processing time
            await asyncio.sleep(0.5)
        
        # Max iterations reached
        print(f"  ⚠️ Max iterations ({max_iterations}) reached")
        return {
            "iterations": max_iterations,
            "final_code": current_code,
            "final_quality": critique_result['quality_score'],
            "issues_resolved": "incomplete"
        }
    
    async def _simulate_code_generation(self, task: Dict, previous_code: str, iteration: int) -> str:
        """Simulate code generation (improving with each iteration)"""
        
        # Simulate different quality levels based on iteration
        if iteration == 1:
            # First iteration - basic implementation
            if "factorial" in task['description']:
                return '''def factorial(n):
    if n == 0:
        return 1
    return n * factorial(n-1)'''
            
            elif "bank account" in task['description']:
                return '''class BankAccount:
    def __init__(self):
        self.balance = 0
    
    def deposit(self, amount):
        self.balance += amount
    
    def withdraw(self, amount):
        self.balance -= amount'''
            
            elif "email" in task['description']:
                return '''def validate_email(email):
    return "@" in email'''
        
        elif iteration == 2:
            # Second iteration - add error handling
            if "factorial" in task['description']:
                return '''def factorial(n):
    if n < 0:
        raise ValueError("Factorial not defined for negative numbers")
    if n == 0 or n == 1:
        return 1
    return n * factorial(n-1)'''
            
            elif "bank account" in task['description']:
                return '''class BankAccount:
    def __init__(self):
        self.balance = 0
        self.transactions = []
    
    def deposit(self, amount):
        if amount <= 0:
            raise ValueError("Amount must be positive")
        self.balance += amount
        self.transactions.append(f"Deposit: {amount}")
    
    def withdraw(self, amount):
        if amount > self.balance:
            raise ValueError("Insufficient funds")
        self.balance -= amount
        self.transactions.append(f"Withdraw: {amount}")'''
            
            elif "email" in task['description']:
                return '''import re

def validate_email(email):
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None'''
        
        else:
            # Third+ iteration - add documentation and type hints
            if "factorial" in task['description']:
                return '''def factorial(n: int) -> int:
    """
    Calculate the factorial of a non-negative integer.
    
    Args:
        n (int): A non-negative integer
        
    Returns:
        int: The factorial of n
        
    Raises:
        ValueError: If n is negative
        TypeError: If n is not an integer
    """
    if not isinstance(n, int):
        raise TypeError("Input must be an integer")
    if n < 0:
        raise ValueError("Factorial not defined for negative numbers")
    if n == 0 or n == 1:
        return 1
    return n * factorial(n-1)'''
            
            elif "bank account" in task['description']:
                return '''from typing import List

class BankAccount:
    """A simple bank account class with transaction tracking."""
    
    def __init__(self, initial_balance: float = 0.0):
        """Initialize account with optional initial balance."""
        self.balance = initial_balance
        self.transactions: List[str] = []
    
    def deposit(self, amount: float) -> None:
        """Deposit money into the account."""
        if amount <= 0:
            raise ValueError("Amount must be positive")
        self.balance += amount
        self.transactions.append(f"Deposit: ${amount:.2f}")
    
    def withdraw(self, amount: float) -> None:
        """Withdraw money from the account."""
        if amount <= 0:
            raise ValueError("Amount must be positive")
        if amount > self.balance:
            raise ValueError("Insufficient funds")
        self.balance -= amount
        self.transactions.append(f"Withdraw: ${amount:.2f}")
    
    def get_balance(self) -> float:
        """Get current account balance."""
        return self.balance
    
    def __str__(self) -> str:
        return f"BankAccount(balance=${self.balance:.2f})"'''
            
            elif "email" in task['description']:
                return '''import re
from typing import Tuple

def validate_email(email: str) -> Tuple[bool, str]:
    """
    Validate an email address using regex.
    
    Args:
        email (str): Email address to validate
        
    Returns:
        Tuple[bool, str]: (is_valid, error_message)
    """
    if not isinstance(email, str):
        return False, "Email must be a string"
    
    if not email:
        return False, "Email cannot be empty"
    
    # Comprehensive email regex pattern
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    
    if re.match(pattern, email):
        return True, "Valid email"
    else:
        return False, "Invalid email format"

# Test cases
def test_email_validation():
    """Test the email validation function."""
    test_cases = [
        ("<EMAIL>", True),
        ("invalid.email", False),
        ("", False),
        ("user@", False),
        ("@example.com", False)
    ]
    
    for email, expected in test_cases:
        is_valid, message = validate_email(email)
        assert is_valid == expected, f"Failed for {email}: {message}"
    
    print("All email validation tests passed!")'''
        
        return "# Code generation simulation"
    
    async def _simulate_critique_analysis(self, code: str, task: Dict) -> Dict[str, Any]:
        """Simulate critique engine analysis"""
        
        issues = []
        quality_score = 5  # Start with medium quality
        
        # Check for common issues
        if "def " not in code:
            issues.append("No function definition found")
            quality_score -= 2
        
        if ":" not in code and "def " in code:
            issues.append("Missing type hints")
            quality_score -= 1
        
        if '"""' not in code and "def " in code:
            issues.append("Missing docstring")
            quality_score -= 1
        
        if "raise" not in code and ("factorial" in task['description'] or "bank" in task['description']):
            issues.append("Missing error handling")
            quality_score -= 1
        
        if "test" not in code.lower() and "email" in task['description']:
            issues.append("Missing test cases")
            quality_score -= 1
        
        # Bonus points for good practices
        if "typing" in code or "List" in code or "Tuple" in code:
            quality_score += 1
        
        if '"""' in code and len(code.split('"""')) >= 3:
            quality_score += 1
        
        if "isinstance" in code:
            quality_score += 1
        
        # Ensure score is in valid range
        quality_score = max(1, min(10, quality_score))
        
        return {
            "issues": issues,
            "quality_score": quality_score,
            "suggestions": [
                "Add comprehensive error handling",
                "Include detailed documentation", 
                "Add type hints for better code clarity",
                "Consider edge cases in implementation"
            ]
        }
    
    def _print_final_summary(self):
        """Print final summary of all tasks"""
        
        print("\n" + "=" * 60)
        print("🎉 FEEDBACK LOOP TEST COMPLETED!")
        print("=" * 60)
        
        total_iterations = 0
        completed_tasks = 0
        
        for i, (task_id, result) in enumerate(self.results.items(), 1):
            print(f"\n📋 TASK {i} ({task_id}):")
            print(f"   ✅ Iterations: {result['iterations']}")
            print(f"   ⭐ Final Quality: {result['final_quality']}/10")
            print(f"   🔧 Issues Resolved: {result['issues_resolved']}")
            
            total_iterations += result['iterations']
            if result['final_quality'] >= 8:
                completed_tasks += 1
        
        print(f"\n📊 OVERALL STATISTICS:")
        print(f"   📋 Total Tasks: {len(self.test_tasks)}")
        print(f"   ✅ Completed Successfully: {completed_tasks}")
        print(f"   🔄 Total Iterations: {total_iterations}")
        print(f"   📈 Success Rate: {(completed_tasks/len(self.test_tasks)*100):.1f}%")
        
        if completed_tasks == len(self.test_tasks):
            print(f"\n🎉 ALL TASKS COMPLETED SUCCESSFULLY!")
            print("✅ Feedback loop is working perfectly!")
        else:
            print(f"\n⚠️ Some tasks need more work")
            print("🔧 Feedback loop identified areas for improvement")


async def main():
    """Run the feedback loop test"""
    tester = FeedbackLoopTester()
    await tester.test_feedback_loop()


if __name__ == "__main__":
    asyncio.run(main())
