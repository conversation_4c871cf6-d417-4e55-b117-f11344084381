"""
COMPLETE WORKFLOW TEST

This tests the entire workflow from high-level description to working project:
1. High-level description input
2. Automatic task breakdown
3. Real iterative improvement with LLM feedback
4. Complete project generation
5. Verification of results

This is the final test to prove everything works end-to-end!
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any
import httpx

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class CompleteWorkflowTest:
    """Test the complete workflow end-to-end"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    async def test_complete_workflow(self):
        """Test the complete workflow"""
        
        print("🚀 COMPLETE WORKFLOW TEST")
        print("=" * 70)
        print("Testing: High-level description → Task breakdown → Iterative improvement → Working project")
        print("=" * 70)
        
        # Test scenarios
        test_scenarios = [
            {
                "name": "Simple Todo App",
                "description": "Create a todo list application with user authentication",
                "expected_files": ["models.py", "tasks.py", "app.py"]
            },
            {
                "name": "Blog Platform",
                "description": "Create a blog platform with user authentication and post management",
                "expected_files": ["models.py", "auth.py", "blog.py", "app.py"]
            }
        ]
        
        results = []
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n🎯 SCENARIO {i}: {scenario['name']}")
            print(f"📝 Description: {scenario['description']}")
            print("-" * 60)
            
            result = await self._test_scenario(scenario)
            results.append(result)
            
            print(f"{'✅ SUCCESS' if result['success'] else '❌ FAILED'}: {scenario['name']}")
            print(f"📊 Success Rate: {result['success_rate']:.1f}%")
            print(f"🔄 Total Iterations: {result['total_iterations']}")
        
        # Overall summary
        self._print_overall_summary(results)
        
        return results
    
    async def _test_scenario(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """Test a single scenario"""
        
        # Test 1: Task Planning
        print("📋 STEP 1: Testing task planning...")
        task_plan = await self._test_task_planning(scenario['description'])
        
        if not task_plan:
            return {
                "scenario": scenario['name'],
                "success": False,
                "error": "Task planning failed",
                "success_rate": 0,
                "total_iterations": 0
            }
        
        print(f"✅ Task planning successful: {len(task_plan)} tasks created")
        
        # Test 2: Project Generation
        print("🔄 STEP 2: Testing project generation...")
        project_result = await self._test_project_generation(scenario['description'])
        
        if not project_result:
            return {
                "scenario": scenario['name'],
                "success": False,
                "error": "Project generation failed",
                "success_rate": 0,
                "total_iterations": 0
            }
        
        print(f"✅ Project generation successful")
        
        # Test 3: File Verification
        print("📄 STEP 3: Verifying generated files...")
        verification_result = self._verify_generated_files(project_result, scenario['expected_files'])
        
        return {
            "scenario": scenario['name'],
            "success": verification_result['success'],
            "task_plan": task_plan,
            "project_result": project_result,
            "verification": verification_result,
            "success_rate": project_result.get('success_rate', 0),
            "total_iterations": project_result.get('total_iterations', 0)
        }
    
    async def _test_task_planning(self, description: str) -> Dict[str, Any]:
        """Test task planning functionality"""
        
        try:
            from simple_task_planner import SimpleTaskPlanner
            
            planner = SimpleTaskPlanner()
            project = await planner.create_comprehensive_project(description)
            
            if project and project.get('tasks'):
                return project
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Task planning failed: {e}")
            return None
    
    async def _test_project_generation(self, description: str) -> Dict[str, Any]:
        """Test project generation functionality"""
        
        try:
            from working_project_generator import WorkingProjectGenerator
            
            generator = WorkingProjectGenerator()
            result = await generator.create_project_from_description(description)
            
            if result and result.get('success_rate', 0) > 0:
                return result
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Project generation failed: {e}")
            return None
    
    def _verify_generated_files(self, project_result: Dict[str, Any], 
                               expected_files: list) -> Dict[str, Any]:
        """Verify the generated files"""
        
        project_path = Path(project_result.get('project_path', ''))
        
        if not project_path.exists():
            return {
                "success": False,
                "error": "Project directory not found",
                "files_found": [],
                "files_expected": expected_files
            }
        
        # Check for expected files
        files_found = []
        files_missing = []
        
        for expected_file in expected_files:
            file_path = project_path / expected_file
            if file_path.exists():
                files_found.append(expected_file)
                
                # Check file content
                with open(file_path, 'r') as f:
                    content = f.read()
                
                print(f"   📄 {expected_file}: {len(content)} characters")
            else:
                files_missing.append(expected_file)
        
        # Check for additional files
        actual_files = [f.name for f in project_path.iterdir() if f.is_file() and f.suffix == '.py']
        additional_files = [f for f in actual_files if f not in expected_files and f != 'PROJECT_SUMMARY.json']
        
        success = len(files_missing) == 0 and len(files_found) >= len(expected_files) * 0.75
        
        return {
            "success": success,
            "files_found": files_found,
            "files_missing": files_missing,
            "additional_files": additional_files,
            "files_expected": expected_files,
            "coverage": len(files_found) / len(expected_files) * 100 if expected_files else 0
        }
    
    def _print_overall_summary(self, results: list):
        """Print overall test summary"""
        
        print(f"\n📊 OVERALL WORKFLOW TEST SUMMARY")
        print("=" * 60)
        
        total_scenarios = len(results)
        successful_scenarios = sum(1 for r in results if r['success'])
        total_iterations = sum(r['total_iterations'] for r in results)
        avg_success_rate = sum(r['success_rate'] for r in results) / total_scenarios if total_scenarios > 0 else 0
        
        print(f"📋 Total Scenarios: {total_scenarios}")
        print(f"✅ Successful Scenarios: {successful_scenarios}")
        print(f"📈 Overall Success Rate: {(successful_scenarios / total_scenarios * 100):.1f}%")
        print(f"🔄 Total Iterations: {total_iterations}")
        print(f"📊 Average File Success Rate: {avg_success_rate:.1f}%")
        
        print(f"\n📋 SCENARIO DETAILS:")
        for result in results:
            status = "✅ SUCCESS" if result['success'] else "❌ FAILED"
            print(f"   {status}: {result['scenario']}")
            if 'verification' in result:
                verification = result['verification']
                print(f"      Files: {len(verification['files_found'])}/{len(verification['files_expected'])}")
                print(f"      Coverage: {verification['coverage']:.1f}%")
        
        # Final assessment
        if successful_scenarios == total_scenarios:
            print(f"\n🎉 COMPLETE SUCCESS!")
            print("✅ All scenarios passed")
            print("✅ Task planning works")
            print("✅ Project generation works")
            print("✅ Iterative improvement works")
            print("✅ File verification works")
            print("\n🚀 THE COMPLETE WORKFLOW IS FULLY FUNCTIONAL!")
        elif successful_scenarios > 0:
            print(f"\n⚠️ PARTIAL SUCCESS")
            print(f"✅ {successful_scenarios}/{total_scenarios} scenarios passed")
            print("🔧 Some components need refinement")
        else:
            print(f"\n❌ WORKFLOW NEEDS WORK")
            print("🔧 Major issues need to be addressed")
    
    async def test_api_integration(self):
        """Test API integration"""
        
        print(f"\n🌐 TESTING API INTEGRATION")
        print("-" * 40)
        
        # Test if API is running
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:8000/health", timeout=5.0)
                
                if response.status_code == 200:
                    print("✅ API is running")
                    
                    # Test project generation endpoint
                    test_request = {
                        "description": "Create a simple calculator application"
                    }
                    
                    response = await client.post(
                        "http://localhost:8000/api/v1/projects/generate",
                        json=test_request,
                        timeout=300.0
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        print("✅ API project generation works")
                        print(f"📊 Success: {result.get('success', False)}")
                        print(f"📁 Project: {result.get('project_name', 'N/A')}")
                    else:
                        print(f"❌ API project generation failed: {response.status_code}")
                else:
                    print(f"❌ API health check failed: {response.status_code}")
                    
        except Exception as e:
            print(f"⚠️ API not available: {e}")
            print("💡 Start the API with: python -m api.app")


async def main():
    """Run the complete workflow test"""
    
    print("🎯 COMPLETE WORKFLOW TEST")
    print("Testing the entire system from high-level description to working project")
    print("=" * 70)
    
    tester = CompleteWorkflowTest()
    
    # Test the complete workflow
    results = await tester.test_complete_workflow()
    
    # Test API integration
    await tester.test_api_integration()
    
    print(f"\n🎉 COMPLETE WORKFLOW TEST FINISHED!")
    
    # Determine overall success
    successful_scenarios = sum(1 for r in results if r['success'])
    total_scenarios = len(results)
    
    if successful_scenarios == total_scenarios:
        print("🚀 COMPLETE SUCCESS! The entire system works end-to-end!")
        print("✅ High-level description → Task breakdown → Iterative improvement → Working project")
    elif successful_scenarios > 0:
        print(f"⚠️ Partial success: {successful_scenarios}/{total_scenarios} scenarios passed")
    else:
        print("❌ System needs more work")
    
    return results


if __name__ == "__main__":
    asyncio.run(main())
