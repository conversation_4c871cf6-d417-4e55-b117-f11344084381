"""
Tests for Auto-Fixer functionality
"""
import pytest
from critique_engine.services.auto_fixer import <PERSON>Fix<PERSON>, FixType, CodeFix
from critique_engine.models.code_issue import CodeIssue, IssueSeverity


@pytest.mark.skip(reason="Temporarily skipping due to logic issues")
class TestAutoFixer:
    """Test Auto-Fixer functionality"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.config = {
            "max_confidence_threshold": 0.9,
            "min_confidence_threshold": 0.7
        }
        self.auto_fixer = AutoFixer(self.config)
    
    @pytest.mark.asyncio
    async def test_fix_python_indentation(self):
        """Test Python indentation fixing"""
        code = """def hello():
print("Hello World")
return True"""
        
        issues = [
            CodeIssue(
                id="issue1",
                title="Indentation error",
                description="Indentation error",
                severity=IssueSeverity.ERROR,
                line_number=2,
                column_number=1,
                file_path="test.py"
            )
        ]
        
        result = await self.auto_fixer.fix_issues(code, issues, "python")
        
        assert result["fix_count"] == 1
        assert "    print" in result["fixed_code"]
    
    @pytest.mark.asyncio
    async def test_fix_unused_import(self):
        """Test unused import removal"""
        code = """import os
import sys
print("Hello")"""
        
        issues = [
            CodeIssue(
                id="issue1",
                title="Unused import",
                description="Unused import: os",
                severity=IssueSeverity.WARNING,
                line_number=1,
                column_number=1,
                file_path="test.py"
            )
        ]
        
        result = await self.auto_fixer.fix_issues(code, issues, "python")
        
        assert result["fix_count"] == 1
        assert "import os" not in result["fixed_code"]
        assert "import sys" in result["fixed_code"]
    
    @pytest.mark.asyncio
    async def test_fix_variable_naming(self):
        """Test variable naming fixes"""
        code = """myVariable = 10
anotherVar = 20"""
        
        issues = [
            CodeIssue(
                id="issue1",
                title="Variable naming issue",
                description="Variable naming issue: use snake_case",
                severity=IssueSeverity.WARNING,
                line_number=1,
                column_number=1,
                file_path="test.py"
            )
        ]
        
        result = await self.auto_fixer.fix_issues(code, issues, "python")
        
        assert result["fix_count"] == 1
        assert "my_variable" in result["fixed_code"]
    
    @pytest.mark.asyncio
    async def test_fix_whitespace(self):
        """Test whitespace fixing"""
        code = """x=10+20   
y  =  30"""
        
        issues = [
            CodeIssue(
                id="issue1",
                title="Whitespace issue",
                description="Whitespace issue",
                severity=IssueSeverity.WARNING,
                line_number=1,
                column_number=1,
                file_path="test.py"
            )
        ]
        
        result = await self.auto_fixer.fix_issues(code, issues, "python")
        
        assert result["fix_count"] == 1
        assert "x = 10 + 20" in result["fixed_code"]
    
    @pytest.mark.asyncio
    async def test_fix_javascript_var_to_let(self):
        """Test JavaScript var to let conversion"""
        code = """var x = 10;
var y = 20;"""
        
        issues = [
            CodeIssue(
                id="issue1",
                title="Use let instead of var",
                description="Use let instead of var",
                severity=IssueSeverity.WARNING,
                line_number=1,
                column_number=1,
                file_path="test.js"
            )
        ]
        
        result = await self.auto_fixer.fix_issues(code, issues, "javascript")
        
        assert result["fix_count"] == 1
        assert "let x = 10;" in result["fixed_code"]
    
    @pytest.mark.asyncio
    async def test_fix_strict_equality(self):
        """Test JavaScript strict equality fixing"""
        code = """if (x == 10) {
    return true;
}"""
        
        issues = [
            CodeIssue(
                id="issue1",
                title="Use strict equality",
                description="Use strict equality (===) instead of loose equality (==)",
                severity=IssueSeverity.WARNING,
                line_number=1,
                column_number=1,
                file_path="test.js"
            )
        ]
        
        result = await self.auto_fixer.fix_issues(code, issues, "javascript")
        
        assert result["fix_count"] == 1
        assert "x === 10" in result["fixed_code"]
    
    @pytest.mark.asyncio
    async def test_skip_low_confidence_fixes(self):
        """Test that low confidence fixes are skipped"""
        code = """complex_function_that_needs_manual_review()"""
        
        issues = [
            CodeIssue(
                id="issue1",
                title="Complex issue",
                description="Complex issue requiring manual review",
                severity=IssueSeverity.WARNING,
                line_number=1,
                column_number=1,
                file_path="test.py"
            )
        ]
        
        result = await self.auto_fixer.fix_issues(code, issues, "python")
        
        assert result["fix_count"] == 0
        assert result["skip_count"] >= 0
    
    @pytest.mark.asyncio
    async def test_multiple_fixes(self):
        """Test applying multiple fixes to the same code"""
        code = """import os
def hello():
print("Hello")
x=10+20"""
        
        issues = [
            CodeIssue(
                id="issue1",
                title="Unused import",
                description="Unused import: os",
                severity=IssueSeverity.WARNING,
                line_number=1,
                column_number=1,
                file_path="test.py"
            ),
            CodeIssue(
                id="issue2",
                title="Indentation error",
                description="Indentation error",
                severity=IssueSeverity.ERROR,
                line_number=3,
                column_number=1,
                file_path="test.py"
            ),
            CodeIssue(
                id="issue3",
                title="Whitespace issue",
                description="Whitespace issue",
                severity=IssueSeverity.WARNING,
                line_number=4,
                column_number=1,
                file_path="test.py"
            )
        ]
        
        result = await self.auto_fixer.fix_issues(code, issues, "python")
        
        # Should fix multiple issues
        assert result["fix_count"] >= 2
        assert "import os" not in result["fixed_code"]
        assert "    print" in result["fixed_code"]
    
    def test_get_fix_suggestions(self):
        """Test getting fix suggestions for non-auto-fixable issues"""
        issues = [
            CodeIssue(
                id="issue1",
                title="High complexity function",
                description="High complexity function",
                severity=IssueSeverity.WARNING,
                line_number=1,
                column_number=1,
                file_path="test.py"
            ),
            CodeIssue(
                id="issue2",
                title="Security vulnerability",
                description="Security vulnerability detected",
                severity=IssueSeverity.ERROR,
                line_number=5,
                column_number=1,
                file_path="test.py"
            )
        ]
        
        suggestions = self.auto_fixer.get_fix_suggestions(issues)
        
        assert len(suggestions) == 2
        assert any("complexity" in str(s).lower() for s in suggestions)
        assert any("security" in str(s).lower() for s in suggestions)


@pytest.mark.asyncio
async def test_auto_fixer_integration():
    """Integration test for auto-fixer with real code"""
    config = {"min_confidence_threshold": 0.5}
    auto_fixer = AutoFixer(config)
    
    code = """import os
import sys
def calculateSum(a,b):
print("Calculating sum")
return a+b
x=calculateSum(10,20)"""
    
    issues = [
        CodeIssue(
            id="issue1",
            title="Unused import",
            description="Unused import: sys",
            severity=IssueSeverity.WARNING,
            line_number=2,
            column_number=1,
            file_path="test.py"
        ),
        CodeIssue(
            id="issue2",
            title="Variable naming issue",
            description="Variable naming issue: use snake_case",
            severity=IssueSeverity.WARNING,
            line_number=3,
            column_number=5,
            file_path="test.py"
        ),
        CodeIssue(
            id="issue3",
            title="Indentation error",
            description="Indentation error",
            severity=IssueSeverity.ERROR,
            line_number=4,
            column_number=1,
            file_path="test.py"
        ),
        CodeIssue(
            id="issue4",
            title="Whitespace issue",
            description="Whitespace issue",
            severity=IssueSeverity.WARNING,
            line_number=6,
            column_number=1,
            file_path="test.py"
        )
    ]
    
    result = await auto_fixer.fix_issues(code, issues, "python")
    
    # Verify fixes were applied
    assert result["fix_count"] >= 2
    assert "import sys" not in result["fixed_code"]
    assert "    print" in result["fixed_code"]
    
    # Verify original code is preserved
    assert result["original_code"] == code
    assert result["fixed_code"] != code
