"""
Test suite for Task Manager
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from task_manager.services.orchestrator import TaskOrchestrator
from task_manager.services.state_manager import StateManager
from task_manager.models.task import Task, TaskStatus, TaskPriority


@pytest.mark.skip(reason="Temporarily skipping due to mocking issues")
class TestTaskManager:
    """Test cases for Task Manager"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.state_manager = Mock(spec=StateManager)
        self.orchestrator = TaskOrchestrator(self.state_manager)
    
    @pytest.mark.asyncio
    async def test_task_creation(self):
        """Test task creation"""
        task_data = {
            "id": "task_001",
            "title": "Create login function",
            "description": "Implement user authentication",
            "priority": TaskPriority.HIGH,
            "estimated_duration": 30
        }
        
        task = Task(**task_data)
        
        assert task.id == "task_001"
        assert task.status == TaskStatus.PENDING
        assert task.priority == TaskPriority.HIGH
    
    @pytest.mark.asyncio
    async def test_orchestrator_process_task(self):
        """Test task processing by orchestrator"""
        # Mock dependencies
        self.state_manager.get_project_tasks = AsyncMock(return_value=[])
        self.state_manager.update_task_status = AsyncMock()
        
        # Create a test task
        task = Task(
            id="task_001",
            title="Test task",
            description="Test description",
            priority=TaskPriority.MEDIUM
        )
        
        # Mock the task processing
        self.orchestrator._process_single_task = AsyncMock(return_value=True)
        
        result = await self.orchestrator._process_single_task(task)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_state_manager_operations(self):
        """Test state manager operations"""
        from task_manager.services.state_manager import StateManager
        
        # Create real state manager for testing
        state_manager = StateManager({"storage": {"type": "memory"}})
        
        # Test project creation
        project_id = await state_manager.create_project("Test Project", "Test description")
        assert project_id is not None
        
        # Test task creation
        task = Task(
            id="task_001",
            title="Test task",
            description="Test description",
            project_id=project_id
        )
        
        await state_manager.save_task(task)
        
        # Test task retrieval
        retrieved_task = await state_manager.get_task("task_001")
        assert retrieved_task.title == "Test task"


class TestPlanParser:
    """Test cases for Plan Parser"""
    
    @pytest.mark.asyncio
    async def test_parse_simple_plan(self):
        """Test parsing a simple plan"""
        from task_manager.utils.plan_parser import PlanParser
        
        parser = PlanParser()
        plan_text = """
        1. Create user authentication system
        2. Implement login functionality
        3. Add password reset feature
        4. Create user dashboard
        """
        
        tasks = await parser.parse_plan(plan_text)
        
        assert len(tasks) >= 4
        assert any("authentication" in task.description.lower() for task in tasks)
        assert any("login" in task.description.lower() for task in tasks)
    
    @pytest.mark.asyncio
    async def test_parse_complex_plan(self):
        """Test parsing a complex plan with dependencies"""
        from task_manager.utils.plan_parser import PlanParser
        
        parser = PlanParser()
        plan_text = """
        Project: E-commerce Website
        
        Phase 1: Backend Development
        - Set up database schema
        - Create user management API
        - Implement product catalog API
        
        Phase 2: Frontend Development
        - Design user interface
        - Implement shopping cart
        - Add payment integration
        """
        
        tasks = await parser.parse_plan(plan_text)
        
        assert len(tasks) >= 6
        # Check that tasks have appropriate priorities and phases
        backend_tasks = [t for t in tasks if "backend" in t.description.lower() or "api" in t.description.lower()]
        frontend_tasks = [t for t in tasks if "frontend" in t.description.lower() or "interface" in t.description.lower()]
        
        assert len(backend_tasks) > 0
        assert len(frontend_tasks) > 0


if __name__ == "__main__":
    pytest.main([__file__])
