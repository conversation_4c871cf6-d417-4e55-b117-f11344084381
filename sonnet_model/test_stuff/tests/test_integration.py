"""
Integration tests for the complete Agentic Code Development System
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from system_integration import AgenticSystem
from task_manager.models.task import Task, TaskPriority
from code_generator.models.generation_request import GenerationRequest, ProgrammingLanguage
from critique_engine.models.critique_request import CritiqueRequest


class TestSystemIntegration:
    """Test cases for complete system integration"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.config = {
            "task_manager": {"storage": {"type": "memory"}},
            "code_generator": {"llm": {"model_endpoint": "http://localhost:8000"}},
            "critique_engine": {"static_analysis": {"enabled_tools": ["pylint"]}}
        }
        self.system = AgenticSystem(self.config)
    
    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow"""
        # Create a project
        project_id = await self.system.create_project(
            "Test Project",
            "Create a simple calculator",
            "Create a Python calculator with basic operations"
        )
        
        assert project_id is not None
        
        # Mock the LLM responses
        self.system.code_generator.llm_interface.generate = AsyncMock(return_value={
            "code": "def add(a, b):\n    return a + b\n\ndef subtract(a, b):\n    return a - b",
            "explanation": "Basic calculator functions",
            "confidence": 0.9
        })
        
        self.system.critique_engine.llm_critic.critique = AsyncMock(return_value={
            "issues": [],
            "suggestions": ["Add input validation", "Add docstrings"]
        })
        
        # Process the project
        result = await self.system.process_project(project_id)
        
        assert result["success"] is True
        assert "project_id" in result
    
    @pytest.mark.asyncio
    async def test_iterative_refinement(self):
        """Test iterative code refinement process"""
        # Create initial code with issues
        initial_code = "def calc(x,y): return x+y"
        
        # Mock critique that finds issues
        self.system.critique_engine.static_analyzer.analyze = AsyncMock(return_value={
            "issues": [
                {"type": "style", "message": "Missing docstring", "line": 1}
            ],
            "metrics": {"complexity": 1}
        })
        
        # Mock improved code generation
        self.system.code_generator.llm_interface.generate = AsyncMock(return_value={
            "code": "def calculate(x: int, y: int) -> int:\n    \"\"\"Add two numbers.\"\"\"\n    return x + y",
            "explanation": "Improved with type hints and docstring",
            "confidence": 0.95
        })
        
        # Test refinement process
        task = Task(
            id="test_task",
            title="Create calculator",
            description="Basic calculator function"
        )
        
        refined_code = await self.system._refine_code_iteratively(
            task, initial_code, "python", max_iterations=2
        )
        
        assert "def calculate" in refined_code
        assert '"""' in refined_code  # Should have docstring
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test system error handling"""
        # Test with invalid project ID
        result = await self.system.get_project_status("invalid_project_id")
        
        assert result["success"] is False
        assert "error" in result
    
    @pytest.mark.asyncio
    async def test_concurrent_processing(self):
        """Test concurrent task processing"""
        # Create multiple projects
        project_ids = []
        for i in range(3):
            project_id = await self.system.create_project(
                f"Test Project {i}",
                f"Test description {i}",
                f"Create test code {i}"
            )
            project_ids.append(project_id)
        
        # Mock responses
        self.system.code_generator.llm_interface.generate = AsyncMock(return_value={
            "code": "def test_function(): pass",
            "explanation": "Test function",
            "confidence": 0.8
        })
        
        # Process all projects concurrently
        tasks = [self.system.process_project(pid) for pid in project_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should complete successfully
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == 3


class TestAPIIntegration:
    """Test cases for API integration"""
    
    @pytest.mark.asyncio
    async def test_api_endpoints(self):
        """Test API endpoint functionality"""
        from api.routes import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # Test health check
        response = client.get("/health")
        assert response.status_code == 200
        
        # Test project creation
        project_data = {
            "name": "Test API Project",
            "description": "Test project via API",
            "user_input": "Create a hello world function"
        }
        
        response = client.post("/projects", json=project_data)
        assert response.status_code == 200
        
        project_id = response.json()["project_id"]
        assert project_id is not None
        
        # Test project status
        response = client.get(f"/projects/{project_id}/status")
        assert response.status_code == 200


if __name__ == "__main__":
    pytest.main([__file__])
