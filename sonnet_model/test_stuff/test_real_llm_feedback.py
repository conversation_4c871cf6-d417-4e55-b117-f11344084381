"""
Real LLM Feedback Loop Test

This tests the ACTUAL feedback loop using:
1. Local DeepSeek LLM for Code Generation
2. Local DeepSeek LLM for Code Critique  
3. Real iterative improvement until quality is acceptable
4. Sequential processing of 3 tasks

This is the REAL system in action!
"""

import asyncio
import json
import logging
from typing import Dict, Any, List
import httpx

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

class RealLLMFeedbackLoop:
    """Test real feedback loop with actual DeepSeek LLM"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model = "deepseek-coder-v2:16b"
        
        # Real test tasks
        self.tasks = [
            {
                "id": 1,
                "name": "Binary Search Implementation",
                "description": "Implement a binary search function that finds the index of a target value in a sorted array",
                "requirements": [
                    "Handle empty arrays",
                    "Return -1 if not found", 
                    "Include proper documentation",
                    "Add type hints",
                    "Handle edge cases"
                ]
            },
            {
                "id": 2,
                "name": "LRU Cache Implementation",
                "description": "Implement an LRU (Least Recently Used) cache with get and put operations",
                "requirements": [
                    "Fixed capacity",
                    "O(1) time complexity for both operations",
                    "Proper eviction policy",
                    "Include documentation",
                    "Add type hints"
                ]
            },
            {
                "id": 3,
                "name": "JSON Validator",
                "description": "Create a function to validate JSON strings and return detailed error information",
                "requirements": [
                    "Validate JSON syntax",
                    "Return meaningful error messages",
                    "Handle edge cases",
                    "Include comprehensive tests",
                    "Add proper documentation"
                ]
            }
        ]
        
    async def run_real_feedback_loop(self):
        """Run the complete real feedback loop"""
        
        print("🚀 STARTING REAL LLM FEEDBACK LOOP TEST")
        print("=" * 70)
        print("🤖 Code Generator: DeepSeek-Coder-V2:16B (Local)")
        print("🔍 Critique Engine: DeepSeek-Coder-V2:16B (Local)")
        print("=" * 70)
        
        # Test Ollama connection first
        if not await self._test_ollama_connection():
            print("❌ Cannot connect to Ollama. Please start Ollama first.")
            return
        
        print("✅ Connected to Ollama successfully!")
        
        # Process each task through real feedback loop
        for task in self.tasks:
            print(f"\n📋 PROCESSING TASK {task['id']}: {task['name']}")
            print(f"Description: {task['description']}")
            print("-" * 70)
            
            await self._process_task_with_real_llm(task)
            
            print(f"✅ TASK {task['id']} COMPLETED!")
            print("Moving to next task...\n")
        
        print("🎉 ALL TASKS COMPLETED WITH REAL LLM FEEDBACK!")
        
    async def _test_ollama_connection(self) -> bool:
        """Test connection to Ollama"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:11434/api/tags", timeout=5.0)
                return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Ollama connection failed: {e}")
            return False
    
    async def _process_task_with_real_llm(self, task: Dict):
        """Process task through real LLM feedback loop"""
        
        iteration = 1
        max_iterations = 4
        current_code = ""
        
        while iteration <= max_iterations:
            print(f"\n🔄 ITERATION {iteration}")
            print("-" * 30)
            
            # STEP 1: REAL CODE GENERATION
            print("🤖 CODE GENERATOR: Generating code with DeepSeek LLM...")
            
            generated_code = await self._generate_code_with_llm(task, current_code, iteration)
            
            if not generated_code:
                print("❌ Code generation failed")
                break
                
            print(f"   ✅ Generated {len(generated_code)} characters of code")
            
            # Show code preview
            lines = generated_code.split('\n')[:5]
            print("   📝 Code Preview:")
            for line in lines:
                if line.strip():
                    print(f"      {line}")
            if len(generated_code.split('\n')) > 5:
                print("      ...")
            
            # STEP 2: REAL CODE CRITIQUE
            print("\n🔍 CRITIQUE ENGINE: Analyzing with DeepSeek LLM...")
            
            critique_result = await self._critique_code_with_llm(generated_code, task)
            
            if not critique_result:
                print("❌ Code critique failed")
                break
            
            print(f"   📊 Quality Score: {critique_result['quality_score']}/10")
            print(f"   🔍 Issues Found: {len(critique_result['issues'])}")
            
            if critique_result['issues']:
                print("   ⚠️  Issues Identified:")
                for issue in critique_result['issues'][:3]:  # Show first 3
                    print(f"      - {issue}")
                if len(critique_result['issues']) > 3:
                    print(f"      ... and {len(critique_result['issues']) - 3} more")
            
            # STEP 3: DECISION POINT
            if critique_result['quality_score'] >= 7:
                print("\n✅ QUALITY THRESHOLD REACHED!")
                print("   Code meets requirements - Task complete!")
                
                # Show final code
                print("\n📄 FINAL CODE:")
                print("```python")
                print(generated_code)
                print("```")
                break
            else:
                print(f"\n🔧 FEEDBACK FOR NEXT ITERATION:")
                for suggestion in critique_result['suggestions'][:3]:
                    print(f"   → {suggestion}")
                
                current_code = generated_code
                iteration += 1
                
                if iteration <= max_iterations:
                    print(f"\n🔄 Preparing iteration {iteration} with feedback...")
        
        if iteration > max_iterations:
            print(f"\n⚠️  Reached maximum iterations ({max_iterations})")
    
    async def _generate_code_with_llm(self, task: Dict, previous_code: str, iteration: int) -> str:
        """Generate code using real DeepSeek LLM"""
        
        # Build prompt based on iteration
        if iteration == 1:
            prompt = f"""You are an expert Python developer. Create a high-quality implementation for this task:

Task: {task['description']}

Requirements:
{chr(10).join(f"- {req}" for req in task['requirements'])}

Please provide a complete, working Python implementation with proper error handling, documentation, and type hints.

Code:"""
        else:
            prompt = f"""You are an expert Python developer. Improve the following code based on the feedback:

Task: {task['description']}

Requirements:
{chr(10).join(f"- {req}" for req in task['requirements'])}

Previous code:
```python
{previous_code}
```

Please provide an improved version that addresses any issues and better meets the requirements.

Improved code:"""
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.2,
                            "top_p": 0.9,
                            "num_predict": 2048
                        }
                    },
                    timeout=120.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    generated_text = result.get("response", "")
                    
                    # Extract code from response
                    code = self._extract_code_from_response(generated_text)
                    return code
                else:
                    self.logger.error(f"LLM request failed: {response.status_code}")
                    return ""
                    
        except Exception as e:
            self.logger.error(f"Code generation failed: {e}")
            return ""
    
    async def _critique_code_with_llm(self, code: str, task: Dict) -> Dict[str, Any]:
        """Critique code using real DeepSeek LLM"""
        
        prompt = f"""You are an expert code reviewer. Analyze this Python code and provide detailed feedback:

Task: {task['description']}

Requirements:
{chr(10).join(f"- {req}" for req in task['requirements'])}

Code to review:
```python
{code}
```

Please provide your analysis in this JSON format:
{{
    "quality_score": <number from 1-10>,
    "issues": [
        "issue 1 description",
        "issue 2 description"
    ],
    "suggestions": [
        "suggestion 1",
        "suggestion 2"
    ],
    "strengths": [
        "strength 1",
        "strength 2"
    ]
}}

Analysis:"""
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.1,
                            "top_p": 0.8,
                            "num_predict": 1024
                        }
                    },
                    timeout=120.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    critique_text = result.get("response", "")
                    
                    # Parse JSON response
                    critique_data = self._parse_critique_response(critique_text)
                    return critique_data
                else:
                    self.logger.error(f"Critique request failed: {response.status_code}")
                    return self._default_critique()
                    
        except Exception as e:
            self.logger.error(f"Code critique failed: {e}")
            return self._default_critique()
    
    def _extract_code_from_response(self, response: str) -> str:
        """Extract Python code from LLM response"""
        
        # Look for code blocks
        if "```python" in response:
            start = response.find("```python") + 9
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()
        
        elif "```" in response:
            start = response.find("```") + 3
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()
        
        # If no code blocks, look for function definitions
        lines = response.split('\n')
        code_lines = []
        in_code = False
        
        for line in lines:
            if line.strip().startswith(('def ', 'class ', 'import ', 'from ')):
                in_code = True
            
            if in_code:
                code_lines.append(line)
        
        if code_lines:
            return '\n'.join(code_lines)
        
        # Fallback: return the whole response
        return response.strip()
    
    def _parse_critique_response(self, response: str) -> Dict[str, Any]:
        """Parse critique response from LLM"""
        
        try:
            # Look for JSON in the response
            start = response.find('{')
            end = response.rfind('}') + 1
            
            if start != -1 and end > start:
                json_str = response[start:end]
                return json.loads(json_str)
        except:
            pass
        
        # Fallback parsing
        return self._extract_critique_manually(response)
    
    def _extract_critique_manually(self, response: str) -> Dict[str, Any]:
        """Manually extract critique information"""
        
        issues = []
        suggestions = []
        quality_score = 5
        
        lines = response.lower().split('\n')
        
        for line in lines:
            if any(word in line for word in ['issue', 'problem', 'error', 'bug']):
                issues.append(line.strip())
            elif any(word in line for word in ['suggest', 'improve', 'recommend', 'consider']):
                suggestions.append(line.strip())
            elif 'score' in line and any(char.isdigit() for char in line):
                # Extract number from line
                numbers = [int(s) for s in line.split() if s.isdigit()]
                if numbers:
                    quality_score = min(10, max(1, numbers[0]))
        
        return {
            "quality_score": quality_score,
            "issues": issues[:5],  # Limit to 5 issues
            "suggestions": suggestions[:5],  # Limit to 5 suggestions
            "strengths": ["Code structure looks good"]
        }
    
    def _default_critique(self) -> Dict[str, Any]:
        """Default critique when LLM fails"""
        return {
            "quality_score": 5,
            "issues": ["Unable to analyze code"],
            "suggestions": ["Please review manually"],
            "strengths": []
        }


async def main():
    """Run the real LLM feedback loop test"""
    
    print("🎯 REAL LLM FEEDBACK LOOP TEST")
    print("Using actual DeepSeek-Coder-V2:16B model for both generation and critique")
    print("=" * 70)
    
    tester = RealLLMFeedbackLoop()
    await tester.run_real_feedback_loop()


if __name__ == "__main__":
    asyncio.run(main())
