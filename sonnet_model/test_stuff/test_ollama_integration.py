#!/usr/bin/env python3
"""
Test Ollama Integration
Quick test to verify the system works with Ollama deepseek-coder-v2:16b
"""
import asyncio
import sys
import os

# Add the sonnet_model directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from code_generator.services.code_generator import CodeGenerator
from shared.models import GenerationRequest, ProgrammingLanguage
from utils.config_loader import load_config


async def test_ollama_code_generation():
    """Test code generation with Ollama"""
    print("🚀 Testing Ollama Integration with deepseek-coder-v2:16b")
    
    # Load configuration
    try:
        config = load_config()
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return False
    
    # Initialize CodeGenerator
    try:
        code_gen_config = config.get("code_generator", {})
        code_generator = CodeGenerator(code_gen_config)
        await code_generator.initialize()
        print("✅ CodeGenerator initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize CodeGenerator: {e}")
        return False
    
    # Create a simple generation request
    request = GenerationRequest(
        task_id="test-ollama-001",
        language=ProgrammingLanguage.PYTHON,
        description="Create a simple Python function that calculates the factorial of a number",
        requirements=[
            "Function should be named 'factorial'",
            "Should handle edge cases (0 and 1)",
            "Should include docstring",
            "Should be efficient"
        ]
    )
    
    print(f"📝 Generation request created: {request.description}")
    
    # Test code generation
    try:
        print("🔄 Generating code with Ollama...")
        result = await code_generator.generate_code(request)
        
        if result.success:
            print("✅ Code generation successful!")
            print(f"📊 Model used: {result.model_used}")
            print(f"⏱️  Generation time: {result.generation_time:.2f}s")
            print(f"🔢 Tokens used: {result.tokens_used}")
            print("\n📄 Generated Code:")
            print("=" * 50)
            print(result.code)
            print("=" * 50)

            # Cleanup
            await code_generator.shutdown()
            return True
        else:
            print(f"❌ Code generation failed: {result.error_message}")
            await code_generator.shutdown()
            return False

    except Exception as e:
        print(f"❌ Exception during code generation: {e}")
        import traceback
        traceback.print_exc()
        try:
            await code_generator.shutdown()
        except:
            pass
        return False


async def test_llm_interface_directly():
    """Test LLM interface directly"""
    print("\n🔧 Testing LLM Interface directly")
    
    try:
        from code_generator.llm_interface import create_llm_interface
        
        # Create LLM interface with Ollama config
        llm_config = {
            "type": "http_api",
            "api_url": "http://localhost:11434/api/generate",
            "model": "deepseek-coder-v2:16b",
            "timeout": 300
        }
        
        llm_interface = create_llm_interface(llm_config)
        print("✅ LLM Interface created successfully")

        # Initialize the interface
        await llm_interface.initialize()
        print("✅ LLM Interface initialized successfully")

        # Test simple generation
        prompt = "Write a Python function to add two numbers:"
        print(f"📝 Testing with prompt: {prompt}")

        response = await llm_interface.generate(prompt, max_tokens=200, temperature=0.2)

        # Cleanup
        await llm_interface.shutdown()
        
        if response.get("success", False):
            print("✅ Direct LLM interface test successful!")
            print(f"📊 Model: {response.get('model', 'unknown')}")
            print(f"🔢 Tokens used: {response.get('tokens_used', 0)}")
            print(f"📄 Response: {response.get('text', '')[:200]}...")
            return True
        else:
            print(f"❌ Direct LLM interface test failed: {response.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during direct LLM test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function"""
    print("🧪 Ollama Integration Test Suite")
    print("=" * 60)
    
    # Test 1: Direct LLM interface
    test1_success = await test_llm_interface_directly()
    
    # Test 2: Full CodeGenerator
    test2_success = await test_ollama_code_generation()
    
    print("\n📊 Test Results:")
    print("=" * 60)
    print(f"Direct LLM Interface: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"CodeGenerator Integration: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed! Ollama integration is working correctly.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Check the configuration and Ollama setup.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
