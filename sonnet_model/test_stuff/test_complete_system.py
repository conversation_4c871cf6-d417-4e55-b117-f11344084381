#!/usr/bin/env python3
"""
Comprehensive System Test Suite

Tests every component of the sonnet_model system:
1. Task Manager (orchestrator, state manager, priority engine)
2. Code Generator (LLM interface, prompt builder, code formatter)
3. Critique Engine (static analyzer, LLM critic, test runner)
4. Shared Models and Utilities
5. Integration Tests
6. Performance Tests
"""
import asyncio
import sys
import os
import time
import json
from pathlib import Path

# Add the sonnet_model directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import all components
from task_manager.services.orchestrator import TaskOrchestrator
from task_manager.services.state_manager import StateManager
from task_manager.services.priority_engine import PriorityEngine
from task_manager.services.completion_tracker import Project<PERSON>ompletionTracker

from code_generator.services.code_generator import CodeGenerator
from code_generator.services.prompt_builder import PromptBuilder
from code_generator.services.code_formatter import CodeFormatter
from code_generator.llm_interface import create_llm_interface

from critique_engine.services.critique_engine import CritiqueEngine
from critique_engine.services.static_analyzer import St<PERSON><PERSON><PERSON>yzer
from critique_engine.services.llm_critic import LL<PERSON>ritic
from critique_engine.analyzers.python_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON>

from shared.models import *
from shared.conversation_state import ConversationStateManager
from utils.config_loader import load_config


class ComprehensiveTestSuite:
    """Comprehensive test suite for the entire system"""
    
    def __init__(self):
        self.config = load_config()
        self.test_results = {}
        self.failed_tests = []
        self.passed_tests = []
        
    async def run_all_tests(self):
        """Run all comprehensive tests"""
        print("🧪 COMPREHENSIVE SYSTEM TEST SUITE")
        print("=" * 80)
        print("Testing ALL components of the sonnet_model system")
        print("=" * 80)
        
        # Test categories
        test_categories = [
            ("🎯 Task Manager Tests", self.test_task_manager),
            ("🤖 Code Generator Tests", self.test_code_generator),
            ("🔍 Critique Engine Tests", self.test_critique_engine),
            ("📊 Shared Models Tests", self.test_shared_models),
            ("🔄 Conversation State Tests", self.test_conversation_state),
            ("🔗 Integration Tests", self.test_integration),
            ("⚡ Performance Tests", self.test_performance),
            ("🛡️ Error Handling Tests", self.test_error_handling),
            ("📝 Configuration Tests", self.test_configuration)
        ]
        
        for category_name, test_function in test_categories:
            print(f"\n{category_name}")
            print("-" * 60)
            
            try:
                start_time = time.time()
                await test_function()
                duration = time.time() - start_time
                print(f"✅ {category_name} PASSED ({duration:.2f}s)")
                self.passed_tests.append(category_name)
            except Exception as e:
                print(f"❌ {category_name} FAILED: {e}")
                self.failed_tests.append((category_name, str(e)))
                import traceback
                traceback.print_exc()
        
        # Final report
        self.print_final_report()
        
        return len(self.failed_tests) == 0
    
    async def test_task_manager(self):
        """Test all Task Manager components"""
        print("Testing Task Manager components...")
        
        # Test TaskOrchestrator
        orchestrator = TaskOrchestrator(self.config.get("task_manager", {}))
        
        # Create test project (using simple dict since Project model may not be fully implemented)
        project = {
            "id": "test-project-001",
            "name": "Test Project",
            "description": "Comprehensive test project",
            "status": "active"
        }
        
        # Test task creation
        task = Task(
            id="test-task-001",
            project_id=project["id"],
            title="Test Task",
            description="Test task description",
            status=TaskStatus.PENDING,
            task_type=TaskType.CODE_GENERATION
        )
        
        # Test state manager
        state_manager = StateManager(self.config.get("task_manager", {}))
        await state_manager.initialize()
        await state_manager.save_task(task)
        retrieved_task = await state_manager.get_task(task.id)
        assert retrieved_task.id == task.id, "Task retrieval failed"
        
        # Test priority engine
        priority_engine = PriorityEngine(self.config.get("task_manager", {}))
        priority = priority_engine.calculate_priority_score(task)
        assert isinstance(priority, (int, float)), "Priority calculation failed"
        
        # Test completion tracker
        completion_tracker = ProjectCompletionTracker(self.config.get("completion_tracker", {}))
        completion_report = completion_tracker.generate_completion_report([task])
        assert "COMPLETION REPORT" in completion_report, "Completion report generation failed"
        
        print("  ✅ TaskOrchestrator: Working")
        print("  ✅ TaskStateManager: Working")
        print("  ✅ PriorityEngine: Working")
        print("  ✅ CompletionTracker: Working")
    
    async def test_code_generator(self):
        """Test all Code Generator components"""
        print("Testing Code Generator components...")
        
        # Test LLM Interface
        llm_config = self.config.get("code_generator", {}).get("llm", {})
        llm_interface = create_llm_interface(llm_config)
        await llm_interface.initialize()
        
        # Test simple generation
        test_prompt = "Write a simple Python function that adds two numbers"
        result = await llm_interface.generate(test_prompt, max_tokens=1000, temperature=0.2)
        assert result["success"], f"LLM generation failed: {result.get('error')}"
        assert len(result["text"]) > 0, "Generated text is empty"
        
        # Test PromptBuilder
        prompt_builder = PromptBuilder(self.config.get("code_generator", {}))
        generation_request = GenerationRequest(
            task_id="test-gen-001",
            language=ProgrammingLanguage.PYTHON,
            description="Create a calculator function",
            requirements=["Add two numbers", "Return the result"]
        )
        
        prompt = prompt_builder.build_generation_prompt(generation_request)
        assert len(prompt) > 0, "Prompt building failed"
        assert "calculator" in prompt.lower(), "Prompt doesn't contain description"
        
        # Test CodeFormatter
        code_formatter = CodeFormatter()
        test_code = "def add(a,b):return a+b"
        formatted_code = await code_formatter.format_code(test_code, "python")
        assert "def add(" in formatted_code, "Code formatting failed"
        
        # Test full CodeGenerator
        code_generator = CodeGenerator(self.config.get("code_generator", {}))
        await code_generator.initialize()
        
        generation_result = await code_generator.generate_code(generation_request)
        assert generation_result.success, f"Code generation failed: {generation_result.error_message}"
        assert len(generation_result.code) > 0, "Generated code is empty"
        
        await code_generator.shutdown()
        
        print("  ✅ LLM Interface: Working")
        print("  ✅ PromptBuilder: Working")
        print("  ✅ CodeFormatter: Working")
        print("  ✅ CodeGenerator: Working")
    
    async def test_critique_engine(self):
        """Test all Critique Engine components"""
        print("Testing Critique Engine components...")
        
        # Test PythonAnalyzer
        python_analyzer = PythonAnalyzer(self.config.get("critique_engine", {}))
        
        # Test with good code
        good_code = '''
def fibonacci(n: int) -> int:
    """Calculate the nth Fibonacci number."""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)
'''
        
        good_issues = await python_analyzer.analyze_code(good_code, "test.py", set())
        print(f"    Good code analysis: {len(good_issues)} issues found")
        
        # Test with problematic code
        bad_code = '''
def bad_function():
    x = 1
    y = 2
    print("hello"
'''  # Missing closing parenthesis
        
        bad_issues = await python_analyzer.analyze_code(bad_code, "test.py", {IssueCategory.SYNTAX})
        assert len(bad_issues) > 0, "Should detect syntax error"
        
        # Test StaticAnalyzer
        static_analyzer = StaticAnalyzer(self.config.get("critique_engine", {}))
        static_issues = await static_analyzer.analyze_code(good_code, "test.py", {IssueCategory.STYLE})
        print(f"    Static analysis: {len(static_issues)} issues found")
        
        # Test LLMCritic
        llm_critic = LLMCritic(self.config.get("critique_engine", {}))
        critique_request = CritiqueRequest(
            task_id="test-critique-001",
            code=good_code,
            language=ProgrammingLanguage.PYTHON,
            categories=set()
        )
        
        llm_issues = await llm_critic.critique_code(critique_request)
        print(f"    LLM critique: {len(llm_issues)} issues found")
        
        # Test full CritiqueEngine
        critique_engine = CritiqueEngine(self.config.get("critique_engine", {}))
        critique_result = await critique_engine.critique_code(critique_request)
        
        assert isinstance(critique_result.quality_score, (int, float)), "Quality score should be numeric"
        assert 0 <= critique_result.quality_score <= 1, "Quality score should be between 0 and 1"
        
        print("  ✅ PythonAnalyzer: Working")
        print("  ✅ StaticAnalyzer: Working")
        print("  ✅ LLMCritic: Working")
        print("  ✅ CritiqueEngine: Working")
    
    async def test_shared_models(self):
        """Test all shared models"""
        print("Testing Shared Models...")
        
        # Test Task model
        task = Task(
            id="test-model-001",
            project_id="test-project",
            title="Test Task",
            description="Test description",
            status=TaskStatus.PENDING,
            task_type=TaskType.CODE_GENERATION
        )
        
        # Test serialization
        task_dict = task.__dict__
        assert task_dict["id"] == "test-model-001", "Task serialization failed"
        
        # Test GenerationRequest
        gen_request = GenerationRequest(
            task_id="test-gen",
            language=ProgrammingLanguage.PYTHON,
            description="Test generation",
            requirements=["Requirement 1"]
        )
        
        assert gen_request.language == ProgrammingLanguage.PYTHON, "GenerationRequest creation failed"
        
        # Test CritiqueRequest
        critique_request = CritiqueRequest(
            task_id="test-critique",
            code="print('hello')",
            language=ProgrammingLanguage.PYTHON,
            categories=set()
        )
        
        assert critique_request.language == ProgrammingLanguage.PYTHON, "CritiqueRequest creation failed"
        
        print("  ✅ Task Model: Working")
        print("  ✅ GenerationRequest: Working")
        print("  ✅ CritiqueRequest: Working")
        print("  ✅ All Models: Serializable")
    
    async def test_conversation_state(self):
        """Test conversation state management"""
        print("Testing Conversation State Management...")
        
        # Test ConversationStateManager
        conv_manager = ConversationStateManager(self.config.get("conversation_state", {}))
        
        # Initialize conversation
        state = conv_manager.initialize_conversation("test-conv-001", "Test project summary")
        assert state.conversation_id == "test-conv-001", "Conversation initialization failed"
        
        # Test message updates
        conv_manager.update_message_count(100)
        assert state.message_count == 1, "Message count update failed"
        assert state.total_tokens == 100, "Token count update failed"
        
        # Test task management
        conv_manager.add_task("task-001")
        assert "task-001" in state.active_tasks, "Task addition failed"
        
        conv_manager.complete_task("task-001")
        assert "task-001" in state.completed_tasks, "Task completion failed"
        assert "task-001" not in state.active_tasks, "Task should be removed from active"
        
        # Test key decisions
        conv_manager.add_key_decision("Important decision made")
        assert len(state.key_decisions) > 0, "Key decision addition failed"
        
        # Test progress summary
        progress = conv_manager.get_progress_summary()
        assert "conversation_id" in progress, "Progress summary missing conversation_id"
        assert "message_count" in progress, "Progress summary missing message_count"
        
        print("  ✅ ConversationStateManager: Working")
        print("  ✅ State Persistence: Working")
        print("  ✅ Progress Tracking: Working")

    async def test_integration(self):
        """Test full system integration"""
        print("Testing Full System Integration...")

        # Test end-to-end workflow: Generate -> Critique -> Complete
        code_generator = CodeGenerator(self.config.get("code_generator", {}))
        await code_generator.initialize()

        critique_engine = CritiqueEngine(self.config.get("critique_engine", {}))

        # Step 1: Generate code
        generation_request = GenerationRequest(
            task_id="integration-test-001",
            language=ProgrammingLanguage.PYTHON,
            description="Create a function to calculate factorial",
            requirements=["Handle edge cases", "Include docstring", "Use recursion"]
        )

        generation_result = await code_generator.generate_code(generation_request)
        assert generation_result.success, "Integration test: Code generation failed"

        # Step 2: Critique the generated code
        critique_request = CritiqueRequest(
            task_id="integration-test-001",
            code=generation_result.code,
            language=ProgrammingLanguage.PYTHON,
            categories=set()
        )

        critique_result = await critique_engine.critique_code(critique_request)
        assert isinstance(critique_result.quality_score, (int, float)), "Integration test: Critique failed"

        # Step 3: Test task management integration
        task = Task(
            id="integration-test-001",
            project_id="integration-project",
            title="Factorial Function",
            description="Generate factorial function",
            status=TaskStatus.COMPLETED,
            task_type=TaskType.CODE_GENERATION,
            quality_score=critique_result.quality_score
        )

        completion_tracker = ProjectCompletionTracker(self.config.get("completion_tracker", {}))
        completion_report = completion_tracker.generate_completion_report([task])
        assert "COMPLETION REPORT" in completion_report, "Integration test: Completion tracking failed"

        await code_generator.shutdown()

        print("  ✅ Generate -> Critique workflow: Working")
        print("  ✅ Task management integration: Working")
        print("  ✅ End-to-end pipeline: Working")

    async def test_performance(self):
        """Test system performance"""
        print("Testing System Performance...")

        # Test code generation performance
        code_generator = CodeGenerator(self.config.get("code_generator", {}))
        await code_generator.initialize()

        start_time = time.time()
        generation_request = GenerationRequest(
            task_id="perf-test-001",
            language=ProgrammingLanguage.PYTHON,
            description="Create a simple hello world function",
            requirements=["Print hello world"]
        )

        generation_result = await code_generator.generate_code(generation_request)
        generation_time = time.time() - start_time

        assert generation_result.success, "Performance test: Generation failed"
        assert generation_time < 30, f"Generation too slow: {generation_time:.2f}s"

        # Test critique performance
        critique_engine = CritiqueEngine(self.config.get("critique_engine", {}))

        start_time = time.time()
        critique_request = CritiqueRequest(
            task_id="perf-test-001",
            code=generation_result.code,
            language=ProgrammingLanguage.PYTHON,
            categories=set()
        )

        critique_result = await critique_engine.critique_code(critique_request)
        critique_time = time.time() - start_time

        assert critique_time < 5, f"Critique too slow: {critique_time:.2f}s"
        assert isinstance(critique_result.quality_score, (int, float)), "Performance test: Invalid quality score"

        await code_generator.shutdown()

        print(f"  ✅ Code Generation: {generation_time:.2f}s")
        print(f"  ✅ Code Critique: {critique_time:.2f}s")
        print("  ✅ Performance: Acceptable")

    async def test_error_handling(self):
        """Test error handling capabilities"""
        print("Testing Error Handling...")

        # Test invalid generation request
        code_generator = CodeGenerator(self.config.get("code_generator", {}))
        await code_generator.initialize()

        invalid_request = GenerationRequest(
            task_id="error-test-001",
            language=ProgrammingLanguage.PYTHON,
            description="",  # Empty description
            requirements=[]
        )

        # Should handle gracefully
        result = await code_generator.generate_code(invalid_request)
        # Should either succeed with minimal code or fail gracefully
        assert hasattr(result, 'success'), "Error handling: No success attribute"

        # Test invalid code critique
        critique_engine = CritiqueEngine(self.config.get("critique_engine", {}))

        invalid_critique_request = CritiqueRequest(
            task_id="error-test-002",
            code="invalid python syntax !!!",
            language=ProgrammingLanguage.PYTHON,
            categories=set()
        )

        # Should handle syntax errors gracefully
        critique_result = await critique_engine.critique_code(invalid_critique_request)
        assert hasattr(critique_result, 'quality_score'), "Error handling: Missing quality score"

        await code_generator.shutdown()

        print("  ✅ Invalid input handling: Working")
        print("  ✅ Syntax error handling: Working")
        print("  ✅ Graceful degradation: Working")

    async def test_configuration(self):
        """Test configuration management"""
        print("Testing Configuration Management...")

        # Test config loading
        config = load_config()
        assert isinstance(config, dict), "Config should be a dictionary"

        # Test required sections
        required_sections = ["code_generator", "critique_engine", "task_manager"]
        for section in required_sections:
            assert section in config, f"Missing config section: {section}"

        # Test LLM configuration
        llm_config = config.get("code_generator", {}).get("llm", {})
        # Check for model configuration in the nested structure
        has_model_config = "model" in llm_config
        assert has_model_config, f"Missing LLM model configuration. Found: {llm_config.keys()}"

        print("  ✅ Configuration loading: Working")
        print("  ✅ Required sections: Present")
        print("  ✅ LLM configuration: Valid")
    
    def print_final_report(self):
        """Print comprehensive test report"""
        print("\n" + "=" * 80)
        print("🎯 COMPREHENSIVE TEST RESULTS")
        print("=" * 80)
        
        total_tests = len(self.passed_tests) + len(self.failed_tests)
        pass_rate = (len(self.passed_tests) / total_tests * 100) if total_tests > 0 else 0
        
        print(f"📊 SUMMARY:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {len(self.passed_tests)}")
        print(f"   Failed: {len(self.failed_tests)}")
        print(f"   Pass Rate: {pass_rate:.1f}%")
        
        if self.passed_tests:
            print(f"\n✅ PASSED TESTS ({len(self.passed_tests)}):")
            for test in self.passed_tests:
                print(f"   ✅ {test}")
        
        if self.failed_tests:
            print(f"\n❌ FAILED TESTS ({len(self.failed_tests)}):")
            for test, error in self.failed_tests:
                print(f"   ❌ {test}: {error}")
        
        if len(self.failed_tests) == 0:
            print(f"\n🎉 ALL TESTS PASSED! System is fully operational!")
        else:
            print(f"\n⚠️  Some tests failed. Review and fix issues above.")


async def main():
    """Main test function"""
    test_suite = ComprehensiveTestSuite()
    success = await test_suite.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
