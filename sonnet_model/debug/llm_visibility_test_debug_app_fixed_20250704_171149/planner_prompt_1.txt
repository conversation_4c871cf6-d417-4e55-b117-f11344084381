You are an expert software architect. Create a comprehensive project plan for this request:

PROJECT DESCRIPTION: create a simple calculator with basic arithmetic operations
PROJECT TYPE: application

Create a detailed project breakdown in JSON format:

{
    "project_name": "suggested_project_name",
    "project_description": "detailed description",
    "technology_stack": [
        "technology1",
        "technology2"
    ],
    "project_structure": {
        "backend": ["file1.py", "file2.py"],
        "frontend": ["file1.html", "file2.js"],
        "database": ["schema.sql"],
        "tests": ["test_file1.py"],
        "config": ["config.yaml"],
        "docs": ["README.md"]
    },
    "tasks": [
        {
            "id": 1,
            "name": "Task Name",
            "description": "Detailed task description",
            "file_path": "path/to/file.py",
            "dependencies": [],
            "estimated_complexity": "low/medium/high",
            "requirements": [
                "Specific requirement 1",
                "Specific requirement 2"
            ]
        }
    ]
}

IMPORTANT: 
- Include ALL necessary files for a complete project
- Break down into specific, implementable tasks
- Each task should create ONE file
- Include proper dependencies between tasks
- Add comprehensive requirements for each task

PROJECT PLAN: