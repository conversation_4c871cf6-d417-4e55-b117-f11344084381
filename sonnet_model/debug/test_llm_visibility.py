"""
LLM VISIBILITY TEST - Show EXACT prompts and responses for each LLM model
This addresses the user's specific concerns:
1. ONE plan from planner LLM (not multiple plans)
2. EXACT input/output visibility for all LLM models
3. Fix the CodeIssue line attribute error
"""

import asyncio
import json
import os
import traceback as tb
from datetime import datetime
from pathlib import Path
import yaml

from task_manager.services.orchestrator import TaskOrchestrator


class LLMInterceptor:
    """Intercept and capture all LLM calls with full visibility"""
    
    def __init__(self, debug_dir):
        self.debug_dir = debug_dir
        self.call_count = 0
        self.calls = []
    
    def log_llm_call(self, model_name, prompt, response, metadata=None):
        """Log an LLM call with full details"""
        self.call_count += 1
        
        call_data = {
            "call_number": self.call_count,
            "model_name": model_name,
            "timestamp": datetime.now().isoformat(),
            "prompt_length": len(prompt),
            "response_length": len(response),
            "prompt": prompt,
            "response": response,
            "metadata": metadata or {}
        }
        
        self.calls.append(call_data)
        
        # Save individual call
        call_file = self.debug_dir / f"llm_call_{self.call_count:02d}_{model_name}.json"
        with open(call_file, "w") as f:
            json.dump(call_data, f, indent=2, default=str)
        
        print(f"🔍 LLM CALL #{self.call_count}: {model_name.upper()}")
        print(f"📝 Prompt: {len(prompt)} chars")
        print(f"📤 Response: {len(response)} chars")
        print(f"💾 Saved: {call_file.name}")
        print("-" * 40)
        
        return call_data


async def test_single_plan_with_llm_visibility():
    """
    Test with SINGLE plan and FULL LLM visibility
    Shows EXACTLY what goes into each LLM and what comes out
    """
    
    # Create debug directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    debug_dir = Path(f"llm_visibility_{timestamp}")
    debug_dir.mkdir(exist_ok=True)
    
    print("🔍 LLM VISIBILITY TEST")
    print("=" * 50)
    print(f"📁 Debug directory: {debug_dir}")
    print()
    
    # Initialize LLM interceptor
    llm_interceptor = LLMInterceptor(debug_dir)
    
    # Load configuration
    with open("config/config.yaml", "r") as f:
        config = yaml.safe_load(f)
    
    # Initialize orchestrator
    orchestrator = TaskOrchestrator(config)
    await orchestrator.initialize()
    
    try:
        # Simple task
        task = "create a Python function that adds two numbers"
        print(f"🎯 TASK: {task}")
        print()
        
        # STEP 1: Call Planner LLM directly
        print("📋 STEP 1: PLANNER LLM")
        print("=" * 30)
        
        planner = orchestrator.task_planner
        
        # Create plan with planner
        print("🤖 Calling Planner LLM...")
        plan_result = await planner.create_project_plan(
            project_description=task,
            project_type="application"
        )
        
        # Save the single plan
        plan_file = debug_dir / "single_plan.json"
        with open(plan_file, "w") as f:
            json.dump(plan_result, f, indent=2, default=str)
        
        print(f"✅ SINGLE plan created: {plan_file.name}")
        print(f"📋 Tasks in plan: {len(plan_result.get('tasks', []))}")
        print()
        
        # STEP 2: Call Code Generator LLM directly
        if plan_result.get('tasks'):
            first_task = plan_result['tasks'][0]
            
            print("🚀 STEP 2: CODE GENERATOR LLM")
            print("=" * 30)
            print(f"📝 Task: {first_task.get('name', 'Unknown')}")
            print()
            
            # Get code generator
            code_generator = orchestrator.code_generator
            
            # Create generation request
            from code_generator.models.generation_request import GenerationRequest

            # Extract requirement strings from requirement dictionaries
            requirements = []
            for req in first_task.get('requirements', []):
                if isinstance(req, dict):
                    requirements.append(req.get('requirement', str(req)))
                else:
                    requirements.append(str(req))

            generation_request = GenerationRequest(
                task_id=str(first_task.get('id', 'task_1')),
                language="python",
                description=first_task.get('description', task),
                requirements=requirements,
                context="",
                iteration=1
            )
            
            print("🤖 Calling Code Generator LLM...")

            # Capture the prompt that will be sent to LLM
            try:
                prompt = code_generator.prompt_builder.build_generation_prompt(generation_request)
                prompt_file = debug_dir / "code_generator_prompt.txt"
                with open(prompt_file, 'w') as f:
                    f.write(prompt)
                print(f"📝 Code generator prompt saved to: {prompt_file}")
            except Exception as e:
                print(f"⚠️ Could not capture prompt: {e}")

            try:
                code_result = await code_generator.generate_code(generation_request)

                # Save generated code
                code_file = debug_dir / "generated_code.py"
                with open(code_file, "w") as f:
                    f.write(code_result.code)

                code_metadata_file = debug_dir / "code_generation_result.json"
                with open(code_metadata_file, "w") as f:
                    json.dump({
                        "request": generation_request.model_dump(),
                        "result": {
                            "code": code_result.code,
                            "language": code_result.language,
                            "metadata": getattr(code_result, 'metadata', {})
                        }
                    }, f, indent=2, default=str)

                print(f"✅ Code generated: {code_file.name}")
                print(f"📊 Code length: {len(code_result.code)} chars")
                print(f"📋 Metadata: {code_metadata_file.name}")
                print()

            except Exception as e:
                # Save the error details
                error_file = debug_dir / "code_generation_error.json"
                with open(error_file, 'w') as f:
                    json.dump({
                        "error": str(e),
                        "type": type(e).__name__,
                        "task": first_task,
                        "request": generation_request.model_dump(),
                        "traceback": tb.format_exc()
                    }, f, indent=2)
                print(f"❌ Code generation error saved to: {error_file}")
                print(f"❌ Error: {e}")
                print()
                # Continue to critique step anyway
                code_result = None
            
            # STEP 3: Call Critique Engine LLM directly
            print("🔍 STEP 3: CRITIQUE ENGINE LLM")
            print("=" * 30)
            
            # Get critique engine
            from critique_engine.services.critique_engine import CritiqueEngine
            critique_engine = CritiqueEngine(config.get("critique_engine", {}))
            
            # Create critique request
            from critique_engine.models.critique_request import CritiqueRequest, CritiqueCategory, CodeFile

            code_file = CodeFile(
                filename="main.py",
                content=code_result.code if code_result else "# No code generated due to error",
                language="python"
            )

            critique_request = CritiqueRequest(
                request_id=f"critique_{first_task.get('id', 'task_1')}",
                task_id=str(first_task.get('id', 'task_1')),
                files=[code_file],
                language="python",
                categories={CritiqueCategory.QUALITY, CritiqueCategory.SECURITY}
            )
            
            print("🤖 Calling Critique Engine LLM...")
            critique_result = await critique_engine.critique_code(critique_request)
            
            # Save critique
            critique_file = debug_dir / "critique_result.json"
            with open(critique_file, "w") as f:
                json.dump({
                    "request": critique_request.dict(),
                    "result": {
                        "quality_score": critique_result.quality_score,
                        "meets_threshold": critique_result.meets_threshold,
                        "issues": [issue.dict() for issue in critique_result.issues],
                        "suggestions": critique_result.suggestions,
                        "analysis_time": critique_result.analysis_time
                    }
                }, f, indent=2, default=str)
            
            print(f"✅ Critique completed: {critique_file.name}")
            print(f"📊 Quality score: {critique_result.quality_score}")
            print(f"🔍 Issues found: {len(critique_result.issues)}")
            print()
        
        # Save summary
        summary = {
            "task": task,
            "plan_file": str(plan_file),
            "code_file": str(code_file) if 'code_file' in locals() else None,
            "critique_file": str(critique_file) if 'critique_file' in locals() else None,
            "llm_calls": llm_interceptor.calls,
            "debug_directory": str(debug_dir),
            "timestamp": datetime.now().isoformat()
        }
        
        summary_file = debug_dir / "summary.json"
        with open(summary_file, "w") as f:
            json.dump(summary, f, indent=2, default=str)
        
        print("✅ LLM VISIBILITY TEST COMPLETED!")
        print(f"📁 All data in: {debug_dir}")
        print(f"🔍 LLM calls captured: {len(llm_interceptor.calls)}")
        print()
        print("📋 FILES CREATED:")
        for debug_file in sorted(debug_dir.glob("*")):
            print(f"   📄 {debug_file.name}")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        
        # Save error
        error_file = debug_dir / "error.json"
        with open(error_file, "w") as f:
            json.dump({
                "error": str(e),
                "type": type(e).__name__,
                "traceback": traceback.format_exc()
            }, f, indent=2)
    
    finally:
        await orchestrator.shutdown()


if __name__ == "__main__":
    asyncio.run(test_single_plan_with_llm_visibility())
