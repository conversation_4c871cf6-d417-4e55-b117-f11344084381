You are a project manager. Analyze these tasks and determine the optimal implementation order:

TASKS:
[
  {
    "id": 1,
    "name": "Create Backend Logic for Calculator",
    "description": "Implement the backend logic in Python to handle basic arithmetic operations. The calculator should be capable of performing addition, subtraction, multiplication, and division.",
    "file_path": "calculator.py",
    "dependencies": [],
    "estimated_complexity": "low",
    "requirements": [
      {
        "requirement": "Implement a RESTful API using Flask or Django to handle HTTP requests for basic arithmetic operations.",
        "implementation_details": "Use Flask as the web framework. Define endpoints such as '/add', '/subtract', '/multiply', and '/divide' that accept POST requests with JSON payloads containing 'num1' and 'num2'. Return the result of the operation in the response."
      },
      {
        "requirement": "Ensure input validation to handle potential errors like non-numeric inputs.",
        "implementation_details": "Before performing any arithmetic operations, validate that both 'num1' and 'num2' are numeric. If not, return a 400 Bad Request response with an error message."
      },
      {
        "requirement": "Implement logging to track the operations performed.",
        "implementation_details": "Use Python's built-in logging module to log each operation and its result in a file named 'calculator.log'. Include timestamp, operation type (add, subtract, multiply, divide), and operands."
      }
    ],
    "acceptance_criteria": [
      "The calculator should correctly handle addition, subtraction, multiplication, and division of two numbers.",
      "Input validation must be present to ensure only numeric values are processed.",
      "Logging should capture all operations for future reference."
    ],
    "technical_specifications": {
      "functions_to_implement": [
        "add",
        "subtract",
        "multiply",
        "divide"
      ],
      "classes_to_create": [],
      "apis_to_create": [
        "/add",
        "/subtract",
        "/multiply",
        "/divide"
      ],
      "error_handling": [
        "Input validation errors"
      ]
    }
  },
  {
    "id": 2,
    "name": "Design Frontend Interface",
    "description": "Create HTML and CSS files to design the user interface for the calculator. The interface should be intuitive and visually appealing, allowing users to perform basic arithmetic operations easily.",
    "file_path": "index.html",
    "dependencies": [],
    "estimated_complexity": "low",
    "requirements": [
      {
        "requirement": "Create a responsive layout using HTML5 and CSS3 that adapts to different screen sizes (mobile, tablet, desktop).",
        "implementation_details": "Use media queries in CSS to adjust the layout based on the width of the viewport."
      },
      {
        "requirement": "Include buttons for addition, subtraction, multiplication, division, and clear operations.",
        "implementation_details": "Design each button with appropriate styles and ensure they are easily identifiable using semantic HTML elements like <button> or <input type='button'>."
      },
      {
        "requirement": "Implement a display area where the current input and result will be shown. This should support numeric values only.",
        "implementation_details": "Use an HTML element such as <div> or <output> with appropriate CSS for styling to show calculations."
      }
    ],
    "acceptance_criteria": [
      "The calculator interface is visually appealing and easy to navigate.",
      "Buttons are clearly labeled and spaced appropriately.",
      "Display area accurately shows the current input and results after operations."
    ],
    "technical_specifications": {
      "functions_to_implement": [
        "displayInput",
        "performOperation"
      ],
      "classes_to_create": [
        "CalculatorUI",
        "ButtonHandler"
      ],
      "apis_to_create": [],
      "error_handling": [
        "InvalidInputError"
      ]
    }
  },
  {
    "id": 3,
    "name": "Implement Frontend Logic",
    "description": "Write JavaScript code to handle user interactions and communicate with the backend for a simple calculator application.",
    "file_path": "script.js",
    "dependencies": [],
    "estimated_complexity": "low",
    "requirements": [
      {
        "requirement": "Implement basic arithmetic operations (addition, subtraction, multiplication, division) through user interactions on the HTML form.",
        "implementation_details": "Create event listeners for each operation button and capture input values from two number inputs. Use JavaScript to perform calculations based on the selected operation."
      },
      {
        "requirement": "Display results in a designated HTML element without refreshing the page.",
        "implementation_details": "Update an output div or span with the result of the calculation using JavaScript, ensuring that DOM manipulation is handled correctly to avoid full page reloads."
      },
      {
        "requirement": "Include error handling for invalid inputs (e.g., non-numeric values).",
        "implementation_details": "Validate input fields before performing calculations by checking if the entered values are numeric. Display appropriate messages or clear previous results in case of errors."
      }
    ],
    "acceptance_criteria": [
      {
        "criteria": "The calculator should correctly perform addition, subtraction, multiplication, and division operations when provided with valid inputs.",
        "details": "Ensure that the application handles edge cases such as dividing by zero or providing non-numeric values."
      },
      {
        "criteria": "User input errors must be clearly indicated to the user through appropriate messages or updates to the display.",
        "details": "Implement error handling that visually informs users of any issues with their inputs, ensuring a smooth user experience despite potential mistakes."
      }
    ],
    "technical_specifications": {
      "functions_to_implement": [
        "calculateResult",
        "handleInputValidation"
      ],
      "classes_to_create": [],
      "apis_to_create": [],
      "error_handling": [
        "inputValidationError"
      ]
    }
  },
  {
    "id": 4,
    "name": "Add Basic Styling to Interface",
    "description": "Enhance the user interface of a simple calculator by applying CSS for aesthetics and usability.",
    "file_path": "styles.css",
    "dependencies": [],
    "estimated_complexity": "low",
    "requirements": [
      {
        "requirement": "Use modern design principles to create an attractive layout with appropriate color scheme that enhances readability of the calculator buttons and display.",
        "implementationDetails": "Implement a responsive design using CSS Flexbox or Grid for arranging elements. Choose colors that contrast well, ensuring clear visibility of text on both light and dark backgrounds."
      },
      {
        "requirement": "Ensure the calculator interface is mobile-friendly by implementing media queries to adjust layout when viewed on smaller screens.",
        "implementationDetails": "Use CSS Media Queries to change the orientation of elements or hide/show certain components based on screen size. Consider using viewport meta tag in HTML for proper scaling."
      },
      {
        "requirement": "Include hover effects and focus styles for buttons that improve user interaction without adding too much visual clutter.",
        "implementationDetails": "Implement CSS transitions or animations to change the appearance of buttons when hovered over or focused on. This can include background color, text color, or box shadow changes."
      }
    ],
    "acceptance_criteria": [
      "Calculator interface displays clearly and is visually appealing.",
      "Responsive design works across devices including tablets and smartphones as well as desktops.",
      "Hover effects are noticeable but do not obscure the main functionality of the calculator."
    ],
    "technical_specifications": {
      "functions_to_implement": [],
      "classes_to_create": [
        "CalculatorStyle"
      ],
      "apis_to_create": [],
      "error_handling": []
    }
  },
  {
    "id": 5,
    "name": "Unit Testing for Calculator Logic",
    "description": "Write unit tests to ensure the correctness of basic arithmetic operations in the calculator.",
    "file_path": "test_calculator.py",
    "dependencies": [
      "Create Backend Logic for Calculator"
    ],
    "estimated_complexity": "medium",
    "requirements": [
      {
        "requirement": "Implement unit tests using Python's built-in unittest framework to cover all basic arithmetic operations: addition, subtraction, multiplication, and division.",
        "implementation_details": "Ensure each test case includes valid and edge cases inputs. For example, testing with positive numbers, negative numbers, zero, and very large numbers."
      },
      {
        "requirement": "Set up a testing environment using pytest for more advanced unit testing capabilities if applicable.",
        "implementation_details": "Configure the pytest framework to run tests efficiently and provide detailed reports on test results."
      },
      {
        "requirement": "Integrate unit tests with a continuous integration system (e.g., GitHub Actions, Travis CI) for automated testing upon code commits or pull requests.",
        "implementation_details": "Configure the CI system to run all unit tests automatically and report any failures back to the developer."
      }
    ],
    "acceptance_criteria": [
      {
        "criteria": "All basic arithmetic operations must pass at least 90% of their respective test cases.",
        "details": "This includes testing for valid inputs as well as edge and exceptional cases."
      },
      {
        "criteria": "Unit tests should run without errors on the latest stable version of Python.",
        "details": "Ensure compatibility with Python 3.7+ if applicable."
      }
    ],
    "technical_specifications": {
      "functions_to_implement": [
        "add(a, b)",
        "subtract(a, b)",
        "multiply(a, b)",
        "divide(a, b)"
      ],
      "classes_to_create": [],
      "apis_to_create": [],
      "error_handling": [
        "ZeroDivisionError"
      ]
    }
  },
  {
    "id": 6,
    "name": "Document the Project",
    "description": "Create a comprehensive README file to document the project's purpose, installation instructions, and usage guidelines. This will include information on how to install and run the calculator application using Python, HTML, JavaScript, and CSS.",
    "file_path": "README.md",
    "dependencies": [
      "Design Frontend Interface",
      "Implement Frontend Logic",
      "Add Basic Styling to Interface"
    ],
    "estimated_complexity": "low",
    "requirements": [
      {
        "requirement": "Design a user-friendly frontend interface using HTML and CSS that includes buttons for digits, operators, and clear/submit functionalities.",
        "implementation_details": "Use semantic HTML5 to structure the page. Design responsive layout with CSS Flexbox or Grid. Ensure intuitive navigation through well-defined classes and IDs."
      },
      {
        "requirement": "Implement basic arithmetic operations using JavaScript. This includes addition, subtraction, multiplication, and division.",
        "implementation_details": "Write functions in JavaScript that handle the input from the HTML form, perform calculations based on user inputs, and display results dynamically without page refresh."
      },
      {
        "requirement": "Ensure error handling for invalid inputs such as dividing by zero or entering non-numeric values.",
        "implementation_details": "Implement try-catch blocks in JavaScript to manage errors gracefully. Display appropriate messages to users when errors occur."
      }
    ],
    "acceptance_criteria": [
      {
        "criteria": "The README file should be well-organized and include a clear title, an abstract or introduction, sections on purpose, installation, usage, technologies used, contributors, and acknowledgments.",
        "details": "Ensure all necessary information is provided in each section to facilitate understanding of the project."
      },
      {
        "criteria": "The installation instructions should be detailed enough for a beginner to follow, including any prerequisites like software versions or system configurations.",
        "details": "Provide step-by-step guidance on setting up the development environment and running the application."
      }
    ],
    "technical_specifications": {
      "functions_to_implement": [
        "calculateResult",
        "handleInputError"
      ],
      "classes_to_create": [
        "Calculator",
        "UserInterface"
      ],
      "apis_to_create": [],
      "error_handling": [
        "InvalidInputError",
        "DivisionByZeroError"
      ]
    }
  }
]

Provide the tasks in optimal implementation order in JSON format:
{
    "implementation_phases": [
        {
            "phase": 1,
            "name": "Foundation Phase",
            "description": "Core infrastructure and models",
            "tasks": [1, 2, 3]
        },
        {
            "phase": 2,
            "name": "Core Features Phase", 
            "description": "Main functionality",
            "tasks": [4, 5, 6]
        }
    ],
    "ordered_tasks": [
        {
            "implementation_order": 1,
            "task_id": 1,
            "rationale": "Why this task should be implemented first"
        }
    ]
}

IMPLEMENTATION ORDER: