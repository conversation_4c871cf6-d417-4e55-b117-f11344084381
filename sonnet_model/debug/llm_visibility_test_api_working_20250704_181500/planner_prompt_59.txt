You are a project manager. Analyze these tasks and determine the optimal implementation order:

TASKS:
[
  {
    "id": 1,
    "name": "Create Python Script",
    "description": "Develop a Python script that prints 'hello world'. This script will be the foundation for learning basic programming concepts in Python.",
    "file_path": "backend/main.py",
    "dependencies": [],
    "estimated_complexity": "low",
    "requirements": [
      {
        "requirement": "Implement a function named `print_hello_world` that takes no parameters and prints 'hello world' to the console.",
        "implementation_details": "The function should be defined in the main module of the script located at backend/main.py."
      },
      {
        "requirement": "Ensure the script can run standalone without any external dependencies by including a command-line interface that calls `print_hello_world`.",
        "implementation_details": "The script should be executable from the command line, and it should handle basic user input if necessary."
      },
      {
        "requirement": "Include error handling to manage cases where the script is run with incorrect parameters or in an environment that does not support Python.",
        "implementation_details": "Implement a try-except block around the function call to catch and print meaningful error messages for exceptions like `TypeError` or `FileNotFoundError`."
      }
    ],
    "acceptance_criteria": [
      {
        "criteria": "The script should be named 'main.py' and located in the directory 'backend/'.",
        "details": "Ensure that all file paths are correctly specified as per the project structure."
      },
      {
        "criteria": "The function `print_hello_world` must execute without errors when called directly from the command line with no arguments.",
        "details": "Test this by running 'python backend/main.py' in a terminal and verifying that it prints 'hello world'."
      }
    ],
    "technical_specifications": {
      "functions_to_implement": [
        "print_hello_world"
      ],
      "classes_to_create": [],
      "apis_to_create": [],
      "error_handling": [
        "try-except"
      ]
    }
  },
  {
    "id": 2,
    "name": "Write Unit Tests",
    "description": "Implement unit tests for the Python script using pytest. Ensure that the test cases cover all edge cases and scenarios to validate the correctness of the 'hello world' print functionality.",
    "file_path": "tests/test_main.py",
    "dependencies": [
      "Task 1"
    ],
    "estimated_complexity": "low",
    "requirements": [
      "Ensure that pytest is installed and configured in the Python environment used for this task.",
      "Create a test file named 'test_main.py' under the 'tests' directory.",
      "Write at least three unit tests to verify that the script prints 'hello world' correctly."
    ],
    "acceptance_criteria": [
      "The unit tests should pass without any errors when run against a correct implementation of the Python script.",
      "The unit tests must cover scenarios where the output is expected to be printed, such as normal execution and edge cases like empty or incorrect outputs."
    ],
    "technical_specifications": {
      "functions_to_implement": [
        "def test_print_hello_world():"
      ],
      "classes_to_create": [],
      "apis_to_create": [],
      "error_handling": []
    }
  },
  {
    "id": 3,
    "name": "Add Project Documentation",
    "description": "Create a comprehensive README.md file to document the project, including all necessary information for users and developers to understand and interact with the project.",
    "file_path": "docs/README.md",
    "dependencies": [
      "Task 1"
    ],
    "estimated_complexity": "low",
    "requirements": [
      {
        "requirement": "The README.md file must include a clear title such as 'Python Hello World Project Documentation'.",
        "implementation_details": "Use markdown formatting to create the title and ensure it is easily noticeable in the document."
      },
      {
        "requirement": "The documentation should provide an overview of the project, including its purpose, functionality, and any relevant background information.",
        "implementation_details": "Include a section titled 'Project Overview' where you explain what the project does, why it was created, and any specific goals or objectives."
      },
      {
        "requirement": "The README.md must list all dependencies required for the project to run successfully.",
        "implementation_details": "Create a 'Dependencies' section in the documentation where you mention Python version and any other software that needs to be installed prior to running the script."
      }
    ],
    "acceptance_criteria": [
      {
        "criteria": "The README.md file should be well-organized, with clear headings and subheadings.",
        "details": "Ensure there is a logical flow from introduction to installation instructions, usage guidelines, and any other relevant sections."
      },
      {
        "criteria": "All technical specifications such as functions, classes, APIs, and error handling should be clearly documented in the README.md file.",
        "details": "This includes details about how to implement these elements if applicable, ensuring clarity for developers who might want to contribute or modify the project."
      }
    ],
    "technical_specifications": {
      "functions_to_implement": [
        "print('Hello World')"
      ],
      "classes_to_create": [],
      "apis_to_create": [],
      "error_handling": []
    }
  }
]

Provide the tasks in optimal implementation order in JSON format:
{
    "implementation_phases": [
        {
            "phase": 1,
            "name": "Foundation Phase",
            "description": "Core infrastructure and models",
            "tasks": [1, 2, 3]
        },
        {
            "phase": 2,
            "name": "Core Features Phase", 
            "description": "Main functionality",
            "tasks": [4, 5, 6]
        }
    ],
    "ordered_tasks": [
        {
            "implementation_order": 1,
            "task_id": 1,
            "rationale": "Why this task should be implemented first"
        }
    ]
}

IMPLEMENTATION ORDER: