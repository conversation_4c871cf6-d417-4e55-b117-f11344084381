{"interaction_id": 6, "component": "code_generator", "type": "input", "timestamp": "2025-07-04T20:55:29.526666", "prompt": "Improve this backend/api.py file.\n\nDescription: Implement REST API endpoints for the application.\n\nRequirements:\n- Define CRUD operations (`Create`, `Read`, `Update`, `Delete`) for tasks.\n- Integrate authentication middleware to secure the API (e.g., using JWT tokens).\n- Implement error handling for common issues like invalid data, unauthorized access, and server errors.\n\nCurrent code:\n```\nfrom flask import Flask, request, jsonify, abort\nfrom flask_sqlalchemy import SQLAlchemy\nfrom flask_jwt_extended import JWTManager, jwt_required, create_access_token, get_jwt_identity\nfrom werkzeug.security import generate_password_hash, check_password_hash\nimport os\nimport datetime\n\napp = Flask(__name__)\napp.config['SECRET_KEY'] = os.getenv('SECRET_KEY')\napp.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///tasks.db'\napp.config['JWT_ACCESS_TOKEN_EXPIRES'] = datetime.timedelta(days=1)\n\ndb = SQLAlchemy(app)\njwt = JWTManager(app)\n\nclass Task(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(80), nullable=False)\n    description = db.Column(db.String(120))\n    completed = db.Column(db.Boolean, default=False)\n\n    def to_dict(self):\n        return {\n            'id': self.id,\n            'title': self.title,\n            'description': self.description,\n            'completed': self.completed\n        }\n\n@app.before_first_request\ndef create_tables():\n    db.create_all()\n\***********('/register', methods=['POST'])\ndef register():\n    \"\"\"Register a new user.\"\"\"\n    username = request.json.get('username', None)\n    password = request.json.get('password', None)\n    if not username or not password:\n        return jsonify({\"msg\": \"Missing username or password\"}), 400\n\n    hashed_password = generate_password_hash(password, method='sha256')\n    new_user = Task(username=username, password=hashed_password)\n    db.session.add(new_user)\n    db.session.commit()\n    return jsonify({\"msg\": \"User registered successfully\"}), 201\n\***********('/login', methods=['POST'])\ndef login():\n    \"\"\"Login a user and get JWT token.\"\"\"\n    username = request.json.get('username', None)\n    password = request.json.get('password', None)\n    if not username or not password:\n        return jsonify({\"msg\": \"Missing username or password\"}), 400\n\n    user = Task.query.filter_by(username=username).first()\n    if not user or not check_password_hash(user.password, password):\n        return jsonify({\"msg\": \"Bad username or password\"}), 401\n\n    access_token = create_access_token(identity=username)\n    return jsonify(access_token=access_token), 200\n\***********('/tasks', methods=['GET'])\n@jwt_required()\ndef get_tasks():\n    \"\"\"Get all tasks.\"\"\"\n    tasks = Task.query.all()\n    return jsonify([task.to_dict() for task in tasks]), 200\n\***********('/tasks/<int:id>', methods=['GET'])\n@jwt_required()\ndef get_task(id):\n    \"\"\"Get a specific task by ID.\"\"\"\n    task = Task.query.get_or_404(id)\n    return jsonify(task.to_dict()), 200\n\***********('/tasks', methods=['POST'])\n@jwt_required()\ndef create_task():\n    \"\"\"Create a new task.\"\"\"\n    if not request.json:\n        abort(400)\n    title = request.json.get('title')\n    description = request.json.get('description')\n    completed = request.json.get('completed', False)\n    new_task = Task(title=title, description=description, completed=completed)\n    db.session.add(new_task)\n    db.session.commit()\n    return jsonify(new_task.to_dict()), 201\n\***********('/tasks/<int:id>', methods=['PUT'])\n@jwt_required()\ndef update_task(id):\n    \"\"\"Update a task by ID.\"\"\"\n    task = Task.query.get_or_404(id)\n    if not request.json:\n        abort(400)\n    task.title = request.json.get('title', task.title)\n    task.description = request.json.get('description', task.description)\n    task.completed = request.json.get('completed', task.completed)\n    db.session.commit()\n    return jsonify(task.to_dict()), 200\n\***********('/tasks/<int:id>', methods=['DELETE'])\n@jwt_required()\ndef delete_task(id):\n    \"\"\"Delete a task by ID.\"\"\"\n    task = Task.query.get_or_404(id)\n    db.session.delete(task)\n    db.session.commit()\n    return '', 204\n\******************(404)\ndef not_found(error):\n    \"\"\"Return a custom 404 error.\"\"\"\n    return jsonify({'error': 'Not found'}), 404\n\******************(500)\ndef internal_error(error):\n    \"\"\"Return a custom 500 error.\"\"\"\n    db.session.rollback()\n    return jsonify({'error': 'Server error'}), 500\n\nif __name__ == '__main__':\n    app.run(debug=True)\n```\n\nImprove the code to better meet the requirements and fix any issues.\n\nImproved code:", "request_data": {"model": "deepseek-coder-v2:16b", "prompt": "Improve this backend/api.py file.\n\nDescription: Implement REST API endpoints for the application.\n\nRequirements:\n- Define CRUD operations (`Create`, `Read`, `Update`, `Delete`) for tasks.\n- Integrate authentication middleware to secure the API (e.g., using JWT tokens).\n- Implement error handling for common issues like invalid data, unauthorized access, and server errors.\n\nCurrent code:\n```\nfrom flask import Flask, request, jsonify, abort\nfrom flask_sqlalchemy import SQLAlchemy\nfrom flask_jwt_extended import JWTManager, jwt_required, create_access_token, get_jwt_identity\nfrom werkzeug.security import generate_password_hash, check_password_hash\nimport os\nimport datetime\n\napp = Flask(__name__)\napp.config['SECRET_KEY'] = os.getenv('SECRET_KEY')\napp.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///tasks.db'\napp.config['JWT_ACCESS_TOKEN_EXPIRES'] = datetime.timedelta(days=1)\n\ndb = SQLAlchemy(app)\njwt = JWTManager(app)\n\nclass Task(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(80), nullable=False)\n    description = db.Column(db.String(120))\n    completed = db.Column(db.Boolean, default=False)\n\n    def to_dict(self):\n        return {\n            'id': self.id,\n            'title': self.title,\n            'description': self.description,\n            'completed': self.completed\n        }\n\n@app.before_first_request\ndef create_tables():\n    db.create_all()\n\***********('/register', methods=['POST'])\ndef register():\n    \"\"\"Register a new user.\"\"\"\n    username = request.json.get('username', None)\n    password = request.json.get('password', None)\n    if not username or not password:\n        return jsonify({\"msg\": \"Missing username or password\"}), 400\n\n    hashed_password = generate_password_hash(password, method='sha256')\n    new_user = Task(username=username, password=hashed_password)\n    db.session.add(new_user)\n    db.session.commit()\n    return jsonify({\"msg\": \"User registered successfully\"}), 201\n\***********('/login', methods=['POST'])\ndef login():\n    \"\"\"Login a user and get JWT token.\"\"\"\n    username = request.json.get('username', None)\n    password = request.json.get('password', None)\n    if not username or not password:\n        return jsonify({\"msg\": \"Missing username or password\"}), 400\n\n    user = Task.query.filter_by(username=username).first()\n    if not user or not check_password_hash(user.password, password):\n        return jsonify({\"msg\": \"Bad username or password\"}), 401\n\n    access_token = create_access_token(identity=username)\n    return jsonify(access_token=access_token), 200\n\***********('/tasks', methods=['GET'])\n@jwt_required()\ndef get_tasks():\n    \"\"\"Get all tasks.\"\"\"\n    tasks = Task.query.all()\n    return jsonify([task.to_dict() for task in tasks]), 200\n\***********('/tasks/<int:id>', methods=['GET'])\n@jwt_required()\ndef get_task(id):\n    \"\"\"Get a specific task by ID.\"\"\"\n    task = Task.query.get_or_404(id)\n    return jsonify(task.to_dict()), 200\n\***********('/tasks', methods=['POST'])\n@jwt_required()\ndef create_task():\n    \"\"\"Create a new task.\"\"\"\n    if not request.json:\n        abort(400)\n    title = request.json.get('title')\n    description = request.json.get('description')\n    completed = request.json.get('completed', False)\n    new_task = Task(title=title, description=description, completed=completed)\n    db.session.add(new_task)\n    db.session.commit()\n    return jsonify(new_task.to_dict()), 201\n\***********('/tasks/<int:id>', methods=['PUT'])\n@jwt_required()\ndef update_task(id):\n    \"\"\"Update a task by ID.\"\"\"\n    task = Task.query.get_or_404(id)\n    if not request.json:\n        abort(400)\n    task.title = request.json.get('title', task.title)\n    task.description = request.json.get('description', task.description)\n    task.completed = request.json.get('completed', task.completed)\n    db.session.commit()\n    return jsonify(task.to_dict()), 200\n\***********('/tasks/<int:id>', methods=['DELETE'])\n@jwt_required()\ndef delete_task(id):\n    \"\"\"Delete a task by ID.\"\"\"\n    task = Task.query.get_or_404(id)\n    db.session.delete(task)\n    db.session.commit()\n    return '', 204\n\******************(404)\ndef not_found(error):\n    \"\"\"Return a custom 404 error.\"\"\"\n    return jsonify({'error': 'Not found'}), 404\n\******************(500)\ndef internal_error(error):\n    \"\"\"Return a custom 500 error.\"\"\"\n    db.session.rollback()\n    return jsonify({'error': 'Server error'}), 500\n\nif __name__ == '__main__':\n    app.run(debug=True)\n```\n\nImprove the code to better meet the requirements and fix any issues.\n\nImproved code:", "stream": false, "options": {"temperature": 0.2, "top_p": 0.9, "num_predict": 3072}}, "url": "http://localhost:11434/api/generate"}