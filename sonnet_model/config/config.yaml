system:
  name: "Sonnet Model"
  version: "0.1.0"
  log_level: "INFO"
  environment: "development"  # development, testing, production
  debug_enabled: false  # Enable LLM visibility debug mode

task_manager:
  max_concurrent_tasks: 3
  task_timeout_seconds: 600
  retry_attempts: 3
  retry_delay_seconds: 30
  priority_levels: 5
  database:
    type: "sqlite"  # sqlite, postgres
    path: "data/sonnet.db"
    # For postgres:
    # host: "localhost"
    # port: 5432
    # username: "sonnet"
    # password: "password"
    # database: "sonnet"

code_generator:
  llm:
    # LLM Provider Configuration
    # Supported types: "http_api" (Ollama), "openai", "anthropic", "azure_openai", "google_palm"
    type: "http_api"  # Change to "openai", "anthropic", etc. for cloud providers

    # Local LLM Configuration (Ollama)
    api_url: "http://localhost:11434/api/generate"
    model: "deepseek-coder-v2:16b"

    # Cloud LLM Configuration (uncomment and configure as needed)
    # OpenAI Configuration
    # api_key: "${OPENAI_API_KEY}"  # Set via environment variable
    # model: "gpt-4"  # or "gpt-3.5-turbo", "gpt-4-turbo"
    # organization: "${OPENAI_ORG_ID}"  # Optional

    # Anthropic Configuration
    # api_key: "${ANTHROPIC_API_KEY}"  # Set via environment variable
    # model: "claude-3-sonnet-20240229"  # or "claude-3-opus-20240229", "claude-3-haiku-20240307"

    # Azure OpenAI Configuration
    # api_key: "${AZURE_OPENAI_API_KEY}"
    # api_base: "${AZURE_OPENAI_ENDPOINT}"
    # api_version: "2023-12-01-preview"
    # model: "gpt-4"
    # deployment_name: "${AZURE_DEPLOYMENT_NAME}"

    # Google PaLM Configuration
    # api_key: "${GOOGLE_PALM_API_KEY}"
    # model: "text-bison-001"

    # Common LLM Parameters
    temperature: 0.2
    max_tokens: 4096
    timeout: 300

  # Conversation Management
  conversation:
    # Dynamic conversation length management
    max_length_mode: "dynamic"  # "fixed", "dynamic", "signal_based"
    max_length_fixed: 50  # Used when mode is "fixed"
    max_length_dynamic_min: 30  # Minimum length before considering reset
    max_length_dynamic_max: 100  # Maximum length before forced reset

    # Signal-based conversation management
    reset_signals:
      - "conversation is getting long"
      - "context is full"
      - "running out of space"
      - "message limit"
      - "too many messages"
      - "context window full"

    # Context preservation during reset
    preserve_context: true
    context_summary_length: 1000  # Characters to preserve in summary

  timeout_seconds: 120
  max_retries: 2
  cache_enabled: true
  cache_ttl_seconds: 3600

critique_engine:
  local_llm:
    enabled: true
    # Same LLM provider options as code_generator
    type: "http_api"  # Change to "openai", "anthropic", etc. for cloud providers

    # Local LLM Configuration
    api_url: "http://localhost:11434/api/generate"
    model: "deepseek-coder-v2:16b"  # Primary model for critique
    fallback_model: "deepseek-coder-v2:16b"

    # Cloud LLM Configuration (same options as code_generator)
    # api_key: "${OPENAI_API_KEY}" or "${ANTHROPIC_API_KEY}"
    # model: "gpt-4" or "claude-3-sonnet-20240229"

    context_window: 8192
    temperature: 0.1
    max_tokens: 2048
    timeout: 300

  # Advanced Conversation Management for Critique Engine
  conversation_management:
    # Dynamic conversation length based on LLM signals
    max_conversation_length_mode: "signal_based"  # "fixed", "dynamic", "signal_based"
    max_conversation_length_fixed: 50  # Legacy fixed limit
    max_conversation_length_dynamic_min: 20
    max_conversation_length_dynamic_max: 80

    # LLM signal detection for conversation reset
    reset_on_signals: true
    conversation_reset_signals:
      - "conversation is getting long"
      - "context is full"
      - "running out of space"
      - "message limit"
      - "too many messages"
      - "context window"
      - "token limit"
      - "memory full"

    # Context preservation strategy
    context_preservation:
      enabled: true
      method: "intelligent_summary"  # "intelligent_summary", "key_points", "full_context"
      summary_max_tokens: 1500
      preserve_recent_exchanges: 5  # Always preserve last N exchanges
      preserve_critical_info: true  # Preserve project state, requirements, etc.
  static_analysis:
    enabled: true
    tools:
      python:
        - "pylint"
        - "mypy"
        - "bandit"
        - "black"
      javascript:
        - "eslint"
        - "tsc"
      general:
        - "sonarqube"
    max_line_length: 88
    strictness_level: "medium"  # low, medium, high
  testing:
    enabled: true
    frameworks:
      python:
        - "pytest"
      javascript:
        - "jest"
    timeout_seconds: 60
    max_test_cases: 10
  feedback:
    detail_level: "high"  # low, medium, high
    include_code_snippets: true
    include_references: true
    max_issues_to_report: 10

api:
  host: "0.0.0.0"
  port: 8000
  debug: false
  cors_origins:
    - "http://localhost:3000"
    - "http://localhost:8080"
  rate_limit:
    enabled: true
    requests_per_minute: 60
  auth:
    enabled: false
    # jwt_secret: "your-secret-key"
    # token_expiry_minutes: 60

message_bus:
  type: "redis"  # redis, rabbitmq, memory
  host: "localhost"
  port: 6379
  # For RabbitMQ:
  # username: "guest"
  # password: "guest"
  # vhost: "/"
  channel_prefetch: 10
  message_ttl_seconds: 3600

monitoring:
  enabled: true
  prometheus:
    enabled: true
    port: 9090
  logging:
    file: "logs/sonnet.log"
    max_size_mb: 10
    backup_count: 5
    format: "json"  # json, text

resources:
  cpu_limit: 0.8  # 80% of available CPU
  memory_limit_gb: 16
  gpu_enabled: true
  gpu_memory_limit_gb: 20
