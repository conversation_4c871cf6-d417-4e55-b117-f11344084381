"""
INTEGRATED PROJECT GENERATOR

This is the complete system that combines:
1. Task Planner LLM (breaks down high-level descriptions)
2. Fixed Iterative Improvement System (real feedback loop)
3. Project Generation (creates actual files)

This is exactly what you wanted - a system that takes a simple description
and creates a complete working project with real iterative improvement!
"""

import asyncio
import json
import logging
import hashlib
import shutil
from pathlib import Path
from typing import Dict, Any, List, Tuple
import httpx

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class IntegratedProjectGenerator:
    """Complete project generator with task planning and iterative improvement"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model = "deepseek-coder-v2:16b"

        # Quality settings
        self.quality_threshold = 8.0  # Achievable but high quality
        self.max_iterations_per_file = 6
        self.min_improvement = 0.3

        # Debug support
        self.debug_callback = None
        self.interaction_counter = 0

    def set_debug_callback(self, callback):
        """Set debug callback for LLM interaction logging"""
        self.debug_callback = callback
        self.logger.info("🔍 Debug callback set for IntegratedProjectGenerator")

    async def generate_complete_project(self, project_description: str) -> Dict[str, Any]:
        """Generate a complete project from high-level description"""

        # Store project description for critique engine
        self._current_project_description = project_description

        print("🚀 INTEGRATED PROJECT GENERATOR")
        print("=" * 70)
        print(f"📝 Project: {project_description}")
        print("=" * 70)
        
        # Step 1: Create task plan
        print("\n📋 STEP 1: CREATING TASK PLAN")
        print("-" * 40)
        
        task_plan = await self._create_task_plan(project_description)
        
        if not task_plan or not task_plan.get('tasks'):
            print("❌ Task planning failed")
            return {"success": False, "error": "Task planning failed"}
        
        print(f"✅ Created plan with {len(task_plan['tasks'])} tasks")
        
        # Step 2: Setup project structure
        print("\n📁 STEP 2: SETTING UP PROJECT")
        print("-" * 40)
        
        project_path = self._setup_project_structure(task_plan['name'])
        
        # Step 3: Generate files with iterative improvement
        print("\n🔄 STEP 3: GENERATING FILES WITH ITERATIVE IMPROVEMENT")
        print("-" * 40)
        
        results = await self._generate_all_files(task_plan['tasks'], project_path)
        
        # Step 4: Create project summary
        summary = self._create_project_summary(task_plan, results, project_path)
        
        print(f"\n🎉 PROJECT GENERATION COMPLETED!")
        print(f"📁 Project created in: {project_path}")
        print(f"✅ Success rate: {results['success_rate']:.1f}%")
        
        return summary
    
    async def _create_task_plan(self, description: str) -> Dict[str, Any]:
        """Create task plan using LLM"""

        # Check if this is a simple request
        if self._is_simple_request(description):
            return self._create_simple_task_plan(description)

        prompt = f"""Break down this project into specific implementation tasks:

PROJECT: {description}

Analyze the project description and create only the necessary tasks to fulfill the request.
For simple requests (like utility functions), create minimal file structures.
For complex applications, create appropriate multi-file architectures.

For each task, provide:
- File path (appropriate to the project type)
- Description of what the file does
- 3-4 specific requirements

Format as a simple list:

TASK 1:
File: [appropriate file path]
Description: [what this file accomplishes]
Requirements:
- [specific requirement 1]
- [specific requirement 2]
- [specific requirement 3]

Continue for all necessary files to complete the project...

TASK BREAKDOWN:"""

        response = await self._send_llm_request(prompt, "planner")

        if not response:
            return {}

        # Parse tasks
        tasks = self._parse_task_response(response)

        # Validate scope alignment
        validated_tasks = self._validate_scope_alignment(description, tasks)

        # Create project structure
        project_name = self._extract_project_name(description)

        return {
            "name": project_name,
            "description": description,
            "tasks": validated_tasks
        }

    def _is_simple_request(self, description: str) -> bool:
        """Detect if this is a simple single-file request"""
        description_lower = description.lower()

        # Simple request indicators
        simple_indicators = [
            'simple', 'utility', 'function', 'helper', 'basic',
            'single', 'small', 'quick', 'minimal'
        ]

        # Complex request indicators
        complex_indicators = [
            'application', 'app', 'system', 'platform', 'service',
            'api', 'database', 'web', 'server', 'backend', 'frontend',
            'authentication', 'user management', 'crud', 'rest',
            'microservice', 'architecture'
        ]

        # Count indicators
        simple_count = sum(1 for indicator in simple_indicators if indicator in description_lower)
        complex_count = sum(1 for indicator in complex_indicators if indicator in description_lower)

        # Simple if:
        # 1. Has simple indicators and no complex indicators
        # 2. Description is short (< 50 words)
        # 3. Contains function/utility keywords
        word_count = len(description.split())

        return (simple_count > 0 and complex_count == 0) or \
               (word_count < 50 and ('function' in description_lower or 'utility' in description_lower))

    def _create_simple_task_plan(self, description: str) -> Dict[str, Any]:
        """Create a simple task plan for basic requests"""

        # Extract the main functionality
        project_name = self._extract_project_name(description)

        # Determine file type and path
        if 'math' in description.lower():
            file_path = f"{project_name}/math_utils.py"
            file_description = "Mathematical utility functions"
        elif 'string' in description.lower() or 'text' in description.lower():
            file_path = f"{project_name}/string_utils.py"
            file_description = "String manipulation utility functions"
        elif 'file' in description.lower():
            file_path = f"{project_name}/file_utils.py"
            file_description = "File handling utility functions"
        else:
            file_path = f"{project_name}/utils.py"
            file_description = "Utility functions"

        # Create single task
        task = {
            "path": file_path,
            "description": file_description,
            "requirements": [
                f"Implement the functionality described: {description}",
                "Include proper error handling and input validation",
                "Add comprehensive documentation with docstrings",
                "Include basic unit tests within the file"
            ]
        }

        return {
            "name": project_name,
            "description": description,
            "tasks": [task]
        }

    def _validate_scope_alignment(self, description: str, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate that generated tasks align with user request scope"""

        if not tasks:
            return tasks

        description_lower = description.lower()
        validated_tasks = []

        # Define relevance keywords for different types of requests
        relevance_map = {
            'math': ['math', 'calculate', 'arithmetic', 'number', 'formula', 'equation'],
            'string': ['string', 'text', 'parse', 'format', 'regex', 'character'],
            'file': ['file', 'read', 'write', 'csv', 'json', 'data'],
            'utility': ['utility', 'helper', 'function', 'tool'],
            'web': ['web', 'http', 'api', 'server', 'endpoint', 'route'],
            'database': ['database', 'db', 'model', 'table', 'sql', 'query'],
            'auth': ['auth', 'login', 'user', 'password', 'token', 'session']
        }

        # Identify request type
        request_types = []
        for req_type, keywords in relevance_map.items():
            if any(keyword in description_lower for keyword in keywords):
                request_types.append(req_type)

        # If no specific type detected, assume utility
        if not request_types:
            request_types = ['utility']

        # Validate each task
        for task in tasks:
            task_path = task.get('path', '').lower()
            task_desc = task.get('description', '').lower()
            task_reqs = ' '.join(task.get('requirements', [])).lower()
            task_content = f"{task_path} {task_desc} {task_reqs}"

            # Check if task is relevant to any identified request type
            is_relevant = False
            for req_type in request_types:
                type_keywords = relevance_map[req_type]
                if any(keyword in task_content for keyword in type_keywords):
                    is_relevant = True
                    break

            # For simple requests, also check if task matches core functionality
            if not is_relevant and len(tasks) <= 2:
                # For simple requests, be more lenient
                core_words = [word for word in description_lower.split()
                             if len(word) > 3 and word.isalpha()]
                if any(word in task_content for word in core_words):
                    is_relevant = True

            # Add task if relevant
            if is_relevant:
                validated_tasks.append(task)
            else:
                self.logger.warning(f"🚨 Filtered out irrelevant task: {task.get('path', 'unknown')}")
                self.logger.warning(f"   Task description: {task.get('description', 'N/A')}")
                self.logger.warning(f"   Request types: {request_types}")

        # Ensure we have at least one task
        if not validated_tasks and tasks:
            self.logger.warning("⚠️ All tasks filtered out, keeping first task to prevent empty project")
            validated_tasks = [tasks[0]]

        # Log validation results
        if len(validated_tasks) < len(tasks):
            self.logger.info(f"📊 Scope validation: {len(validated_tasks)}/{len(tasks)} tasks kept")
            self.logger.info(f"   Request types detected: {request_types}")

        return validated_tasks

    def _parse_task_response(self, response: str) -> List[Dict[str, Any]]:
        """Parse task response from LLM"""
        
        tasks = []
        lines = response.split('\n')
        current_task = {}
        current_requirements = []
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('TASK ') and ':' in line:
                # Save previous task
                if current_task and current_requirements:
                    current_task['requirements'] = current_requirements.copy()
                    tasks.append(current_task.copy())
                
                # Start new task
                current_task = {}
                current_requirements = []
                
            elif line.startswith('File:'):
                current_task['path'] = line.replace('File:', '').strip()
                
            elif line.startswith('Description:'):
                current_task['description'] = line.replace('Description:', '').strip()
                
            elif line.startswith('Requirements:'):
                current_requirements = []
                
            elif line.startswith('- ') and current_task:
                requirement = line.replace('- ', '').strip()
                if requirement:
                    current_requirements.append(requirement)
        
        # Add the last task
        if current_task and current_requirements:
            current_task['requirements'] = current_requirements
            tasks.append(current_task)
        
        return tasks
    
    def _extract_project_name(self, description: str) -> str:
        """Extract project name from description"""
        words = description.lower().split()[:3]
        name = '_'.join(word.strip('.,!?') for word in words if word.isalpha())
        return name or "generated_project"
    
    def _setup_project_structure(self, project_name: str) -> Path:
        """Setup project directory structure"""
        
        project_path = Path(project_name)
        
        # Remove existing project
        if project_path.exists():
            shutil.rmtree(project_path)
        
        # Create project directory
        project_path.mkdir()
        
        print(f"📁 Created project directory: {project_path}")
        return project_path
    
    async def _generate_all_files(self, tasks: List[Dict[str, Any]], 
                                project_path: Path) -> Dict[str, Any]:
        """Generate all files with iterative improvement"""
        
        results = {
            "total_files": len(tasks),
            "successful_files": 0,
            "total_iterations": 0,
            "file_results": []
        }
        
        for i, task in enumerate(tasks, 1):
            print(f"\n📄 GENERATING FILE {i}/{len(tasks)}: {task['path']}")
            print(f"📝 {task['description']}")
            print("-" * 50)
            
            success, iterations, final_code = await self._generate_single_file(
                task, project_path
            )
            
            file_result = {
                "path": task['path'],
                "success": success,
                "iterations": iterations,
                "code_length": len(final_code) if final_code else 0
            }
            
            results["file_results"].append(file_result)
            results["total_iterations"] += iterations
            
            if success:
                results["successful_files"] += 1
                print(f"✅ SUCCESS: {task['path']} ({iterations} iterations)")
            else:
                print(f"❌ FAILED: {task['path']} ({iterations} iterations)")
        
        results["success_rate"] = (results["successful_files"] / results["total_files"]) * 100
        return results
    
    async def _generate_single_file(self, task: Dict[str, Any], 
                                   project_path: Path) -> Tuple[bool, int, str]:
        """Generate a single file with iterative improvement"""
        
        file_path = project_path / task['path']
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        iteration = 1
        current_code = ""
        previous_quality = 0
        
        while iteration <= self.max_iterations_per_file:
            print(f"🔄 Iteration {iteration}")
            
            # Generate code
            new_code = await self._generate_file_code(task, current_code, iteration)
            
            if not new_code:
                print("❌ Code generation failed")
                return False, iteration, current_code
            
            # Write to file
            with open(file_path, 'w') as f:
                f.write(new_code)
            
            print(f"📄 Generated: {len(new_code)} chars")
            
            # Critique code
            critique = await self._critique_file_code(new_code, task)
            
            if not critique:
                print("❌ Critique failed")
                return False, iteration, new_code
            
            current_quality = critique.get('quality_score', 0)
            issues = critique.get('issues', [])
            
            print(f"📊 Quality: {current_quality:.1f}/10, Issues: {len(issues)}")
            
            # Check if quality threshold is met
            if current_quality >= self.quality_threshold:
                print(f"✅ Quality threshold reached!")
                return True, iteration, new_code
            
            # Check for improvement
            quality_improvement = current_quality - previous_quality
            if iteration > 2 and quality_improvement < self.min_improvement:
                print(f"⚠️ Insufficient improvement ({quality_improvement:.1f})")
            
            current_code = new_code
            previous_quality = current_quality
            iteration += 1
        
        print(f"⚠️ Max iterations reached")
        return current_quality >= (self.quality_threshold - 1), iteration - 1, current_code
    
    async def _generate_file_code(self, task: Dict[str, Any], 
                                existing_code: str, iteration: int) -> str:
        """Generate code for a file"""
        
        if iteration == 1:
            prompt = f"""Create a high-quality {task['path']} file.

Description: {task['description']}

Requirements:
{chr(10).join(f"- {req}" for req in task['requirements'])}

Create complete, production-ready code with:
- Proper error handling
- Documentation
- Type hints (for Python)
- Best practices

Code:"""
        else:
            prompt = f"""Improve this {task['path']} file.

Description: {task['description']}

Requirements:
{chr(10).join(f"- {req}" for req in task['requirements'])}

Current code:
```
{existing_code}
```

Improve the code to better meet the requirements and fix any issues.

Improved code:"""
        
        response = await self._send_llm_request(prompt, "code_generator")
        return self._extract_code_from_response(response, task['path'])
    
    async def _critique_file_code(self, code: str, task: Dict[str, Any]) -> Dict[str, Any]:
        """Critique file code with enhanced relevance validation"""

        # Get original project description for relevance checking
        original_description = getattr(self, '_current_project_description', 'N/A')

        prompt = f"""Analyze this {task['path']} file and provide comprehensive feedback.

ORIGINAL PROJECT REQUEST: {original_description}

FILE DESCRIPTION: {task['description']}

REQUIREMENTS:
{chr(10).join(f"- {req}" for req in task['requirements'])}

CODE:
```
{code}
```

Evaluate the code on multiple dimensions:

1. RELEVANCE: Does this file directly address the original project request?
2. SCOPE APPROPRIATENESS: Is the complexity appropriate for the request?
3. REQUIREMENT FULFILLMENT: Does it meet all specified requirements?
4. CODE QUALITY: Is the code well-written, documented, and maintainable?
5. ERROR HANDLING: Are edge cases and errors properly handled?

Provide analysis in JSON format:
{{
    "quality_score": <1-10>,
    "relevance_score": <1-10>,
    "scope_appropriateness": <1-10>,
    "requirement_fulfillment": <1-10>,
    "issues": ["specific issue 1", "specific issue 2"],
    "suggestions": ["suggestion 1", "suggestion 2"],
    "relevance_concerns": ["concern 1 if any", "concern 2 if any"]
}}

Be specific and actionable in your feedback. Flag any code that seems unrelated to the original request.

Analysis:"""

        response = await self._send_llm_request(prompt, "critique_engine")
        critique = self._parse_json_response(response)

        # Enhanced quality calculation considering relevance
        if critique:
            quality_score = critique.get('quality_score', 0)
            relevance_score = critique.get('relevance_score', 0)
            scope_score = critique.get('scope_appropriateness', 0)
            requirement_score = critique.get('requirement_fulfillment', 0)

            # Weighted overall score (relevance is most important)
            overall_score = (
                relevance_score * 0.4 +      # 40% weight on relevance
                requirement_score * 0.3 +    # 30% weight on requirements
                quality_score * 0.2 +        # 20% weight on code quality
                scope_score * 0.1            # 10% weight on scope
            )

            critique['quality_score'] = overall_score

            # Add relevance warnings to issues if needed
            if relevance_score < 7:
                relevance_concerns = critique.get('relevance_concerns', [])
                if relevance_concerns:
                    critique['issues'] = critique.get('issues', []) + [
                        f"RELEVANCE CONCERN: {concern}" for concern in relevance_concerns
                    ]

        return critique
    
    def _create_project_summary(self, task_plan: Dict[str, Any], 
                               results: Dict[str, Any], project_path: Path) -> Dict[str, Any]:
        """Create project summary"""
        
        summary = {
            "success": True,
            "project_name": task_plan['name'],
            "project_path": str(project_path),
            "description": task_plan['description'],
            "statistics": {
                "total_files": results['total_files'],
                "successful_files": results['successful_files'],
                "success_rate": results['success_rate'],
                "total_iterations": results['total_iterations'],
                "average_iterations": results['total_iterations'] / results['total_files']
            },
            "files": results['file_results']
        }
        
        # Save summary
        summary_path = project_path / "PROJECT_SUMMARY.json"
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        return summary
    
    async def _send_llm_request(self, prompt: str, component: str = "planner") -> str:
        """Send request to LLM with debug capture"""
        self.interaction_counter += 1

        # Prepare request data
        request_data = {
            "model": self.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.2,
                "top_p": 0.9,
                "num_predict": 3072
            }
        }

        # Save debug input
        if self.debug_callback:
            self.debug_callback(f"llm_{self.interaction_counter:03d}_{component}_input.json", {
                "interaction_id": self.interaction_counter,
                "component": component,
                "type": "input",
                "timestamp": "2025-07-04T20:55:29.526666",  # Will be updated by callback
                "prompt": prompt,
                "request_data": request_data,
                "url": self.ollama_url
            })

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json=request_data,
                    timeout=120.0
                )

                if response.status_code == 200:
                    result = response.json()
                    llm_response = result.get("response", "")

                    # Save debug output
                    if self.debug_callback:
                        self.debug_callback(f"llm_{self.interaction_counter:03d}_{component}_output.json", {
                            "interaction_id": self.interaction_counter,
                            "component": component,
                            "type": "output",
                            "timestamp": "2025-07-04T20:55:29.526666",  # Will be updated by callback
                            "response": llm_response,
                            "full_response": result,
                            "url": self.ollama_url
                        })

                    return llm_response
                else:
                    return ""

        except Exception as e:
            self.logger.error(f"LLM request failed: {e}")
            return ""
    
    def _extract_code_from_response(self, response: str, file_path: str) -> str:
        """Extract code from LLM response"""
        
        # Determine language
        if file_path.endswith('.py'):
            patterns = ["```python", "```"]
        elif file_path.endswith('.js'):
            patterns = ["```javascript", "```js", "```"]
        elif file_path.endswith('.html'):
            patterns = ["```html", "```"]
        elif file_path.endswith('.css'):
            patterns = ["```css", "```"]
        else:
            patterns = ["```"]
        
        # Try each pattern
        for pattern in patterns:
            if pattern in response:
                start = response.find(pattern) + len(pattern)
                if pattern != "```":
                    start = response.find("\n", start) + 1
                end = response.find("```", start)
                if end != -1:
                    return response[start:end].strip()
        
        return response.strip()
    
    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """Parse JSON response"""
        try:
            start = response.find('{')
            end = response.rfind('}') + 1
            
            if start != -1 and end > start:
                json_str = response[start:end]
                return json.loads(json_str)
        except:
            pass
        
        return {
            "quality_score": 5,
            "issues": ["Could not parse critique"],
            "suggestions": ["Manual review needed"]
        }


async def test_integrated_generator():
    """Test the complete integrated system"""
    
    print("🎯 TESTING INTEGRATED PROJECT GENERATOR")
    print("This will create a complete project with real iterative improvement!")
    print("=" * 70)
    
    generator = IntegratedProjectGenerator()
    
    # Test project
    project_description = "Create a simple blog platform with user authentication and post management"
    
    # Generate complete project
    result = await generator.generate_complete_project(project_description)
    
    print(f"\n📊 FINAL RESULTS:")
    print(f"✅ Success: {result.get('success', False)}")
    print(f"📁 Project: {result.get('project_name', 'N/A')}")
    print(f"📄 Files: {result.get('statistics', {}).get('successful_files', 0)}/{result.get('statistics', {}).get('total_files', 0)}")
    print(f"📈 Success Rate: {result.get('statistics', {}).get('success_rate', 0):.1f}%")
    print(f"🔄 Total Iterations: {result.get('statistics', {}).get('total_iterations', 0)}")
    
    return result


async def main():
    """Run the integrated generator test"""
    
    result = await test_integrated_generator()
    
    print(f"\n🎉 INTEGRATED PROJECT GENERATOR TEST COMPLETED!")
    
    if result.get('success'):
        print("✅ The complete system works!")
        print("🎯 High-level description → Task breakdown → Iterative improvement → Working project")
    else:
        print("⚠️ System needs refinement")


if __name__ == "__main__":
    asyncio.run(main())
