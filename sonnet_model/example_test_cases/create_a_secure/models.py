# models.py
from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import Min<PERSON>engthValida<PERSON>, EmailValidator

class User(AbstractUser):
    """ Custom user model with additional fields and methods """
    
    # Additional fields can be added here
    email = models.EmailField(unique=True)
    phone_number = models.CharField(max_length=15, unique=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    def __str__(self):
        return self.username

class CoreDataModel(models.Model):
    """ Core data model example with relationships and constraints """
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

class Product(CoreDataModel):
    """ Model representing a product with relationships and constraints """
    
    price = models.DecimalField(max_digits=10, decimal_places=2)
    stock = models.PositiveIntegerField()

    def clean(self):
        if self.price <= 0:
            raise ValidationError({'price': 'Price must be greater than zero.'})
        if self.stock < 0:
            raise ValidationError({'stock': 'Stock cannot be negative.'})

    def save(self, *args, **kwargs):
        self.full_clean()  # Run validation before saving
        super().save(*args, **kwargs)

class Order(CoreDataModel):
    """ Model representing an order with relationships and constraints """
    
    customer = models.ForeignKey(User, on_delete=models.CASCADE)
    products = models.ManyToManyField(Product, through='OrderItem')
    total_price = models.DecimalField(max_digits=10, decimal_places=2, editable=False)

    def clean(self):
        if not self.products.exists():
            raise ValidationError({'products': 'An order must have at least one product.'})

    def save(self, *args, **kwargs):
        self.full_clean()  # Run validation before saving
        super().save(*args, **kwargs)
        self.total_price = sum(item.product.price for item in self.orderitem_set.all())
        self.save()

class OrderItem(models.Model):
    """ Intermediate model representing items in an order """
    
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField()
    price = models.DecimalField(max_digits=10, decimal_places=2)

    def clean(self):
        if self.quantity <= 0:
            raise ValidationError({'quantity': 'Quantity must be greater than zero.'})
        if self.price != self.product.price:
            raise ValidationError({'price': 'Price must match the product price.'})

    def save(self, *args, **kwargs):
        self.full_clean()  # Run validation before saving
        super().save(*args, **kwargs)

# Example usage of custom validators in a Django management command or elsewhere
from django.core.management.base import BaseCommand
from django.core.exceptions import ValidationError

class Command(BaseCommand):
    help = 'Validate and create products'

    def handle(self, *args, **kwargs):
        try:
            product = Product(name='Test Product', price=0, stock=-1)
            product.full_clean()  # This will trigger the clean method with validation errors
            product.save()
        except ValidationError as e:
            self.stdout.write(self.style.ERROR('Validation error: %s' % str(e)))
        else:
            self.stdout.write(self.style.SUCCESS('Product created successfully'))