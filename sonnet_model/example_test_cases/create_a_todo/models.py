# models.py
from django.db import models
from django.contrib.auth.models import AbstractUser

class User(AbstractUser):
    """Custom User model with additional fields"""
    
    def __str__(self):
        return self.username

class Task(models.Model):
    """Task model with status and priority"""
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('in_progress', 'In Progress'),
    ]
    
    PRIORITY_CHOICES = [
        (1, 'Low'),
        (2, 'Medium'),
        (3, 'High'),
    ]
    
    title = models.CharField(max_length=200)
    description = models.TextField()
    status = models.CharField(max_length=50, choices=STATUS_CHOICES)
    priority = models.Integer<PERSON>ield(choices=PRIORITY_CHOICES)
    assigned_to = models.ForeignKey(User, on_delete=models.CASCADE, related_name='tasks')
    
    def __str__(self):
        return self.title

# Example usage:
# from .models import User, Task
# user = User.objects.create(username='john_doe', email='<EMAIL>')
# task = Task.objects.create(title='Task 1', description='Description of Task 1', status='pending', priority=2, assigned_to=user)