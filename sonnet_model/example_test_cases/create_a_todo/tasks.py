"""
tasks.py: Task management functionality

This module provides CRUD operations for tasks, task status updates, task filtering and search, and due date management.
"""

import uuid
from datetime import datetime

class TaskManager:
    def __init__(self):
        self.tasks = []

    def create_task(self, title, description, due_date=None):
        """Create a new task."""
        if not isinstance(title, str) or len(title) == 0:
            raise ValueError("Title must be a non-empty string.")
        if not isinstance(description, str):
            raise ValueError("Description must be a string.")
        if due_date and not isinstance(due_date, datetime):
            raise ValueError("Due date must be a datetime object or None.")
        
        task = {
            'id': str(uuid.uuid4()),
            'title': title,
            'description': description,
            'status': 'pending',
            'due_date': due_date
        }
        self.tasks.append(task)
        return task['id']

    def read_task(self, task_id):
        """Read a task by its ID."""
        task = next((task for task in self.tasks if task['id'] == task_id), None)
        if task is None:
            raise ValueError("Task not found.")
        return task

    def update_task(self, task_id, title=None, description=None, status=None, due_date=None):
        """Update an existing task."""
        task = next((task for task in self.tasks if task['id'] == task_id), None)
        if task is None:
            raise ValueError("Task not found.")
        
        if title is not None and isinstance(title, str) and len(title) > 0:
            task['title'] = title
        if description is not None and isinstance(description, str):
            task['description'] = description
        if status is not None and status in ['pending', 'completed']:
            task['status'] = status
        if due_date is not None and isinstance(due_date, datetime):
            task['due_date'] = due_date
        
        return True

    def delete_task(self, task_id):
        """Delete a task by its ID."""
        self.tasks = [task for task in self.tasks if task['id'] != task_id]
        return True

    def list_tasks(self, status=None, query=None):
        """List tasks with optional filtering by status and/or search query."""
        filtered_tasks = self.tasks
        if status:
            filtered_tasks = [task for task in filtered_tasks if task['status'] == status]
        if query:
            filtered_tasks = [task for task in filtered_tasks if query.lower() in task['title'].lower()]
        return filtered_tasks

# Example usage:
if __name__ == "__main__":
    manager = TaskManager()
    task_id = manager.create_task("Buy groceries", "Don't forget the milk and bread.", datetime(2023, 10, 15))
    print(f"Created task with ID: {task_id}")
    
    task = manager.read_task(task_id)
    print(f"Read task: {task}")
    
    manager.update_task(task_id, status="completed")
    print("Updated task status to completed.")
    
    tasks = manager.list_tasks(status="completed")
    print("Filtered tasks by status 'completed':", tasks)
    
    manager.delete_task(task_id)
    print("Deleted the task.")