# models.py
from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _
import datetime

class User(AbstractUser):
    """ Custom user model with additional fields """
    
    # Add any additional fields here
    bio = models.TextField(_('bio'), max_length=500, blank=True)
    
    def __str__(self):
        return self.username

class Post(models.Model):
    """ Blog post model with title, content, author relationship and timestamps """
    
    title = models.CharField(_('title'), max_length=200)
    content = models.TextField(_('content'))
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='posts')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.title
    
    class Meta:
        ordering = ['-created_at']  # Order posts by creation date in descending order