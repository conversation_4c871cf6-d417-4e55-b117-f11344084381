import json
from flask import Flask, request, jsonify, abort

app = Flask(__name__)

# In-memory storage for blog posts
posts_db = []

def load_posts():
    """Load posts from a JSON file if available."""
    try:
        with open('posts.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def save_posts():
    """Save posts to a JSON file."""
    with open('posts.json', 'w') as f:
        json.dump(posts_db, f)

# Load posts from the database (if available)
posts_db = load_posts()

@app.route('/posts', methods=['GET'])
def get_posts():
    """List all posts with pagination."""
    page = request.args.get('page', default=1, type=int)
    per_page = request.args.get('per_page', default=10, type=int)
    start = (page - 1) * per_page
    end = start + per_page
    return jsonify(posts_db[start:end])

@app.route('/posts/<int:post_id>', methods=['GET'])
def get_post(post_id):
    """Get a specific post by ID."""
    post = next((post for post in posts_db if post['id'] == post_id), None)
    if post is None:
        abort(404, description="Post not found")
    return jsonify(post)

@app.route('/posts', methods=['POST'])
def create_post():
    """Create a new post."""
    data = request.get_json()
    if 'title' not in data or 'content' not in data:
        abort(400, description="Title and content are required")
    post = {
        'id': len(posts_db) + 1,
        'title': data['title'],
        'content': data['content'],
        'author': data.get('author', '')
    }
    posts_db.append(post)
    save_posts()
    return jsonify(post), 201

@app.route('/posts/<int:post_id>', methods=['PUT'])
def update_post(post_id):
    """Update an existing post by ID."""
    data = request.get_json()
    post = next((post for post in posts_db if post['id'] == post_id), None)
    if post is None:
        abort(404, description="Post not found")
    if 'title' in data:
        post['title'] = data['title']
    if 'content' in data:
        post['content'] = data['content']
    if 'author' in data and request.headers.get('Authorization') == f"Bearer {post['author']}":
        post['author'] = data['author']
    else:
        abort(403, description="Permission denied")
    save_posts()
    return jsonify(post)

@app.route('/posts/<int:post_id>', methods=['DELETE'])
def delete_post(post_id):
    """Delete a post by ID."""
    global posts_db
    posts_db = [post for post in posts_db if post['id'] != post_id]
    save_posts()
    return '', 204

@app.route('/search', methods=['GET'])
def search_posts():
    """Search posts by title or content."""
    query = request.args.get('q', '')
    results = [post for post in posts_db if query.lower() in post['title'].lower() or query.lower() in post['content'].lower()]
    return jsonify(results)

if __name__ == '__main__':
    app.run(debug=True)