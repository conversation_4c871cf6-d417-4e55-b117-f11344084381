{"error": "'Plan' object has no attribute 'title'", "type": "AttributeError", "traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/git/local_agent/sonnet_model/test_complete_debug.py\", line 95, in test_complete_debug\n    exec_result = await orchestrator.process_user_request(\"continue\", {})\n  File \"/home/<USER>/git/local_agent/sonnet_model/task_manager/services/orchestrator.py\", line 594, in process_user_request\n    return await self._handle_general_request(user_input, context)\n  File \"/home/<USER>/git/local_agent/sonnet_model/task_manager/services/orchestrator.py\", line 806, in _handle_general_request\n    \"suggested_actions\": await self._suggest_next_actions(),\n  File \"/home/<USER>/git/local_agent/sonnet_model/task_manager/services/orchestrator.py\", line 872, in _suggest_next_actions\n    resumption_context = await self.state_manager.get_resumption_context()\n  File \"/home/<USER>/git/local_agent/sonnet_model/task_manager/services/state_manager.py\", line 721, in get_resumption_context\n    \"recommended_next_action\": await self._recommend_next_action(incomplete_tasks, incomplete_plans)\n  File \"/home/<USER>/git/local_agent/sonnet_model/task_manager/services/state_manager.py\", line 782, in _recommend_next_action\n    return f\"Continue with plan: {best_plan['plan'].title} ({best_plan['progress']:.1f}% complete)\"\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/pydantic/main.py\", line 991, in __getattr__\n    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')\nAttributeError: 'Plan' object has no attribute 'title'\n"}