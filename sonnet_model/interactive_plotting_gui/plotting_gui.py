import tkinter as tk
from tkinter import filedialog, messagebox
import matplotlib.pyplot as plt
import matplotlib.backends.tkagg as tkagg
import numpy as np

class PlottingApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Interactive Data Plotting App")
        
        # Data storage
        self.data = {'x': [], 'y': []}
        self.plot_type = tk.StringVar(value="line")
        
        # Create UI elements
        self.create_widgets()
    
    def create_widgets(self):
        # Plot type selection
        plot_type_frame = tk.Frame(self.root)
        tk.Label(plot_type_frame, text="Plot Type:").grid(row=0, column=0, padx=10, pady=10)
        self.plot_type_menu = tk.OptionMenu(plot_type_frame, self.plot_type, "line", "scatter", "bar")
        self.plot_type_menu.grid(row=0, column=1, padx=10, pady=10)
        plot_type_frame.pack()
        
        # Data input frame
        data_input_frame = tk.Frame(self.root)
        self.x_entry = tk.Entry(data_input_frame, width=20)
        self.y_entry = tk.Entry(data_input_frame, width=20)
        tk.Label(data_input_frame, text="X Data:").grid(row=0, column=0, padx=10, pady=10)
        self.x_entry.grid(row=0, column=1, padx=10, pady=10)
        tk.Label(data_input_frame, text="Y Data:").grid(row=1, column=0, padx=10, pady=10)
        self.y_entry.grid(row=1, column=1, padx=10, pady=10)
        
        tk.Button(data_input_frame, text="Add Data", command=self.add_data).grid(row=2, column=0, columnspan=2, pady=10)
        data_input_frame.pack()
        
        # Plot button
        tk.Button(self.root, text="Plot", command=self.plot_data).pack(pady=20)
        
        # Canvas for plotting
        self.canvas = plt.FigureCanvasTkAgg(plt.figure(), master=self.root)
        self.canvas.get_tk_widget().pack(side=tk.BOTTOM, fill=tk.BOTH, expand=True)
        self.ax = self.canvas.figure.add_subplot(111)
    
    def add_data(self):
        try:
            x = list(map(float, self.x_entry.get().split()))
            y = list(map(float, self.y_entry.get().split()))
            if len(x) == len(y):
                self.data['x'].extend(x)
                self.data['y'].extend(y)
            else:
                raise ValueError("X and Y data lengths do not match.")
        except ValueError as e:
            messagebox.showerror("Error", str(e))
    
    def plot_data(self):
        if not self.data['x'] or not self.data['y']:
            messagebox.showwarning("Warning", "No data to plot.")
            return
        
        x = np.array(self.data['x'])
        y = np.array(self.data['y'])
        
        self.ax.clear()
        if self.plot_type.get() == "line":
            self.ax.plot(x, y, 'o-')
        elif self.plot_type.get() == "scatter":
            self.ax.scatter(x, y)
        elif self.plot_type.get() == "bar":
            self.ax.bar(x, y)
        
        self.canvas.draw()
    
    def load_data_from_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("Text files", "*.txt")])
        if file_path:
            with open(file_path, 'r') as file:
                lines = file.readlines()
                x, y = [], []
                for line in lines:
                    values = list(map(float, line.split()))
                    if len(values) == 2:
                        x.append(values[0])
                        y.append(values[1])
                self.data['x'].extend(x)
                self.data['y'].extend(y)

if __name__ == "__main__":
    root = tk.Tk()
    app = PlottingApp(root)
    root.mainloop()