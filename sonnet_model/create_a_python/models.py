# models.py
from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import MinLengthValidator

class User(AbstractUser):
    email = models.EmailField(unique=True)
    phone_number = models.CharField(max_length=15, unique=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'phone_number']

    def __str__(self):
        return self.username

class CoreDataModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

class Product(CoreDataModel):
    name = models.CharField(max_length=200, unique=True)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return self.name

    def clean(self):
        if self.price <= 0:
            raise ValidationError({'price': 'Price must be greater than zero.'})

class Order(CoreDataModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    products = models.ManyToManyField(Product, through='OrderItem')

    def __str__(self):
        return f"Order {self.id} by {self.user}"

class OrderItem(CoreDataModel):
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField()
    price = models.DecimalField(max_digits=10, decimal_places=2)

    def clean(self):
        if self.quantity <= 0:
            raise ValidationError({'quantity': 'Quantity must be greater than zero.'})
        if self.price <= 0:
            raise ValidationError({'price': 'Price must be greater than zero.'})

    def save(self, *args, **kwargs):
        self.full_clean()  # Run validation before saving
        super().save(*args, **kwargs)

# Example usage of the models in a Django shell session:
# python manage.py shell
# from .models import User, Product, Order, OrderItem
# user = User.objects.create(username='testuser', email='<EMAIL>', phone_number='1234567890')
# product = Product.objects.create(name='Test Product', description='A test product', price=10.00)
# order = Order.objects.create(user=user)
# order_item = OrderItem.objects.create(order=order, product=product, quantity=2, price=10.00)