# factorial.py

def factorial(n):
    """
    Calculate the factorial of a non-negative integer n.
    
    Parameters:
    n (int): A non-negative integer whose factorial is to be calculated.
    
    Returns:
    int: The factorial of the given number n.
    
    Raises:
    ValueError: If the input is not a non-negative integer.
    """
    if not isinstance(n, int) or n < 0:
        raise ValueError("Input must be a non-negative integer.")
    if n == 0:
        return 1
    result = 1
    for i in range(1, n + 1):
        result *= i
    return result

# Example usage:
if __name__ == "__main__":
    try:
        print(factorial(5))  # Output should be 120
        print(factorial(-1)) # Raises ValueError
        print(factorial("a")) # Raises ValueError
    except ValueError as e:
        print(e)