{"asctime": "2025-07-04T18:22:31+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
/home/<USER>/git/local_agent/sonnet_model/main.py:70: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
/home/<USER>/git/local_agent/sonnet_model/main.py:84: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("shutdown")
INFO:     Will watch for changes in these directories: ['/home/<USER>/git/local_agent/sonnet_model']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [332586] using WatchFiles
{"asctime": "2025-07-04T18:22:31+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
INFO:     Started server process [332589]
INFO:     Waiting for application startup.
{"asctime": "2025-07-04T18:22:31+0200", "name": "root", "levelname": "INFO", "message": "Starting Sonnet Model system"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "shared.state_manager", "levelname": "WARNING", "message": "Using in-memory state store - not suitable for production multi-worker setup"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T18:22:31+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
INFO:     Application startup complete.
{"asctime": "2025-07-04T18:22:31+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:32+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:32+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:32+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:33+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:33+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:33+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:34+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:34+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:34+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:35+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:35+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:35+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:36+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:36+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:36+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:37+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:37+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:38+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:38+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:38+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:39+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:39+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:39+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:40+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:40+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:40+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:41+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:41+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:41+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:42+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:42+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:42+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:43+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:43+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:44+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:44+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:44+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:45+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:45+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:45+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:46+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:46+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:46+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:47+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:47+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:47+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:48+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:48+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:48+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:49+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:49+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:49+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:50+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:50+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:51+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:51+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:51+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:52+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:52+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:52+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:53+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:53+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:53+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:54+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:54+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:54+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:55+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:55+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:55+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:56+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:56+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:56+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:57+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:57+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:58+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:58+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:58+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:59+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:59+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:22:59+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:00+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:00+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:00+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:01+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:01+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:01+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:02+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:02+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:02+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:03+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:03+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:03+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:04+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:04+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:05+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:05+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:05+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:06+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:06+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:06+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:07+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:07+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:07+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:08+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:08+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:08+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:09+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:09+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:09+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:10+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:10+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:10+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:11+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:11+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:12+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:12+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:12+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:13+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:13+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:13+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:14+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:14+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:14+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:15+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:15+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:15+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:16+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:16+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:16+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:17+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:17+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:18+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:18+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:18+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:19+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:19+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:19+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:20+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:20+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:20+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:21+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:21+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:21+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:22+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:22+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:22+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:23+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:23+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:23+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:24+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:24+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:25+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:25+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:25+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:26+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:26+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:26+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:27+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:27+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:27+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:28+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:28+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:28+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:29+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:29+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:29+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:30+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:30+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:30+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:31+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:31+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:32+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:32+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:32+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:33+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:33+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:33+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:34+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:34+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:34+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:35+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:35+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:35+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:36+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:36+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:36+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:37+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:37+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:37+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:38+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:38+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:39+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:39+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:39+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:40+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:40+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:40+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:41+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:41+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:41+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:42+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:42+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:42+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:43+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:43+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:43+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:44+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:44+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:45+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:45+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:45+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:46+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:46+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:46+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:47+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:47+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:47+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:48+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:48+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:48+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:49+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:49+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:49+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:50+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:50+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:50+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:51+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:51+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:52+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:52+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:52+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:53+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:53+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:53+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:54+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:54+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:54+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:55+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:55+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:55+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:56+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:56+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:56+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:57+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:57+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:57+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:58+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:58+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:59+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:59+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:23:59+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:00+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:00+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:00+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:01+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:01+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:01+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:02+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:02+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:02+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:03+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:03+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:03+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:04+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:04+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:04+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:05+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:05+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:06+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:06+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:06+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:07+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:07+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:07+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:08+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:08+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:08+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:09+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:09+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:09+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:10+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:10+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:10+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:11+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:11+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:11+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:12+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:12+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:13+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:13+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:13+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:14+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:14+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:14+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:15+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:15+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:15+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:16+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:16+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:16+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:17+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:17+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:17+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:18+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:18+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:19+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:19+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:19+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:20+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:20+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:20+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:21+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:21+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:21+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:22+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:22+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:22+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:23+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:23+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:23+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:24+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:24+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:24+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:25+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:25+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:26+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:26+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:26+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:27+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:27+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:27+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:28+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:28+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:28+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:29+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:29+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:29+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:30+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:30+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:30+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:31+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:31+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:31+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:32+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:32+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:33+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:33+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:33+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:34+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:34+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:34+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:35+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:35+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:35+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:36+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:36+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:36+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:37+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:37+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:37+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:38+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:38+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:38+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:39+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:39+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:40+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:40+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:40+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:41+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:41+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:41+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:42+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:42+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
{"asctime": "2025-07-04T18:24:42+0200", "name": "watchfiles.main", "levelname": "INFO", "message": "2 changes detected"}
