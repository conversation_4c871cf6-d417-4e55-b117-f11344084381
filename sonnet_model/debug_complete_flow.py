"""
Complete Flow Debug Test - See every step of the process
This will run a real task and capture all prompts, contexts, and outputs
"""

import asyncio
import json
import os
import logging
from datetime import datetime
from pathlib import Path

# Import the main orchestrator
from task_manager.services.orchestrator import TaskOrchestrator

# Set up detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class DebugCapture:
    """Capture all debug information during the flow"""
    
    def __init__(self):
        self.debug_dir = Path("debug_session_" + datetime.now().strftime("%Y%m%d_%H%M%S"))
        self.debug_dir.mkdir(exist_ok=True)
        self.step_counter = 0
        
    def save_step(self, step_name: str, data: dict, description: str = ""):
        """Save a debug step to file"""
        self.step_counter += 1
        filename = f"step_{self.step_counter:02d}_{step_name}.json"
        filepath = self.debug_dir / filename
        
        debug_data = {
            "step": self.step_counter,
            "name": step_name,
            "description": description,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(debug_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📝 DEBUG STEP {self.step_counter}: {step_name}")
        print(f"   📄 Saved to: {filepath}")
        if description:
            print(f"   📋 {description}")
        print()

async def debug_complete_flow():
    """Run a complete debug session"""
    
    print("🔍 STARTING COMPLETE FLOW DEBUG SESSION")
    print("=" * 60)
    
    # Initialize debug capture
    debug = DebugCapture()
    
    # Create orchestrator with debug-friendly config
    config = {
        "llm": {
            "provider": "ollama",
            "model": "deepseek-coder-v2:16b",
            "base_url": "http://localhost:11434"
        },
        "coaching_enabled": True,
        "enable_quality_gates": True,
        "auto_recovery": True,
        "max_iterations": 3,  # Limit iterations for debug
        "quality_threshold": 0.8
    }
    
    debug.save_step("config", config, "Initial orchestrator configuration")
    
    # Initialize orchestrator
    orchestrator = TaskOrchestrator(config)
    await orchestrator.state_manager.initialize()
    
    # Test task - Python-based, decent complexity
    task_description = "create a Python web scraper that can extract article titles and URLs from a news website, save the data to CSV, and include error handling for network issues"
    
    debug.save_step("task_input", {
        "description": task_description,
        "language": "python",
        "complexity": "medium"
    }, "User input task description")
    
    try:
        print(f"🎯 TASK: {task_description}")
        print()
        
        # STEP 1: Plan Generation
        print("📋 STEP 1: PLAN GENERATION")
        print("-" * 40)
        
        result = await orchestrator.process_request(task_description, {})
        
        debug.save_step("orchestrator_result", result, "Complete orchestrator response")
        
        # Get the generated plan
        if result.get("type") == "plan_created":
            plan = result.get("plan", {})
            debug.save_step("generated_plan", plan, "Detailed plan structure created by TaskPlannerLLM")
            
            print(f"✅ Plan created: {plan.get('name')}")
            print(f"📋 Steps: {len(plan.get('steps', []))}")
            print(f"🎯 Goals: {len(plan.get('goals', []))}")
            print(f"📋 Requirements: {len(plan.get('requirements', []))}")
            print()
            
            # STEP 2: Start Execution
            print("🚀 STEP 2: STARTING PLAN EXECUTION")
            print("-" * 40)
            
            # Get the plan ID and start execution
            plan_id = plan.get("id")
            if plan_id:
                # Start the first task
                execution_result = await orchestrator.process_request(f"start plan {plan_id}", {})
                debug.save_step("execution_start", execution_result, "Plan execution initiation")
                
                print("✅ Plan execution started")
                print()
                
                # STEP 3: Monitor the iterative process
                print("🔄 STEP 3: MONITORING ITERATIVE PROCESS")
                print("-" * 40)
                
                # Let's manually trigger a few iterations to see the flow
                for iteration in range(3):
                    print(f"🔄 Iteration {iteration + 1}")
                    
                    # Get current state
                    state = await orchestrator.state_manager.get_state_summary()
                    debug.save_step(f"iteration_{iteration + 1}_state", state, f"System state at iteration {iteration + 1}")
                    
                    # Continue processing
                    continue_result = await orchestrator.process_request("continue", {})
                    debug.save_step(f"iteration_{iteration + 1}_result", continue_result, f"Processing result for iteration {iteration + 1}")
                    
                    print(f"   📊 Result type: {continue_result.get('type', 'unknown')}")
                    
                    # Check if we have generated code
                    if "generated_code" in continue_result:
                        debug.save_step(f"iteration_{iteration + 1}_code", {
                            "code": continue_result["generated_code"],
                            "language": continue_result.get("language", "python"),
                            "files": continue_result.get("files", [])
                        }, f"Generated code at iteration {iteration + 1}")
                    
                    # Check if we have critique feedback
                    if "critique_feedback" in continue_result:
                        debug.save_step(f"iteration_{iteration + 1}_critique", {
                            "feedback": continue_result["critique_feedback"],
                            "quality_score": continue_result.get("quality_score", 0),
                            "suggestions": continue_result.get("suggestions", [])
                        }, f"Critique feedback at iteration {iteration + 1}")
                    
                    print()
                    
                    # Break if completed
                    if continue_result.get("type") == "completed":
                        break
        
        # STEP 4: Final State
        print("📊 STEP 4: FINAL STATE ANALYSIS")
        print("-" * 40)
        
        final_state = await orchestrator.state_manager.get_state_summary()
        debug.save_step("final_state", final_state, "Complete final system state")
        
        # Get all generated files
        generated_files = await orchestrator.state_manager.get_generated_files()
        debug.save_step("generated_files", generated_files, "All files generated during the process")
        
        print("✅ Debug session completed!")
        print(f"📁 All debug data saved to: {debug.debug_dir}")
        
    except Exception as e:
        debug.save_step("error", {
            "error": str(e),
            "type": type(e).__name__
        }, f"Error occurred: {e}")
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await orchestrator.state_manager.shutdown()
        
        # Create a summary file
        summary = {
            "session_info": {
                "task": task_description,
                "total_steps": debug.step_counter,
                "debug_directory": str(debug.debug_dir),
                "completed_at": datetime.now().isoformat()
            },
            "files_created": [f.name for f in debug.debug_dir.glob("*.json")]
        }
        
        with open(debug.debug_dir / "session_summary.json", 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n📋 SESSION SUMMARY:")
        print(f"   🎯 Task: {task_description}")
        print(f"   📊 Total debug steps: {debug.step_counter}")
        print(f"   📁 Debug directory: {debug.debug_dir}")
        print(f"   📄 Files created: {len(summary['files_created'])}")

if __name__ == "__main__":
    asyncio.run(debug_complete_flow())
