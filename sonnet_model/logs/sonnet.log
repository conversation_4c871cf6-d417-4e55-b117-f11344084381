{"asctime": "2025-07-04T20:32:27+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "root", "levelname": "INFO", "message": "Starting Sonnet Model system"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.state_manager", "levelname": "WARNING", "message": "Using in-memory state store - not suitable for production multi-worker setup"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "root", "levelname": "INFO", "message": "Shutting down Sonnet Model system"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "root", "levelname": "INFO", "message": "Starting Sonnet Model system"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.state_manager", "levelname": "WARNING", "message": "Using in-memory state store - not suitable for production multi-worker setup"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\ude80 Starting background processing for project: factorial_debug_test"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udcdd User input: Create a Python function to calculate factorial with error handling"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug enabled: True"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled - LLM interactions will be saved to debug folder"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled for project: factorial_debug_test"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd27 Calling system.generate_complete_project..."}
{"asctime": "2025-07-04T20:33:28+0200", "name": "system_integration", "levelname": "INFO", "message": "Generating complete project: Create a Python function to calculate factorial with error handling"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug directory created: debug/llm_visibility_project_20250704_203328_20250704_203328"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: project_request.json"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Setting debug callback for WorkingProjectGenerator (debug_enabled=True)"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug callback set for WorkingProjectGenerator"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_001_code_generator_input.json"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_001_code_generator_input.json"}
{"asctime": "2025-07-04T20:33:45+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:33:45+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_002_code_generator_output.json"}
{"asctime": "2025-07-04T20:33:45+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_002_code_generator_output.json"}
{"asctime": "2025-07-04T20:33:45+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_003_critique_engine_input.json"}
{"asctime": "2025-07-04T20:33:45+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_003_critique_engine_input.json"}
{"asctime": "2025-07-04T20:33:54+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:33:54+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_004_critique_engine_output.json"}
{"asctime": "2025-07-04T20:33:54+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_004_critique_engine_output.json"}
{"asctime": "2025-07-04T20:33:54+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_005_code_generator_input.json"}
{"asctime": "2025-07-04T20:33:54+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_005_code_generator_input.json"}
{"asctime": "2025-07-04T20:34:12+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:34:12+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_006_code_generator_output.json"}
{"asctime": "2025-07-04T20:34:12+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_006_code_generator_output.json"}
{"asctime": "2025-07-04T20:34:12+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_007_critique_engine_input.json"}
{"asctime": "2025-07-04T20:34:12+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_007_critique_engine_input.json"}
{"asctime": "2025-07-04T20:34:18+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:34:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_008_critique_engine_output.json"}
{"asctime": "2025-07-04T20:34:18+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_008_critique_engine_output.json"}
{"asctime": "2025-07-04T20:34:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_009_code_generator_input.json"}
{"asctime": "2025-07-04T20:34:18+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_009_code_generator_input.json"}
{"asctime": "2025-07-04T20:34:29+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:34:29+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_010_code_generator_output.json"}
{"asctime": "2025-07-04T20:34:29+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_010_code_generator_output.json"}
{"asctime": "2025-07-04T20:34:29+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_011_critique_engine_input.json"}
{"asctime": "2025-07-04T20:34:29+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_011_critique_engine_input.json"}
{"asctime": "2025-07-04T20:34:40+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:34:40+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_012_critique_engine_output.json"}
{"asctime": "2025-07-04T20:34:40+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_012_critique_engine_output.json"}
{"asctime": "2025-07-04T20:34:40+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_013_code_generator_input.json"}
{"asctime": "2025-07-04T20:34:40+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_013_code_generator_input.json"}
{"asctime": "2025-07-04T20:34:51+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:34:51+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_014_code_generator_output.json"}
{"asctime": "2025-07-04T20:34:51+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_014_code_generator_output.json"}
{"asctime": "2025-07-04T20:34:51+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_015_critique_engine_input.json"}
{"asctime": "2025-07-04T20:34:51+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_015_critique_engine_input.json"}
{"asctime": "2025-07-04T20:35:01+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:35:01+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_016_critique_engine_output.json"}
{"asctime": "2025-07-04T20:35:01+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_016_critique_engine_output.json"}
{"asctime": "2025-07-04T20:35:01+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_017_code_generator_input.json"}
{"asctime": "2025-07-04T20:35:01+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_017_code_generator_input.json"}
{"asctime": "2025-07-04T20:35:10+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:35:10+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_018_code_generator_output.json"}
{"asctime": "2025-07-04T20:35:10+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_018_code_generator_output.json"}
{"asctime": "2025-07-04T20:35:10+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_019_critique_engine_input.json"}
{"asctime": "2025-07-04T20:35:10+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_019_critique_engine_input.json"}
{"asctime": "2025-07-04T20:35:18+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:35:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_020_critique_engine_output.json"}
{"asctime": "2025-07-04T20:35:18+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_020_critique_engine_output.json"}
{"asctime": "2025-07-04T20:35:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_021_code_generator_input.json"}
{"asctime": "2025-07-04T20:35:18+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_021_code_generator_input.json"}
{"asctime": "2025-07-04T20:35:28+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:35:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_022_code_generator_output.json"}
{"asctime": "2025-07-04T20:35:28+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_022_code_generator_output.json"}
{"asctime": "2025-07-04T20:35:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_023_critique_engine_input.json"}
{"asctime": "2025-07-04T20:35:28+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_023_critique_engine_input.json"}
{"asctime": "2025-07-04T20:35:36+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:35:36+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_024_critique_engine_output.json"}
{"asctime": "2025-07-04T20:35:36+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_024_critique_engine_output.json"}
{"asctime": "2025-07-04T20:35:36+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project generation completed with result: {'type': 'project_generation', 'success': True, 'project_name': 'create_a_python', 'project_path': 'create_a_python', 'statistics': {'total_files': 3, 'successful_files': 3, 'success_rate': 100.0, 'total_iterations': 6}, 'files': [{'path': 'models.py', 'success': True, 'iterations': 1, 'size': 2334}, {'path': 'views.py', 'success': True, 'iterations': 1, 'size': 1092}, {'path': 'app.py', 'success': True, 'iterations': 4, 'size': 1585}]}"}
{"asctime": "2025-07-04T20:35:36+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project factorial_debug_test completed successfully with 3 files"}
{"asctime": "2025-07-04T20:35:36+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83c\udfc1 Background processing completed for project: factorial_debug_test"}
