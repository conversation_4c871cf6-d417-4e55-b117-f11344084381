{"asctime": "2025-07-04T20:32:27+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "root", "levelname": "INFO", "message": "Starting Sonnet Model system"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.state_manager", "levelname": "WARNING", "message": "Using in-memory state store - not suitable for production multi-worker setup"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:32:27+0200", "name": "root", "levelname": "INFO", "message": "Shutting down Sonnet Model system"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "root", "levelname": "INFO", "message": "Starting Sonnet Model system"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.state_manager", "levelname": "WARNING", "message": "Using in-memory state store - not suitable for production multi-worker setup"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:33:05+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\ude80 Starting background processing for project: factorial_debug_test"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udcdd User input: Create a Python function to calculate factorial with error handling"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug enabled: True"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled - LLM interactions will be saved to debug folder"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled for project: factorial_debug_test"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd27 Calling system.generate_complete_project..."}
{"asctime": "2025-07-04T20:33:28+0200", "name": "system_integration", "levelname": "INFO", "message": "Generating complete project: Create a Python function to calculate factorial with error handling"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug directory created: debug/llm_visibility_project_20250704_203328_20250704_203328"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: project_request.json"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Setting debug callback for WorkingProjectGenerator (debug_enabled=True)"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug callback set for WorkingProjectGenerator"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_001_code_generator_input.json"}
{"asctime": "2025-07-04T20:33:28+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_001_code_generator_input.json"}
{"asctime": "2025-07-04T20:33:45+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:33:45+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_002_code_generator_output.json"}
{"asctime": "2025-07-04T20:33:45+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_002_code_generator_output.json"}
{"asctime": "2025-07-04T20:33:45+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_003_critique_engine_input.json"}
{"asctime": "2025-07-04T20:33:45+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_003_critique_engine_input.json"}
{"asctime": "2025-07-04T20:33:54+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:33:54+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_004_critique_engine_output.json"}
{"asctime": "2025-07-04T20:33:54+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_004_critique_engine_output.json"}
{"asctime": "2025-07-04T20:33:54+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_005_code_generator_input.json"}
{"asctime": "2025-07-04T20:33:54+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_005_code_generator_input.json"}
{"asctime": "2025-07-04T20:34:12+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:34:12+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_006_code_generator_output.json"}
{"asctime": "2025-07-04T20:34:12+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_006_code_generator_output.json"}
{"asctime": "2025-07-04T20:34:12+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_007_critique_engine_input.json"}
{"asctime": "2025-07-04T20:34:12+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_007_critique_engine_input.json"}
{"asctime": "2025-07-04T20:34:18+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:34:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_008_critique_engine_output.json"}
{"asctime": "2025-07-04T20:34:18+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_008_critique_engine_output.json"}
{"asctime": "2025-07-04T20:34:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_009_code_generator_input.json"}
{"asctime": "2025-07-04T20:34:18+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_009_code_generator_input.json"}
{"asctime": "2025-07-04T20:34:29+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:34:29+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_010_code_generator_output.json"}
{"asctime": "2025-07-04T20:34:29+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_010_code_generator_output.json"}
{"asctime": "2025-07-04T20:34:29+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_011_critique_engine_input.json"}
{"asctime": "2025-07-04T20:34:29+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_011_critique_engine_input.json"}
{"asctime": "2025-07-04T20:34:40+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:34:40+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_012_critique_engine_output.json"}
{"asctime": "2025-07-04T20:34:40+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_012_critique_engine_output.json"}
{"asctime": "2025-07-04T20:34:40+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_013_code_generator_input.json"}
{"asctime": "2025-07-04T20:34:40+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_013_code_generator_input.json"}
{"asctime": "2025-07-04T20:34:51+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:34:51+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_014_code_generator_output.json"}
{"asctime": "2025-07-04T20:34:51+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_014_code_generator_output.json"}
{"asctime": "2025-07-04T20:34:51+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_015_critique_engine_input.json"}
{"asctime": "2025-07-04T20:34:51+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_015_critique_engine_input.json"}
{"asctime": "2025-07-04T20:35:01+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:35:01+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_016_critique_engine_output.json"}
{"asctime": "2025-07-04T20:35:01+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_016_critique_engine_output.json"}
{"asctime": "2025-07-04T20:35:01+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_017_code_generator_input.json"}
{"asctime": "2025-07-04T20:35:01+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_017_code_generator_input.json"}
{"asctime": "2025-07-04T20:35:10+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:35:10+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_018_code_generator_output.json"}
{"asctime": "2025-07-04T20:35:10+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_018_code_generator_output.json"}
{"asctime": "2025-07-04T20:35:10+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_019_critique_engine_input.json"}
{"asctime": "2025-07-04T20:35:10+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_019_critique_engine_input.json"}
{"asctime": "2025-07-04T20:35:18+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:35:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_020_critique_engine_output.json"}
{"asctime": "2025-07-04T20:35:18+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_020_critique_engine_output.json"}
{"asctime": "2025-07-04T20:35:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_021_code_generator_input.json"}
{"asctime": "2025-07-04T20:35:18+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_021_code_generator_input.json"}
{"asctime": "2025-07-04T20:35:28+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:35:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_022_code_generator_output.json"}
{"asctime": "2025-07-04T20:35:28+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_022_code_generator_output.json"}
{"asctime": "2025-07-04T20:35:28+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_023_critique_engine_input.json"}
{"asctime": "2025-07-04T20:35:28+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_023_critique_engine_input.json"}
{"asctime": "2025-07-04T20:35:36+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:35:36+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_024_critique_engine_output.json"}
{"asctime": "2025-07-04T20:35:36+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_024_critique_engine_output.json"}
{"asctime": "2025-07-04T20:35:36+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project generation completed with result: {'type': 'project_generation', 'success': True, 'project_name': 'create_a_python', 'project_path': 'create_a_python', 'statistics': {'total_files': 3, 'successful_files': 3, 'success_rate': 100.0, 'total_iterations': 6}, 'files': [{'path': 'models.py', 'success': True, 'iterations': 1, 'size': 2334}, {'path': 'views.py', 'success': True, 'iterations': 1, 'size': 1092}, {'path': 'app.py', 'success': True, 'iterations': 4, 'size': 1585}]}"}
{"asctime": "2025-07-04T20:35:36+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project factorial_debug_test completed successfully with 3 files"}
{"asctime": "2025-07-04T20:35:36+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83c\udfc1 Background processing completed for project: factorial_debug_test"}
{"asctime": "2025-07-04T20:52:23+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\ude80 Starting background processing for project: factorial_fixed_test"}
{"asctime": "2025-07-04T20:52:23+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udcdd User input: Create a Python function to calculate factorial with error handling"}
{"asctime": "2025-07-04T20:52:23+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug enabled: True"}
{"asctime": "2025-07-04T20:52:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled - LLM interactions will be saved to debug folder"}
{"asctime": "2025-07-04T20:52:23+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled for project: factorial_fixed_test"}
{"asctime": "2025-07-04T20:52:23+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd27 Calling system.generate_complete_project..."}
{"asctime": "2025-07-04T20:52:23+0200", "name": "system_integration", "levelname": "INFO", "message": "Generating complete project: Create a Python function to calculate factorial with error handling"}
{"asctime": "2025-07-04T20:52:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug directory created: debug/llm_visibility_project_20250704_205223_20250704_205223"}
{"asctime": "2025-07-04T20:52:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: project_request.json"}
{"asctime": "2025-07-04T20:52:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Setting debug callback for WorkingProjectGenerator (debug_enabled=True)"}
{"asctime": "2025-07-04T20:52:23+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug callback set for WorkingProjectGenerator"}
{"asctime": "2025-07-04T20:52:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_001_code_generator_input.json"}
{"asctime": "2025-07-04T20:52:23+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_001_code_generator_input.json"}
{"asctime": "2025-07-04T20:52:39+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:52:39+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_002_code_generator_output.json"}
{"asctime": "2025-07-04T20:52:39+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_002_code_generator_output.json"}
{"asctime": "2025-07-04T20:52:39+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_003_critique_engine_input.json"}
{"asctime": "2025-07-04T20:52:39+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_003_critique_engine_input.json"}
{"asctime": "2025-07-04T20:52:47+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:52:47+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_004_critique_engine_output.json"}
{"asctime": "2025-07-04T20:52:47+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_004_critique_engine_output.json"}
{"asctime": "2025-07-04T20:52:47+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_005_code_generator_input.json"}
{"asctime": "2025-07-04T20:52:47+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_005_code_generator_input.json"}
{"asctime": "2025-07-04T20:52:59+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:52:59+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_006_code_generator_output.json"}
{"asctime": "2025-07-04T20:52:59+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_006_code_generator_output.json"}
{"asctime": "2025-07-04T20:52:59+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_007_critique_engine_input.json"}
{"asctime": "2025-07-04T20:52:59+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_007_critique_engine_input.json"}
{"asctime": "2025-07-04T20:53:04+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:53:04+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_008_critique_engine_output.json"}
{"asctime": "2025-07-04T20:53:04+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_008_critique_engine_output.json"}
{"asctime": "2025-07-04T20:53:04+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_009_code_generator_input.json"}
{"asctime": "2025-07-04T20:53:04+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_009_code_generator_input.json"}
{"asctime": "2025-07-04T20:53:18+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:53:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_010_code_generator_output.json"}
{"asctime": "2025-07-04T20:53:18+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_010_code_generator_output.json"}
{"asctime": "2025-07-04T20:53:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_011_critique_engine_input.json"}
{"asctime": "2025-07-04T20:53:18+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_011_critique_engine_input.json"}
{"asctime": "2025-07-04T20:53:26+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:53:26+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_012_critique_engine_output.json"}
{"asctime": "2025-07-04T20:53:26+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_012_critique_engine_output.json"}
{"asctime": "2025-07-04T20:53:26+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project generation completed with result: {'type': 'project_generation', 'success': True, 'project_name': 'create_a_python', 'project_path': 'create_a_python', 'statistics': {'total_files': 3, 'successful_files': 3, 'success_rate': 100.0, 'total_iterations': 3}, 'files': [{'path': 'models.py', 'success': True, 'iterations': 1, 'size': 1899}, {'path': 'views.py', 'success': True, 'iterations': 1, 'size': 1122}, {'path': 'app.py', 'success': True, 'iterations': 1, 'size': 1732}]}"}
{"asctime": "2025-07-04T20:53:26+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project factorial_fixed_test completed successfully with 3 files"}
{"asctime": "2025-07-04T20:53:26+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83c\udfc1 Background processing completed for project: factorial_fixed_test"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "root", "levelname": "INFO", "message": "Starting Sonnet Model system"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "shared.state_manager", "levelname": "WARNING", "message": "Using in-memory state store - not suitable for production multi-worker setup"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T20:55:09+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T20:55:29+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\ude80 Starting background processing for project: factorial_final_test"}
{"asctime": "2025-07-04T20:55:29+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udcdd User input: Create a Python function to calculate factorial with error handling"}
{"asctime": "2025-07-04T20:55:29+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug enabled: True"}
{"asctime": "2025-07-04T20:55:29+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled - LLM interactions will be saved to debug folder"}
{"asctime": "2025-07-04T20:55:29+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled for project: factorial_final_test"}
{"asctime": "2025-07-04T20:55:29+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd27 Calling system.generate_complete_project..."}
{"asctime": "2025-07-04T20:55:29+0200", "name": "system_integration", "levelname": "INFO", "message": "Generating complete project: Create a Python function to calculate factorial with error handling"}
{"asctime": "2025-07-04T20:55:29+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug directory created: debug/llm_visibility_project_20250704_205529_20250704_205529"}
{"asctime": "2025-07-04T20:55:29+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: project_request.json"}
{"asctime": "2025-07-04T20:55:29+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Setting debug callback for WorkingProjectGenerator (debug_enabled=True)"}
{"asctime": "2025-07-04T20:55:29+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug callback set for WorkingProjectGenerator"}
{"asctime": "2025-07-04T20:55:29+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_001_code_generator_input.json"}
{"asctime": "2025-07-04T20:55:29+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_001_code_generator_input.json"}
{"asctime": "2025-07-04T20:55:37+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:55:37+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_002_code_generator_output.json"}
{"asctime": "2025-07-04T20:55:37+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_002_code_generator_output.json"}
{"asctime": "2025-07-04T20:55:37+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_003_critique_engine_input.json"}
{"asctime": "2025-07-04T20:55:37+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_003_critique_engine_input.json"}
{"asctime": "2025-07-04T20:55:45+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:55:45+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_004_critique_engine_output.json"}
{"asctime": "2025-07-04T20:55:45+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_004_critique_engine_output.json"}
{"asctime": "2025-07-04T20:55:45+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_005_code_generator_input.json"}
{"asctime": "2025-07-04T20:55:45+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_005_code_generator_input.json"}
{"asctime": "2025-07-04T20:55:56+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:55:56+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_006_code_generator_output.json"}
{"asctime": "2025-07-04T20:55:56+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_006_code_generator_output.json"}
{"asctime": "2025-07-04T20:55:56+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_007_critique_engine_input.json"}
{"asctime": "2025-07-04T20:55:56+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_007_critique_engine_input.json"}
{"asctime": "2025-07-04T20:56:03+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:56:03+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_008_critique_engine_output.json"}
{"asctime": "2025-07-04T20:56:03+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_008_critique_engine_output.json"}
{"asctime": "2025-07-04T20:56:03+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_009_code_generator_input.json"}
{"asctime": "2025-07-04T20:56:03+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_009_code_generator_input.json"}
{"asctime": "2025-07-04T20:56:13+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:56:13+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_010_code_generator_output.json"}
{"asctime": "2025-07-04T20:56:13+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_010_code_generator_output.json"}
{"asctime": "2025-07-04T20:56:13+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_011_critique_engine_input.json"}
{"asctime": "2025-07-04T20:56:13+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_011_critique_engine_input.json"}
{"asctime": "2025-07-04T20:56:19+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:56:19+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_012_critique_engine_output.json"}
{"asctime": "2025-07-04T20:56:19+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_012_critique_engine_output.json"}
{"asctime": "2025-07-04T20:56:19+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_013_code_generator_input.json"}
{"asctime": "2025-07-04T20:56:19+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_013_code_generator_input.json"}
{"asctime": "2025-07-04T20:56:25+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:56:25+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_014_code_generator_output.json"}
{"asctime": "2025-07-04T20:56:25+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_014_code_generator_output.json"}
{"asctime": "2025-07-04T20:56:25+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_015_critique_engine_input.json"}
{"asctime": "2025-07-04T20:56:25+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_015_critique_engine_input.json"}
{"asctime": "2025-07-04T20:56:32+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T20:56:32+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_016_critique_engine_output.json"}
{"asctime": "2025-07-04T20:56:32+0200", "name": "working_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug interaction saved: llm_016_critique_engine_output.json"}
{"asctime": "2025-07-04T20:56:32+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project generation completed with result: {'type': 'project_generation', 'success': True, 'project_name': 'create_a_python', 'project_path': 'create_a_python', 'statistics': {'total_files': 3, 'successful_files': 3, 'success_rate': 100.0, 'total_iterations': 4}, 'files': [{'path': 'factorial.py', 'success': True, 'iterations': 1, 'size': 812}, {'path': 'test_factorial.py', 'success': True, 'iterations': 1, 'size': 283}, {'path': 'README.md', 'success': True, 'iterations': 2, 'size': 159}]}"}
{"asctime": "2025-07-04T20:56:32+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project factorial_final_test completed successfully with 3 files"}
{"asctime": "2025-07-04T20:56:32+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83c\udfc1 Background processing completed for project: factorial_final_test"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "root", "levelname": "INFO", "message": "Starting Sonnet Model system"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "shared.state_manager", "levelname": "WARNING", "message": "Using in-memory state store - not suitable for production multi-worker setup"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T21:04:55+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T21:05:16+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\ude80 Starting background processing for project: factorial_with_planner"}
{"asctime": "2025-07-04T21:05:16+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udcdd User input: Create a Python function to calculate factorial with error handling"}
{"asctime": "2025-07-04T21:05:16+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug enabled: True"}
{"asctime": "2025-07-04T21:05:16+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled - LLM interactions will be saved to debug folder"}
{"asctime": "2025-07-04T21:05:16+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled for project: factorial_with_planner"}
{"asctime": "2025-07-04T21:05:16+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd27 Calling system.generate_complete_project..."}
{"asctime": "2025-07-04T21:05:16+0200", "name": "system_integration", "levelname": "INFO", "message": "Generating complete project: Create a Python function to calculate factorial with error handling"}
{"asctime": "2025-07-04T21:05:16+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug directory created: debug/llm_visibility_project_20250704_210516_20250704_210516"}
{"asctime": "2025-07-04T21:05:16+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: project_request.json"}
{"asctime": "2025-07-04T21:05:16+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Setting debug callback for IntegratedProjectGenerator (debug_enabled=True)"}
{"asctime": "2025-07-04T21:05:16+0200", "name": "integrated_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug callback set for IntegratedProjectGenerator"}
{"asctime": "2025-07-04T21:05:16+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_001_planner_input.json"}
{"asctime": "2025-07-04T21:05:23+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:05:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_001_planner_output.json"}
{"asctime": "2025-07-04T21:05:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_002_code_generator_input.json"}
{"asctime": "2025-07-04T21:05:31+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:05:31+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_002_code_generator_output.json"}
{"asctime": "2025-07-04T21:05:31+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_003_critique_engine_input.json"}
{"asctime": "2025-07-04T21:05:33+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:05:33+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_003_critique_engine_output.json"}
{"asctime": "2025-07-04T21:05:33+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_004_code_generator_input.json"}
{"asctime": "2025-07-04T21:05:46+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:05:46+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_004_code_generator_output.json"}
{"asctime": "2025-07-04T21:05:46+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_005_critique_engine_input.json"}
{"asctime": "2025-07-04T21:05:48+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:05:48+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_005_critique_engine_output.json"}
{"asctime": "2025-07-04T21:05:48+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_006_code_generator_input.json"}
{"asctime": "2025-07-04T21:05:57+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:05:57+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_006_code_generator_output.json"}
{"asctime": "2025-07-04T21:05:57+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_007_critique_engine_input.json"}
{"asctime": "2025-07-04T21:05:59+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:05:59+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_007_critique_engine_output.json"}
{"asctime": "2025-07-04T21:05:59+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project generation completed with result: {'type': 'project_generation', 'success': False, 'project_name': 'create_a_python', 'project_path': 'create_a_python', 'statistics': {'total_files': 0, 'successful_files': 0, 'success_rate': 0, 'total_iterations': 0}, 'files': [{'path': 'factorial_calculator.py', 'success': True, 'iterations': 1, 'code_length': 1075}, {'path': 'test_factorial_calculator.py', 'success': True, 'iterations': 1, 'code_length': 1600}, {'path': 'main.py', 'success': True, 'iterations': 1, 'code_length': 1212}]}"}
{"asctime": "2025-07-04T21:05:59+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project factorial_with_planner completed successfully with 0 files"}
{"asctime": "2025-07-04T21:05:59+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83c\udfc1 Background processing completed for project: factorial_with_planner"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "root", "levelname": "INFO", "message": "Starting Sonnet Model system"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "shared.state_manager", "levelname": "WARNING", "message": "Using in-memory state store - not suitable for production multi-worker setup"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T21:10:03+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T21:10:25+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\ude80 Starting background processing for project: factorial_with_real_planner"}
{"asctime": "2025-07-04T21:10:25+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udcdd User input: Create a Python function to calculate factorial with error handling"}
{"asctime": "2025-07-04T21:10:25+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug enabled: True"}
{"asctime": "2025-07-04T21:10:25+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled - LLM interactions will be saved to debug folder"}
{"asctime": "2025-07-04T21:10:25+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled for project: factorial_with_real_planner"}
{"asctime": "2025-07-04T21:10:25+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd27 Calling system.generate_complete_project..."}
{"asctime": "2025-07-04T21:10:25+0200", "name": "system_integration", "levelname": "INFO", "message": "Generating complete project: Create a Python function to calculate factorial with error handling"}
{"asctime": "2025-07-04T21:10:25+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug directory created: debug/llm_visibility_project_20250704_211025_20250704_211025"}
{"asctime": "2025-07-04T21:10:25+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: project_request.json"}
{"asctime": "2025-07-04T21:10:25+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Setting debug callback for IntegratedProjectGenerator (debug_enabled=True)"}
{"asctime": "2025-07-04T21:10:25+0200", "name": "integrated_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug callback set for IntegratedProjectGenerator"}
{"asctime": "2025-07-04T21:10:25+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_001_planner_input.json"}
{"asctime": "2025-07-04T21:10:30+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:10:30+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_001_planner_output.json"}
{"asctime": "2025-07-04T21:10:30+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_002_code_generator_input.json"}
{"asctime": "2025-07-04T21:10:38+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:10:38+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_002_code_generator_output.json"}
{"asctime": "2025-07-04T21:10:38+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_003_critique_engine_input.json"}
{"asctime": "2025-07-04T21:10:40+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:10:40+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_003_critique_engine_output.json"}
{"asctime": "2025-07-04T21:10:40+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_004_code_generator_input.json"}
{"asctime": "2025-07-04T21:10:51+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:10:51+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_004_code_generator_output.json"}
{"asctime": "2025-07-04T21:10:51+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_005_critique_engine_input.json"}
{"asctime": "2025-07-04T21:10:54+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:10:54+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_005_critique_engine_output.json"}
{"asctime": "2025-07-04T21:10:54+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_006_code_generator_input.json"}
{"asctime": "2025-07-04T21:11:04+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:11:04+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_006_code_generator_output.json"}
{"asctime": "2025-07-04T21:11:04+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_007_critique_engine_input.json"}
{"asctime": "2025-07-04T21:11:07+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:11:07+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_007_critique_engine_output.json"}
{"asctime": "2025-07-04T21:11:07+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_008_code_generator_input.json"}
{"asctime": "2025-07-04T21:11:19+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:11:19+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_008_code_generator_output.json"}
{"asctime": "2025-07-04T21:11:19+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_009_critique_engine_input.json"}
{"asctime": "2025-07-04T21:11:21+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:11:21+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_009_critique_engine_output.json"}
{"asctime": "2025-07-04T21:11:21+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_010_code_generator_input.json"}
{"asctime": "2025-07-04T21:11:31+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:11:31+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_010_code_generator_output.json"}
{"asctime": "2025-07-04T21:11:31+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_011_critique_engine_input.json"}
{"asctime": "2025-07-04T21:11:33+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:11:33+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_011_critique_engine_output.json"}
{"asctime": "2025-07-04T21:11:33+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_012_code_generator_input.json"}
{"asctime": "2025-07-04T21:11:44+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:11:44+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_012_code_generator_output.json"}
{"asctime": "2025-07-04T21:11:44+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_013_critique_engine_input.json"}
{"asctime": "2025-07-04T21:11:46+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:11:46+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_013_critique_engine_output.json"}
{"asctime": "2025-07-04T21:11:46+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_014_code_generator_input.json"}
{"asctime": "2025-07-04T21:11:53+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:11:53+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_014_code_generator_output.json"}
{"asctime": "2025-07-04T21:11:53+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_015_critique_engine_input.json"}
{"asctime": "2025-07-04T21:11:55+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:11:55+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_015_critique_engine_output.json"}
{"asctime": "2025-07-04T21:11:55+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_016_code_generator_input.json"}
{"asctime": "2025-07-04T21:12:04+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:12:04+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_016_code_generator_output.json"}
{"asctime": "2025-07-04T21:12:04+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_017_critique_engine_input.json"}
{"asctime": "2025-07-04T21:12:07+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:12:07+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_017_critique_engine_output.json"}
{"asctime": "2025-07-04T21:12:07+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_018_code_generator_input.json"}
{"asctime": "2025-07-04T21:12:17+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:12:17+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_018_code_generator_output.json"}
{"asctime": "2025-07-04T21:12:17+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_019_critique_engine_input.json"}
{"asctime": "2025-07-04T21:12:19+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:12:19+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_019_critique_engine_output.json"}
{"asctime": "2025-07-04T21:12:19+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_020_code_generator_input.json"}
{"asctime": "2025-07-04T21:12:27+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:12:27+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_020_code_generator_output.json"}
{"asctime": "2025-07-04T21:12:27+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_021_critique_engine_input.json"}
{"asctime": "2025-07-04T21:12:30+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:12:30+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_021_critique_engine_output.json"}
{"asctime": "2025-07-04T21:12:30+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_022_code_generator_input.json"}
{"asctime": "2025-07-04T21:12:38+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:12:39+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_022_code_generator_output.json"}
{"asctime": "2025-07-04T21:12:39+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_023_critique_engine_input.json"}
{"asctime": "2025-07-04T21:12:41+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:12:41+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_023_critique_engine_output.json"}
{"asctime": "2025-07-04T21:12:41+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project generation completed with result: {'type': 'project_generation', 'success': False, 'project_name': 'create_a_python', 'project_path': 'create_a_python', 'statistics': {'total_files': 0, 'successful_files': 0, 'success_rate': 0, 'total_iterations': 0}, 'files': [{'path': 'factorial_calculator.py', 'success': True, 'iterations': 1, 'code_length': 944}, {'path': 'test_factorial_calculator.py', 'success': True, 'iterations': 3, 'code_length': 1943}, {'path': 'main.py', 'success': True, 'iterations': 1, 'code_length': 1079}, {'path': 'README.md', 'success': True, 'iterations': 6, 'code_length': 710}]}"}
{"asctime": "2025-07-04T21:12:41+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project factorial_with_real_planner completed successfully with 0 files"}
{"asctime": "2025-07-04T21:12:41+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83c\udfc1 Background processing completed for project: factorial_with_real_planner"}
{"asctime": "2025-07-04T21:25:37+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\ude80 Starting background processing for project: test_normal_mode"}
{"asctime": "2025-07-04T21:25:37+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udcdd User input: Create a simple calculator class with basic operations"}
{"asctime": "2025-07-04T21:25:37+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug enabled: False"}
{"asctime": "2025-07-04T21:25:37+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd27 Calling system.generate_complete_project..."}
{"asctime": "2025-07-04T21:25:37+0200", "name": "system_integration", "levelname": "INFO", "message": "Generating complete project: Create a simple calculator class with basic operations"}
{"asctime": "2025-07-04T21:25:37+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug directory created: debug/llm_visibility_project_20250704_212537_20250704_212537"}
{"asctime": "2025-07-04T21:25:37+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: project_request.json"}
{"asctime": "2025-07-04T21:25:37+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Setting debug callback for IntegratedProjectGenerator (debug_enabled=True)"}
{"asctime": "2025-07-04T21:25:37+0200", "name": "integrated_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug callback set for IntegratedProjectGenerator"}
{"asctime": "2025-07-04T21:25:37+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_001_planner_input.json"}
{"asctime": "2025-07-04T21:25:46+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:25:46+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_001_planner_output.json"}
{"asctime": "2025-07-04T21:25:46+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_002_code_generator_input.json"}
{"asctime": "2025-07-04T21:25:59+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:25:59+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_002_code_generator_output.json"}
{"asctime": "2025-07-04T21:25:59+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_003_critique_engine_input.json"}
{"asctime": "2025-07-04T21:26:02+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:26:02+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_003_critique_engine_output.json"}
{"asctime": "2025-07-04T21:26:02+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_004_code_generator_input.json"}
{"asctime": "2025-07-04T21:26:18+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:26:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_004_code_generator_output.json"}
{"asctime": "2025-07-04T21:26:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_005_critique_engine_input.json"}
{"asctime": "2025-07-04T21:26:21+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:26:21+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_005_critique_engine_output.json"}
{"asctime": "2025-07-04T21:26:21+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_006_code_generator_input.json"}
{"asctime": "2025-07-04T21:26:36+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:26:36+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_006_code_generator_output.json"}
{"asctime": "2025-07-04T21:26:36+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_007_critique_engine_input.json"}
{"asctime": "2025-07-04T21:26:39+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:26:39+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_007_critique_engine_output.json"}
{"asctime": "2025-07-04T21:26:39+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_008_code_generator_input.json"}
{"asctime": "2025-07-04T21:26:53+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:26:53+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_008_code_generator_output.json"}
{"asctime": "2025-07-04T21:26:53+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_009_critique_engine_input.json"}
{"asctime": "2025-07-04T21:26:56+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:26:56+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_009_critique_engine_output.json"}
{"asctime": "2025-07-04T21:26:56+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_010_code_generator_input.json"}
{"asctime": "2025-07-04T21:27:08+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:27:08+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_010_code_generator_output.json"}
{"asctime": "2025-07-04T21:27:08+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_011_critique_engine_input.json"}
{"asctime": "2025-07-04T21:27:10+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:27:10+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_011_critique_engine_output.json"}
{"asctime": "2025-07-04T21:27:10+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_012_code_generator_input.json"}
{"asctime": "2025-07-04T21:27:21+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:27:21+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_012_code_generator_output.json"}
{"asctime": "2025-07-04T21:27:21+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_013_critique_engine_input.json"}
{"asctime": "2025-07-04T21:27:24+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:27:24+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_013_critique_engine_output.json"}
{"asctime": "2025-07-04T21:27:24+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_014_code_generator_input.json"}
{"asctime": "2025-07-04T21:27:30+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:27:30+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_014_code_generator_output.json"}
{"asctime": "2025-07-04T21:27:30+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_015_critique_engine_input.json"}
{"asctime": "2025-07-04T21:27:32+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:27:32+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_015_critique_engine_output.json"}
{"asctime": "2025-07-04T21:27:32+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_016_code_generator_input.json"}
{"asctime": "2025-07-04T21:27:45+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:27:45+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_016_code_generator_output.json"}
{"asctime": "2025-07-04T21:27:45+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_017_critique_engine_input.json"}
{"asctime": "2025-07-04T21:27:48+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:27:48+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_017_critique_engine_output.json"}
{"asctime": "2025-07-04T21:27:48+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_018_code_generator_input.json"}
{"asctime": "2025-07-04T21:27:56+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:27:56+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_018_code_generator_output.json"}
{"asctime": "2025-07-04T21:27:56+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_019_critique_engine_input.json"}
{"asctime": "2025-07-04T21:27:59+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:27:59+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_019_critique_engine_output.json"}
{"asctime": "2025-07-04T21:27:59+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_020_code_generator_input.json"}
{"asctime": "2025-07-04T21:28:06+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:28:06+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_020_code_generator_output.json"}
{"asctime": "2025-07-04T21:28:06+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_021_critique_engine_input.json"}
{"asctime": "2025-07-04T21:28:08+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:28:08+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_021_critique_engine_output.json"}
{"asctime": "2025-07-04T21:28:08+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_022_code_generator_input.json"}
{"asctime": "2025-07-04T21:28:19+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:28:19+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_022_code_generator_output.json"}
{"asctime": "2025-07-04T21:28:19+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_023_critique_engine_input.json"}
{"asctime": "2025-07-04T21:28:22+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:28:22+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_023_critique_engine_output.json"}
{"asctime": "2025-07-04T21:28:22+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project generation completed with result: {'type': 'project_generation', 'success': False, 'project_name': 'create_a_simple', 'project_path': 'create_a_simple', 'statistics': {'total_files': 0, 'successful_files': 0, 'success_rate': 0, 'total_iterations': 0}, 'files': [{'path': 'src/calculator.py', 'success': True, 'iterations': 1, 'code_length': 2491}, {'path': 'tests/test_calculator.py', 'success': True, 'iterations': 3, 'code_length': 2501}, {'path': 'docs/README.md', 'success': True, 'iterations': 6, 'code_length': 563}, {'path': 'src/main.py', 'success': True, 'iterations': 1, 'code_length': 2054}]}"}
{"asctime": "2025-07-04T21:28:22+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project test_normal_mode completed successfully with 0 files"}
{"asctime": "2025-07-04T21:28:22+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83c\udfc1 Background processing completed for project: test_normal_mode"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "root", "levelname": "INFO", "message": "Starting Sonnet Model system"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "shared.state_manager", "levelname": "WARNING", "message": "Using in-memory state store - not suitable for production multi-worker setup"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T21:30:56+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T21:31:19+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\ude80 Starting background processing for project: test_normal_mode_clean"}
{"asctime": "2025-07-04T21:31:19+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udcdd User input: Create a simple hello world function"}
{"asctime": "2025-07-04T21:31:19+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug enabled: False"}
{"asctime": "2025-07-04T21:31:19+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd27 Calling system.generate_complete_project..."}
{"asctime": "2025-07-04T21:31:19+0200", "name": "system_integration", "levelname": "INFO", "message": "Generating complete project: Create a simple hello world function"}
{"asctime": "2025-07-04T21:31:19+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug callback NOT set (debug_enabled=False, has_method=True)"}
{"asctime": "2025-07-04T21:31:28+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:31:42+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:31:45+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:31:58+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:32:00+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:32:13+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:32:17+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:32:30+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:32:33+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:32:47+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:32:50+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:33:01+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:33:05+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:33:25+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:33:27+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:33:49+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:33:53+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:34:09+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:34:12+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:34:44+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "root", "levelname": "INFO", "message": "Logging configured with level INFO"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "root", "levelname": "INFO", "message": "Starting Sonnet Model system"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "shared.state_manager", "levelname": "WARNING", "message": "Using in-memory state store - not suitable for production multi-worker setup"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: []"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: []"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: NOT_SET"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: NOT_SET"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "shared.conversation_manager", "levelname": "INFO", "message": "Enhanced conversation manager initialized with dynamic mode"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "CodeGenerator config keys: ['llm', 'conversation', 'timeout_seconds', 'max_retries', 'cache_enabled', 'cache_ttl_seconds']"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM config keys: ['type', 'api_url', 'model', 'temperature', 'max_tokens', 'timeout']"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM type: http_api"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "code_generator.services.code_generator", "levelname": "INFO", "message": "LLM model: deepseek-coder-v2:16b"}
{"asctime": "2025-07-04T21:34:44+0200", "name": "system_integration", "levelname": "INFO", "message": "Agentic System initialized with persistent coaching (stateless mode)"}
{"asctime": "2025-07-04T21:35:23+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\ude80 Starting background processing for project: debug_test_math_utils"}
{"asctime": "2025-07-04T21:35:23+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udcdd User input: Create a simple math utility function"}
{"asctime": "2025-07-04T21:35:23+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug enabled: True"}
{"asctime": "2025-07-04T21:35:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled - LLM interactions will be saved to debug folder"}
{"asctime": "2025-07-04T21:35:23+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd0d Debug mode enabled for project: debug_test_math_utils"}
{"asctime": "2025-07-04T21:35:23+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83d\udd27 Calling system.generate_complete_project..."}
{"asctime": "2025-07-04T21:35:23+0200", "name": "system_integration", "levelname": "INFO", "message": "Generating complete project: Create a simple math utility function"}
{"asctime": "2025-07-04T21:35:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Debug directory created: debug/llm_visibility_project_20250704_213523_20250704_213523"}
{"asctime": "2025-07-04T21:35:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: project_request.json"}
{"asctime": "2025-07-04T21:35:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udd0d Setting debug callback for IntegratedProjectGenerator (debug_enabled=True)"}
{"asctime": "2025-07-04T21:35:23+0200", "name": "integrated_project_generator", "levelname": "INFO", "message": "\ud83d\udd0d Debug callback set for IntegratedProjectGenerator"}
{"asctime": "2025-07-04T21:35:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_001_planner_input.json"}
{"asctime": "2025-07-04T21:35:31+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:35:31+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_001_planner_output.json"}
{"asctime": "2025-07-04T21:35:31+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_002_code_generator_input.json"}
{"asctime": "2025-07-04T21:35:44+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:35:44+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_002_code_generator_output.json"}
{"asctime": "2025-07-04T21:35:44+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_003_critique_engine_input.json"}
{"asctime": "2025-07-04T21:35:47+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:35:47+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_003_critique_engine_output.json"}
{"asctime": "2025-07-04T21:35:47+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_004_code_generator_input.json"}
{"asctime": "2025-07-04T21:36:15+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 500 Internal Server Error\""}
{"asctime": "2025-07-04T21:36:15+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_005_code_generator_input.json"}
{"asctime": "2025-07-04T21:36:36+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:36:36+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_005_code_generator_output.json"}
{"asctime": "2025-07-04T21:36:36+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_006_critique_engine_input.json"}
{"asctime": "2025-07-04T21:36:39+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:36:39+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_006_critique_engine_output.json"}
{"asctime": "2025-07-04T21:36:39+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_007_code_generator_input.json"}
{"asctime": "2025-07-04T21:36:56+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 500 Internal Server Error\""}
{"asctime": "2025-07-04T21:36:56+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_008_code_generator_input.json"}
{"asctime": "2025-07-04T21:37:14+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:37:14+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_008_code_generator_output.json"}
{"asctime": "2025-07-04T21:37:14+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_009_critique_engine_input.json"}
{"asctime": "2025-07-04T21:37:18+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:37:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_009_critique_engine_output.json"}
{"asctime": "2025-07-04T21:37:18+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_010_code_generator_input.json"}
{"asctime": "2025-07-04T21:37:31+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:37:31+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_010_code_generator_output.json"}
{"asctime": "2025-07-04T21:37:31+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_011_critique_engine_input.json"}
{"asctime": "2025-07-04T21:37:34+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:37:34+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_011_critique_engine_output.json"}
{"asctime": "2025-07-04T21:37:34+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_012_code_generator_input.json"}
{"asctime": "2025-07-04T21:37:47+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:37:47+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_012_code_generator_output.json"}
{"asctime": "2025-07-04T21:37:47+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_013_critique_engine_input.json"}
{"asctime": "2025-07-04T21:37:50+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:37:50+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_013_critique_engine_output.json"}
{"asctime": "2025-07-04T21:37:50+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_014_code_generator_input.json"}
{"asctime": "2025-07-04T21:38:03+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:38:03+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_014_code_generator_output.json"}
{"asctime": "2025-07-04T21:38:03+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_015_critique_engine_input.json"}
{"asctime": "2025-07-04T21:38:07+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:38:07+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_015_critique_engine_output.json"}
{"asctime": "2025-07-04T21:38:07+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_016_code_generator_input.json"}
{"asctime": "2025-07-04T21:38:20+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:38:20+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_016_code_generator_output.json"}
{"asctime": "2025-07-04T21:38:20+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_017_critique_engine_input.json"}
{"asctime": "2025-07-04T21:38:23+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:38:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_017_critique_engine_output.json"}
{"asctime": "2025-07-04T21:38:23+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_018_code_generator_input.json"}
{"asctime": "2025-07-04T21:38:36+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:38:36+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_018_code_generator_output.json"}
{"asctime": "2025-07-04T21:38:36+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_019_critique_engine_input.json"}
{"asctime": "2025-07-04T21:38:39+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:38:39+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_019_critique_engine_output.json"}
{"asctime": "2025-07-04T21:38:39+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_020_code_generator_input.json"}
{"asctime": "2025-07-04T21:38:50+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:38:50+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_020_code_generator_output.json"}
{"asctime": "2025-07-04T21:38:50+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_021_critique_engine_input.json"}
{"asctime": "2025-07-04T21:38:52+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:38:52+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_021_critique_engine_output.json"}
{"asctime": "2025-07-04T21:38:52+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_022_code_generator_input.json"}
{"asctime": "2025-07-04T21:39:02+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:39:02+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_022_code_generator_output.json"}
{"asctime": "2025-07-04T21:39:02+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_023_critique_engine_input.json"}
{"asctime": "2025-07-04T21:39:04+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:39:04+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_023_critique_engine_output.json"}
{"asctime": "2025-07-04T21:39:04+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_024_code_generator_input.json"}
{"asctime": "2025-07-04T21:39:25+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:39:25+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_024_code_generator_output.json"}
{"asctime": "2025-07-04T21:39:25+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_025_critique_engine_input.json"}
{"asctime": "2025-07-04T21:39:27+0200", "name": "httpx", "levelname": "INFO", "message": "HTTP Request: POST http://localhost:11434/api/generate \"HTTP/1.1 200 OK\""}
{"asctime": "2025-07-04T21:39:27+0200", "name": "system_integration", "levelname": "INFO", "message": "\ud83d\udcc4 Debug file saved: llm_025_critique_engine_output.json"}
{"asctime": "2025-07-04T21:39:27+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project generation completed with result: {'type': 'project_generation', 'success': False, 'project_name': 'create_a_simple', 'project_path': 'create_a_simple', 'statistics': {'total_files': 0, 'successful_files': 0, 'success_rate': 0, 'total_iterations': 0}, 'files': [{'path': 'backend/models.py', 'success': True, 'iterations': 1, 'code_length': 1942}, {'path': 'backend/api.py', 'success': False, 'iterations': 1, 'code_length': 0}, {'path': 'backend/utils/math_utils.py', 'success': False, 'iterations': 2, 'code_length': 2690}, {'path': 'backend/config/settings.py', 'success': True, 'iterations': 6, 'code_length': 2000}, {'path': 'backend/tests/test_math_utils.py', 'success': True, 'iterations': 2, 'code_length': 1592}, {'path': 'backend/migrations/...', 'success': True, 'iterations': 1, 'code_length': 844}]}"}
{"asctime": "2025-07-04T21:39:27+0200", "name": "api.routes", "levelname": "INFO", "message": "\u2705 Project debug_test_math_utils completed successfully with 0 files"}
{"asctime": "2025-07-04T21:39:27+0200", "name": "api.routes", "levelname": "INFO", "message": "\ud83c\udfc1 Background processing completed for project: debug_test_math_utils"}
