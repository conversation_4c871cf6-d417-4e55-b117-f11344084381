# Sonnet Model - AI-Powered Code Generation & Critique System

[![Tests](https://img.shields.io/badge/tests-100%25%20passing-brightgreen)](./test_complete_system.py)
[![Python](https://img.shields.io/badge/python-3.8%2B-blue)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green)](LICENSE)

**Sonnet Model** is a comprehensive AI-powered system for automated code generation, intelligent critique, and persistent project completion. It features aggressive LLM coaching that ensures projects are completed to the highest standards without premature stopping.

## 📚 **Complete Documentation**

### **📖 Essential Guides**
- **[📋 Documentation Index](INDEX.md)** - Complete guide to all documentation
- **[🛠️ System Management](SYSTEM_MANAGEMENT.md)** - Start, stop, and manage the system
- **[🎯 Task Execution Guide](TASK_EXECUTION_GUIDE.md)** - Execute real development tasks
- **[⚡ Enhanced Features](ENHANCED_FEATURES_GUIDE.md)** - Cloud LLM & dynamic conversation management

### **🚀 Quick Start Scripts**
```bash
# Start the complete system
./scripts/start_system.sh

# Check system status
./scripts/system_status.sh

# Stop system and free GPU memory
./scripts/stop_system.sh
```

## 🚀 Key Features

### **🧠 Persistent LLM Coaching**
- **Never-Stop Architecture**: Aggressive coaching system that prevents LLM hesitation
- **Completion Verification**: Blocks premature completion claims until truly done
- **Error Recovery**: Transforms errors into stepping stones, not stopping points
- **Dynamic Conversation Management**: Intelligent conversation length management based on LLM signals
- **Context Preservation**: Smart context preservation during conversation resets

### **🤖 Advanced Code Generation**
- **Multi-Provider LLM Support**: Local (Ollama, llama.cpp, vLLM) + Cloud (OpenAI, Anthropic, Azure, Google)
- **Dynamic Provider Selection**: Automatic fallback between providers
- **Multi-Language Support**: Python, JavaScript, TypeScript, Java, C++, Go, Rust
- **Intelligent Prompting**: Context-aware prompt building with examples and constraints
- **Code Formatting**: Automatic code formatting and style enforcement

### **🔍 Expert Code Critique**
- **Multi-Layer Analysis**: Static analysis + LLM-based critique + Expert rules
- **Specialized Expertise**: Deep knowledge of Python, OpenCL, PyOpenCL, C++
- **Performance Optimization**: GPU acceleration suggestions and memory optimization
- **Security & Quality**: Comprehensive security scanning and quality assessment

### **📊 Project Management**
- **Task Orchestration**: Intelligent task prioritization and dependency management
- **State Persistence**: Conversation state management across sessions
- **Progress Tracking**: Real-time project completion tracking
- **Quality Assurance**: Ensures all tasks meet quality thresholds before completion

## Architecture

![Sonnet Model Architecture](docs/images/architecture.png)

### Core Components

1. **Task Manager/Orchestrator**
   - Maintains the todo/plan state
   - Tracks completion status of each task
   - Routes tasks between components
   - Decides when to terminate the loop

2. **Code Generation Agent**
   - Interfaces with LLM for code generation
   - Takes requirements from Task Manager
   - Incorporates feedback from Critique Engine

3. **Critique Engine**
   - Runs tests on generated code
   - Provides detailed feedback
   - Uses local LLM models for code review
   - **Auto-fixing capabilities**: Automatically fixes common issues like:
     - Syntax errors (indentation, whitespace)
     - Style violations (variable naming, import organization)
     - Basic security issues
     - Performance optimizations
     - Documentation improvements

## Technology Stack

- **Core Framework**: Python with asyncio for async communication
- **State Management**: Redis/SQLite
- **API Layer**: FastAPI
- **Containerization**: Docker
- **Local LLM Models**: 
  - Primary: CodeLlama-34B-Instruct
  - Fallback: DeepSeek-Coder-33B-Instruct
  - Lightweight: WizardCoder-15B

## Getting Started

### Prerequisites

- Python 3.11+
- Docker and Docker Compose
- CUDA-compatible GPU (for local LLM inference)

### Installation & Quick Start

1. **Clone and navigate:**
```bash
git clone https://github.com/yourusername/sonnet_model.git
cd sonnet_model
```

2. **Start the complete system (includes dependency installation):**
```bash
./scripts/start_system.sh
```

3. **Verify system is ready:**
```bash
./scripts/system_status.sh
```

4. **Execute your first task:**
```bash
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "first_task",
    "description": "A Python function to calculate factorial with error handling",
    "user_input": "Create a Python function to calculate factorial with error handling"
  }'
```

**For detailed setup instructions, see [System Management Guide](SYSTEM_MANAGEMENT.md)**

## 🚀 **HOW TO RUN A COMPLETE TASK THROUGH THE WHOLE SYSTEM**

### **📋 STEP-BY-STEP INSTRUCTIONS**

#### **1. 🏁 START THE SYSTEM**

```bash
# Navigate to the project directory
cd sonnet_model

# Start the complete system (this will install dependencies and start all services)
./scripts/start_system.sh
```

#### **2. ✅ VERIFY SYSTEM IS READY**

```bash
# Check if all services are running
./scripts/system_status.sh

# Or check via API
curl http://localhost:8000/health
```

#### **3. 🎯 SUBMIT A TASK SENTENCE**

**Option A: Using curl (Command Line)**
```bash
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my_plotting_app",
    "description": "Interactive plotting application",
    "user_input": "create a small interactive gui for interactive plotting with multiple chart types and data export functionality"
  }'
```

**Option B: Using Python script**
```python
import requests
import json

# Submit task to the system
response = requests.post(
    "http://localhost:8000/api/v1/projects",
    json={
        "name": "my_web_scraper",
        "description": "Web scraping tool",
        "user_input": "create a web scraper that can extract product information from e-commerce websites with rate limiting and error handling"
    }
)

print(f"Project started: {response.json()}")
```

**Option C: Using the Web Interface**
```bash
# Open in browser
open http://localhost:8000/api/docs

# Use the interactive API documentation to submit tasks
```

#### **4. 📊 MONITOR TASK PROGRESS**

```bash
# Check system status
curl http://localhost:8000/api/v1/status

# Check specific project status
curl http://localhost:8000/api/v1/projects/my_plotting_app/status

# Get project results (after completion)
curl http://localhost:8000/api/v1/projects/my_plotting_app/results
```

#### **5. 🔍 VIEW RESULTS**

The system will create a project folder with all generated files:
```bash
# Check generated project
ls -la sonnet_model/generated_projects/my_plotting_app/

# View the main generated file
cat sonnet_model/generated_projects/my_plotting_app/main.py
```

---

## 🎯 **EXAMPLE TASK SENTENCES TO TEST**

### **🖥️ GUI Applications**
```bash
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "calculator_app",
    "description": "A scientific calculator with a modern GUI interface",
    "user_input": "create a scientific calculator with a modern GUI interface"
  }'
```

### **🌐 Web Applications**
```bash
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "todo_api",
    "user_input": "create a REST API for a todo application with user authentication and database storage"
  }'
```

### **📊 Data Processing**
```bash
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "data_analyzer",
    "user_input": "create a data analysis tool that can read CSV files and generate statistical reports with visualizations"
  }'
```

### **🤖 Machine Learning**
```bash
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "image_classifier",
    "user_input": "create an image classification system using deep learning with a web interface for uploading images"
  }'
```

---

## 🔄 **WHAT HAPPENS DURING PROCESSING**

When you submit a task, the system will:

1. **📋 Parse the task** → Extract requirements and create structured plan
2. **🤖 Generate initial code** → Create working implementation
3. **🔍 Critique the code** → Analyze quality, security, performance
4. **🔄 Apply feedback** → Improve code based on critique
5. **✅ Verify completion** → Ensure all requirements are met
6. **📁 Save results** → Create project folder with all files

---

## 📁 **WHERE TO FIND RESULTS**

Generated projects are saved in:
```
sonnet_model/
├── generated_projects/
│   ├── my_plotting_app/
│   │   ├── main.py
│   │   ├── requirements.txt
│   │   ├── README.md
│   │   └── tests/
│   └── my_web_scraper/
│       ├── scraper.py
│       ├── config.py
│       └── ...
```

---

## 🛑 **STOP THE SYSTEM**

When you're done testing:
```bash
# Stop all services and free GPU memory
./scripts/stop_system.sh
```

---

## 🎯 **QUICK TEST COMMAND**

Here's a one-liner to test the complete system:

```bash
# Start system, submit task, and monitor
./scripts/start_system.sh && sleep 10 && \
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Content-Type: application/json" \
  -d '{"name": "test_task", "user_input": "create a simple file organizer that sorts files by extension"}' && \
watch -n 5 'curl -s http://localhost:8000/api/v1/status | jq'
```

This will start the system, submit a test task, and monitor the progress every 5 seconds!

## Usage

### Quick Start

1. **Start the system:**
   ```bash
   python main.py
   ```

2. **Access the API:**
   - Web interface: http://localhost:8000
   - API documentation: http://localhost:8000/docs
   - Health check: http://localhost:8000/health

### API Endpoints

#### Code Generation
```bash
# Generate code
curl -X POST "http://localhost:8000/api/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "my_task_001",
    "description": "Create a function that calculates fibonacci numbers",
    "language": "python",
    "framework": "none"
  }'
```

#### Code Critique
```bash
# Critique code
curl -X POST "http://localhost:8000/api/critique" \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "my_task_002",
    "request_id": "req_001",
    "files": [{
      "filename": "fibonacci.py",
      "content": "def fib(n):\n    return n if n <= 1 else fib(n-1) + fib(n-2)",
      "language": "python"
    }],
    "categories": ["quality", "performance"]
  }'
```

#### Auto-fixing
```bash
# Auto-fix code issues
curl -X POST "http://localhost:8000/api/auto-fix" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "def hello():\nprint(\"Hello World\")",
    "language": "python",
    "issues": [{
      "id": "indent_1",
      "title": "Indentation Error",
      "description": "Missing indentation",
      "severity": "error",
      "line_start": 2,
      "column_start": 1,
      "file_path": "test.py"
    }]
  }'
```

### Configuration

The system can be configured via environment variables or `config/config.yaml`:

#### **LLM Provider Configuration**

**Local LLM (Ollama):**
```yaml
code_generator:
  llm:
    type: "http_api"
    api_url: "http://localhost:11434/api/generate"
    model: "deepseek-coder-v2:16b"
    temperature: 0.2
    max_tokens: 4096
```

**OpenAI:**
```yaml
code_generator:
  llm:
    type: "openai"
    api_key: "${OPENAI_API_KEY}"  # Set via environment variable
    model: "gpt-4"  # or "gpt-3.5-turbo", "gpt-4-turbo"
    temperature: 0.2
    max_tokens: 4096
```

**Anthropic Claude:**
```yaml
code_generator:
  llm:
    type: "anthropic"
    api_key: "${ANTHROPIC_API_KEY}"  # Set via environment variable
    model: "claude-3-sonnet-20240229"  # or "claude-3-opus-20240229"
    temperature: 0.2
    max_tokens: 4096
```

**Azure OpenAI:**
```yaml
code_generator:
  llm:
    type: "azure_openai"
    api_key: "${AZURE_OPENAI_API_KEY}"
    api_base: "${AZURE_OPENAI_ENDPOINT}"
    deployment_name: "${AZURE_DEPLOYMENT_NAME}"
    api_version: "2023-12-01-preview"
    temperature: 0.2
    max_tokens: 4096
```

#### **Conversation Management Configuration**

**Dynamic Conversation Length:**
```yaml
code_generator:
  conversation:
    max_length_mode: "signal_based"  # "fixed", "dynamic", "signal_based"
    max_length_dynamic_min: 30
    max_length_dynamic_max: 100
    reset_signals:
      - "conversation is getting long"
      - "context is full"
      - "running out of space"
    preserve_context: true
    context_summary_length: 1000
```

**Environment Variables:**
```bash
# OpenAI
export OPENAI_API_KEY="your-openai-api-key"

# Anthropic
export ANTHROPIC_API_KEY="your-anthropic-api-key"

# Azure OpenAI
export AZURE_OPENAI_API_KEY="your-azure-key"
export AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com"
export AZURE_DEPLOYMENT_NAME="your-deployment-name"

# Google PaLM
export GOOGLE_PALM_API_KEY="your-palm-api-key"

# Redis for multi-worker state management
export REDIS_URL="redis://localhost:6379"
export USE_REDIS="true"
```

### Docker Usage

```bash
# Build and run with Docker
docker-compose up --build

# Run in production mode
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up
```

### Programming Examples

#### Python Integration
```python
from system_integration import AgenticSystem

# Initialize the system
system = AgenticSystem()

# Generate code
result = await system.generate_code(
    task_id="example_001",
    description="Create a REST API endpoint for user management",
    language="python",
    framework="fastapi"
)

# Critique and auto-fix
critique_result = await system.critique_code(
    task_id="example_002",
    files=[{"filename": "api.py", "content": result.code}]
)

if critique_result.issues:
    fixed_result = await system.auto_fix_code(
        code=result.code,
        issues=critique_result.issues,
        language="python"
    )
```

## 📚 **Complete Documentation**

### **📖 Essential Guides**
- **[📋 Documentation Index](INDEX.md)** - Complete guide to all documentation
- **[🛠️ System Management](SYSTEM_MANAGEMENT.md)** - Start, stop, and manage the system
- **[🎯 Task Execution Guide](TASK_EXECUTION_GUIDE.md)** - Execute real development tasks
- **[⚡ Enhanced Features](ENHANCED_FEATURES_GUIDE.md)** - Cloud LLM & dynamic conversation management

### **🏗️ Technical Documentation**
- **[Architecture Overview](ARCHITECTURE.md)** - System design and component interaction
- **[Critical Fixes Summary](CRITICAL_FIXES_SUMMARY.md)** - Recent architectural improvements

### **🛠️ System Management Scripts**
```bash
./scripts/start_system.sh      # 🚀 Start all services
./scripts/stop_system.sh       # 🛑 Stop all services gracefully
./scripts/system_status.sh     # 📊 Comprehensive status check
./scripts/free_gpu_memory.sh   # 🎮 Free GPU memory and resources
```

### **🎯 Quick Reference**
- **API Documentation:** http://localhost:8000/docs (when running)
- **Health Check:** http://localhost:8000/health
- **System Status:** http://localhost:8000/api/v1/status

## License

MIT
