# models.py
from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EmailValidator

class User(AbstractUser):
    email = models.EmailField(unique=True)
    phone_number = models.CharField(max_length=15, unique=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'phone_number']

    def __str__(self):
        return self.username

class CoreDataModel(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class RelationshipModel(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    core_data = models.ForeignKey(CoreDataModel, on_delete=models.CASCADE)
    value = models.IntegerField()

    class Meta:
        unique_together = ('user', 'core_data')

    def clean(self):
        if self.value < 0:
            raise ValidationError({'value': 'Value must be non-negative'})

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

class CustomValidationError(Exception):
    pass

# Add validation methods to User model
def validate_phone_number(value):
    if not value.startswith('+1'):
        raise ValidationError('Phone number must start with +1')

User._meta.get_field('phone_number').validators.append(validate_phone_number)

# Add validation methods to CoreDataModel
def validate_name_length(value):
    if len(value) < 3:
        raise ValidationError('Name must be at least 3 characters long')

User._meta.get_field('username').validators.append(MinLengthValidator(3))
CoreDataModel._meta.get_field('name').validators.append(validate_name_length)

# Add validation methods to RelationshipModel
def validate_value_range(value):
    if value < 0 or value > 100:
        raise ValidationError('Value must be between 0 and 100')

RelationshipModel._meta.get_field('value').validators.append(validate_value_range)