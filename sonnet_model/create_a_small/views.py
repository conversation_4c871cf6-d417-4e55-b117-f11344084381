# views.py
from flask import Flask, render_template, request, redirect, url_for, flash

app = Flask(__name__)
app.secret_key = 'your_secret_key'  # Replace with a secure secret key for production

# Home route
@app.route('/')
def home():
    """Render the home page."""
    return render_template('home.html')

# Example form processing
@app.route('/submit', methods=['POST'])
def submit():
    """Process the form submission and handle errors."""
    name = request.form['name']
    if not name:
        flash("Name is required!", "error")
        return redirect(url_for('home'))
    
    # Process the form data (e.g., save to database)
    # For demonstration, we'll just show a success message
    flash("Form submitted successfully!", "success")
    return redirect(url_for('home'))

# Error handling
@app.errorhandler(404)
def page_not_found(e):
    """Render the 404 error page."""
    return render_template('404.html'), 404

if __name__ == '__main__':
    app.run(debug=True)