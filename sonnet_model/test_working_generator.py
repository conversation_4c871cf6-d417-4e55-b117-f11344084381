#!/usr/bin/env python3
"""
Test script to verify WorkingProjectGenerator debug functionality
"""

import asyncio
import json
import os
from pathlib import Path
from working_project_generator import WorkingProjectGenerator

def test_debug_callback(filename: str, content: dict):
    """Test debug callback function"""
    print(f"🔍 DEBUG CALLBACK CALLED: {filename}")
    print(f"📄 Content keys: {list(content.keys())}")
    
    # Save to test debug folder
    debug_dir = Path("test_debug")
    debug_dir.mkdir(exist_ok=True)
    
    file_path = debug_dir / filename
    with open(file_path, "w") as f:
        json.dump(content, f, indent=2, default=str)
    
    print(f"✅ Saved to: {file_path}")

async def test_debug_system():
    """Test the debug system with WorkingProjectGenerator"""
    
    print("🧪 Testing WorkingProjectGenerator debug system...")
    
    # Clean up previous test
    test_debug_dir = Path("test_debug")
    if test_debug_dir.exists():
        import shutil
        shutil.rmtree(test_debug_dir)
    
    # Create generator
    generator = WorkingProjectGenerator()
    
    # Set debug callback
    generator.set_debug_callback(test_debug_callback)
    print("🔍 Debug callback set")
    
    # Test description
    description = "Create a simple hello world Python script that prints a greeting message"
    print(f"📝 Description: {description}")
    
    print("🚀 Starting generation...")
    result = await generator.create_project_from_description(description)
    
    print("✅ Generation completed!")
    print(f"📊 Result: {result}")
    
    # Check debug files
    if test_debug_dir.exists():
        debug_files = list(test_debug_dir.glob("*.json"))
        print(f"📁 Debug files created: {len(debug_files)}")
        for file in debug_files:
            print(f"  - {file.name}")
    else:
        print("❌ No debug directory created")

if __name__ == "__main__":
    asyncio.run(test_debug_system())
