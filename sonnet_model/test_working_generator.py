#!/usr/bin/env python3

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from working_project_generator import WorkingProjectGenerator
from system_integration import AgenticSystem
from utils.config_loader import load_config

async def test_generator():
    """Test the WorkingProjectGenerator directly"""

    print("🧪 Testing WorkingProjectGenerator directly...")

    generator = WorkingProjectGenerator()

    # Test with a simple project
    description = "Create a simple hello world Python script that prints a greeting message"

    print(f"📝 Description: {description}")
    print("🚀 Starting generation...")

    try:
        result = await generator.create_project_from_description(description)

        print("\n✅ Generation completed!")
        print(f"📊 Result: {result}")

        # Check if project directory was created
        project_path = Path(result.get('project_path', ''))
        if project_path.exists():
            print(f"📁 Project directory exists: {project_path}")
            files = list(project_path.glob('*'))
            print(f"📄 Files created: {[f.name for f in files]}")
        else:
            print(f"❌ Project directory not found: {project_path}")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

async def test_system_integration():
    """Test the system integration generate_complete_project method"""

    print("\n🧪 Testing System Integration generate_complete_project...")

    try:
        # Load config
        config = load_config("config/config.yaml")

        # Initialize system
        system = AgenticSystem(config, debug_enabled=False)

        # Test with a simple project
        description = "Create a simple todo list Python script"

        print(f"📝 Description: {description}")
        print("🚀 Starting system generation...")

        result = await system.generate_complete_project(description)

        print("\n✅ System generation completed!")
        print(f"📊 Result: {result}")

        # Check if project directory was created
        project_path = Path(result.get('project_path', ''))
        if project_path.exists():
            print(f"📁 Project directory exists: {project_path}")
            files = list(project_path.glob('*'))
            print(f"📄 Files created: {[f.name for f in files]}")
        else:
            print(f"❌ Project directory not found: {project_path}")

    except Exception as e:
        print(f"❌ System Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_generator())
    asyncio.run(test_system_integration())
