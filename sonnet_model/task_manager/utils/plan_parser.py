"""
Plan Parser - Parses user input into structured plans and tasks
"""

import asyncio
import re
import uuid
from typing import Dict, List, Tuple, Optional
from datetime import datetime

from ..models.plan import Plan, PlanStatus
from ..models.task import Task, TaskPriority, TaskStatus


class PlanParser:
    """Parses user input into structured plans and tasks"""
    
    def __init__(self):
        self.task_keywords = [
            'create', 'implement', 'build', 'develop', 'write', 'add', 'design',
            'setup', 'configure', 'install', 'test', 'deploy', 'fix', 'update'
        ]
        
        self.priority_keywords = {
            'critical': TaskPriority.CRITICAL,
            'urgent': TaskPriority.HIGH,
            'high': TaskPriority.HIGH,
            'important': TaskPriority.HIGH,
            'medium': TaskPriority.MEDIUM,
            'normal': TaskPriority.MEDIUM,
            'low': TaskPriority.LOW,
            'optional': TaskPriority.LOW
        }
        
        self.language_keywords = {
            'python': 'python',
            'javascript': 'javascript',
            'js': 'javascript',
            'typescript': 'typescript',
            'ts': 'typescript',
            'java': 'java',
            'c++': 'cpp',
            'cpp': 'cpp',
            'go': 'go',
            'rust': 'rust',
            'php': 'php',
            'ruby': 'ruby'
        }
    
    def parse_user_input(self, user_input: str, plan_id: str = None) -> Tuple[Plan, List[Task]]:
        """Parse user input into a plan and list of tasks"""
        if not plan_id:
            plan_id = str(uuid.uuid4())
        
        # Extract plan-level information
        plan_info = self._extract_plan_info(user_input)
        
        # Create plan
        plan = Plan(
            id=plan_id,
            name=plan_info.get('name', 'Generated Plan'),
            description=plan_info.get('description', user_input[:200] + '...' if len(user_input) > 200 else user_input),
            user_input=user_input,
            language=plan_info.get('language', 'python'),
            frameworks=plan_info.get('frameworks', []),
            goals=plan_info.get('goals', []),
            requirements=plan_info.get('requirements', []),
            constraints=plan_info.get('constraints', [])
        )
        
        # Extract tasks
        tasks = self._extract_tasks(user_input, plan_id)
        
        # Update plan with task information
        plan.task_ids = [task.id for task in tasks]
        plan.total_tasks = len(tasks)
        
        return plan, tasks
    
    async def parse_plan(self, plan_text: str, plan_id: str = None) -> Tuple[Plan, List[Task]]:
        """Parse a plan text into a plan and tasks (alias for parse_user_input)"""
        # Add a small delay to make it truly async
        await asyncio.sleep(0.01)
        return self.parse_user_input(plan_text, plan_id)
    
    def _extract_plan_info(self, user_input: str) -> Dict[str, any]:
        """Extract plan-level information from user input"""
        info = {}
        
        # Extract project name
        name_patterns = [
            r'(?:create|build|develop)\s+(?:a\s+)?(?:project\s+)?(?:called\s+)?["\']?([^"\'.\n]+)["\']?',
            r'project\s+name[:\s]+["\']?([^"\'.\n]+)["\']?',
            r'building\s+["\']?([^"\'.\n]+)["\']?'
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, user_input, re.IGNORECASE)
            if match:
                info['name'] = match.group(1).strip()
                break
        
        # Extract language
        for keyword, language in self.language_keywords.items():
            # Escape the keyword to avoid regex errors
            escaped_keyword = re.escape(keyword)
            # For alphanumeric keywords (with underscores considered part of the word) we can use word boundaries
            if keyword.replace('_', '').isalnum():
                pattern = rf'\b{escaped_keyword}\b'
            else:
                pattern = rf'(?<!\w){escaped_keyword}(?!\w)'
            try:
                if re.search(pattern, user_input, re.IGNORECASE):
                    info['language'] = language
                    break
            except re.error:
                # If there's an error, skip this keyword
                continue
        
        # Extract frameworks
        framework_patterns = [
            r'using\s+([a-zA-Z]+(?:\s+[a-zA-Z]+)*)',
            r'with\s+([a-zA-Z]+(?:\s+[a-zA-Z]+)*)',
            r'framework[:\s]+([a-zA-Z]+(?:\s+[a-zA-Z]+)*)'
        ]
        
        frameworks = []
        for pattern in framework_patterns:
            matches = re.findall(pattern, user_input, re.IGNORECASE)
            frameworks.extend(matches)
        
        if frameworks:
            info['frameworks'] = [f.strip() for f in frameworks]
        
        # Extract goals
        goal_patterns = [
            r'(?:goal|objective|aim)[s]?[:\s]+([^.\n]+)',
            r'(?:should|must|need to)\s+([^.\n]+)',
            r'requirements?[:\s]+([^.\n]+)'
        ]
        
        goals = []
        for pattern in goal_patterns:
            matches = re.findall(pattern, user_input, re.IGNORECASE)
            goals.extend([g.strip() for g in matches])
        
        if goals:
            info['goals'] = goals
        
        return info
    
    def _extract_tasks(self, user_input: str, plan_id: str) -> List[Task]:
        """Extract tasks from user input"""
        tasks = []
        
        # Split input into sentences and paragraphs
        sentences = self._split_into_sentences(user_input)
        
        # Look for explicit task lists
        task_list_patterns = [
            r'(?:tasks?|steps?|todo)[:\s]*\n((?:[-*]\s+.+\n?)+)',
            r'(?:need to|should|must)[:\s]*\n((?:[-*]\s+.+\n?)+)',
            r'\d+\.\s+(.+?)(?=\n\d+\.|\n\n|$)'
        ]
        
        explicit_tasks = []
        for pattern in task_list_patterns:
            matches = re.findall(pattern, user_input, re.MULTILINE | re.DOTALL)
            for match in matches:
                if isinstance(match, str):
                    # Extract individual items
                    items = re.findall(r'[-*]\s+(.+)', match)
                    explicit_tasks.extend(items)
                else:
                    explicit_tasks.append(match)
        
        # If explicit tasks found, use them
        if explicit_tasks:
            for i, task_desc in enumerate(explicit_tasks):
                task = self._create_task_from_description(
                    task_desc.strip(), 
                    plan_id, 
                    i + 1
                )
                tasks.append(task)
        else:
            # Extract implicit tasks from sentences
            for i, sentence in enumerate(sentences):
                if self._is_task_sentence(sentence):
                    task = self._create_task_from_description(
                        sentence.strip(), 
                        plan_id, 
                        i + 1
                    )
                    tasks.append(task)
        
        # If no tasks found, create a default task
        if not tasks:
            task = self._create_task_from_description(
                user_input[:100] + '...' if len(user_input) > 100 else user_input,
                plan_id,
                1
            )
            tasks.append(task)
        
        return tasks
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences"""
        # Simple sentence splitting
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _is_task_sentence(self, sentence: str) -> bool:
        """Check if a sentence describes a task"""
        sentence_lower = sentence.lower()
        
        # Check for task keywords
        for keyword in self.task_keywords:
            if keyword in sentence_lower:
                return True
        
        # Check for imperative patterns
        imperative_patterns = [
            r'^(?:please\s+)?(?:can\s+you\s+)?(?:i\s+need\s+to\s+)?(?:we\s+should\s+)?(?:let\'s\s+)?(\w+)',
            r'(?:need to|should|must|have to)\s+\w+',
            r'(?:will|would)\s+(?:like to|need to)\s+\w+'
        ]
        
        for pattern in imperative_patterns:
            if re.search(pattern, sentence_lower):
                return True
        
        return False
    
    def _create_task_from_description(self, description: str, plan_id: str, order: int) -> Task:
        """Create a task from a description"""
        task_id = str(uuid.uuid4())
        
        # Extract priority
        priority = TaskPriority.MEDIUM
        for keyword, task_priority in self.priority_keywords.items():
            if keyword in description.lower():
                priority = task_priority
                break
        
        # Extract language if specified
        language = 'python'
        for keyword, lang in self.language_keywords.items():
            # Escape the keyword to avoid regex errors
            escaped_keyword = re.escape(keyword)
            # For alphanumeric keywords (with underscores considered part of the word) we can use word boundaries
            if keyword.replace('_', '').isalnum():
                pattern = rf'\b{escaped_keyword}\b'
            else:
                pattern = rf'(?<!\w){escaped_keyword}(?!\w)'
            try:
                if re.search(pattern, description, re.IGNORECASE):
                    language = lang
                    break
            except re.error:
                # If there's an error, skip this keyword
                continue
        
        # Generate title from description
        title = self._generate_task_title(description)
        
        # Extract requirements
        requirements = self._extract_requirements(description)
        
        return Task(
            id=task_id,
            title=title,
            description=description,
            project_id=plan_id,
            priority=priority,
            language=language,
            requirements=requirements,
            metadata={'order': order}
        )
    
    def _generate_task_title(self, description: str) -> str:
        """Generate a concise title from task description"""
        # Take first few words, up to 50 characters
        words = description.split()
        title = ""
        for word in words:
            if len(title + word) > 50:
                break
            title += word + " "
        
        return title.strip() or "Task"
    
    def _extract_requirements(self, description: str) -> List[str]:
        """Extract requirements from task description"""
        requirements = []
        
        # Look for requirement patterns
        req_patterns = [
            r'(?:must|should|need to|required to)\s+([^.]+)',
            r'(?:ensure|make sure)\s+([^.]+)',
            r'(?:with|using|include)\s+([^.]+)'
        ]
        
        for pattern in req_patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            requirements.extend([req.strip() for req in matches])
        
        return requirements
