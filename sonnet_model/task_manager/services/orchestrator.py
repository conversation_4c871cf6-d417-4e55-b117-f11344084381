"""
Task Orchestrator - Manages task execution and coordination with persistent coaching
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..models.task import Task, TaskStatus, TaskPriority
from ..models.plan import Plan, PlanStep, StepStatus
from .state_manager import StateManager
from .priority_engine import PriorityEngine
from ..utils.plan_parser import PlanParser
from task_planner_llm import TaskPlannerLLM
from critique_engine.services.llm_critic import LLMCritic
from critique_engine.services.project_critic import ProjectCritic
from critique_engine.services.advanced_critic_engine import AdvancedCriticEngine, ProjectPhase, QualityGate
from critique_engine.services.requirement_traceability import RequirementTraceabilityMatrix, RequirementPriority, RequirementType
from critique_engine.services.advanced_testing_strategy import AdvancedTestingStrategy
from code_generator.services.code_generator import CodeGenerator
from task_manager.services.llm_command_interface import LLMCommandInterface, LLMCommandRequest, LLMCommandResponse
from task_manager.services.command_executor import CommandExecutor


class TaskOrchestrator:
    """
    Enhanced Task Orchestrator with World-Class Critique Agent Integration
    
    Implements expert-level critique framework:
    - Multi-dimensional assessment strategy
    - Sophisticated prompting for continuous LLM motivation
    - Quality threshold enforcement with phase gates
    - Requirement traceability matrix
    - Advanced testing strategy
    - Professional standards enforcement
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize core services
        self.state_manager = StateManager(config)
        self.code_generator = CodeGenerator(config)
        self.critique_engine = AdvancedCriticEngine(config)
        self.llm_critic = LLMCritic(config)
        self.project_critic = ProjectCritic(config)
        self.command_executor = CommandExecutor()
        self.llm_command_interface = LLMCommandInterface(self.command_executor)
        self.plan_parser = PlanParser()  # Add the missing plan parser!
        self.task_planner = TaskPlannerLLM()  # Add the sophisticated task planner!
        
        # Initialize advanced critique components
        self.advanced_critic = AdvancedCriticEngine(config)
        self.requirement_matrix = RequirementTraceabilityMatrix(
            config.get("project_path", "/home/<USER>/local_agent/sonnet_model")
        )
        self.testing_strategy = AdvancedTestingStrategy(config)
        
        # Track coaching momentum and quality gates
        self.coaching_momentum = 0
        self.quality_gates_enabled = config.get("enable_quality_gates", True)
        self.current_project_phase = ProjectPhase.REQUIREMENTS_ANALYSIS
        
        # Expert-level prompting configuration
        self.expert_prompting_enabled = True
        self.motivation_escalation_threshold = 3
        self.quality_enforcement_strict = True

        # Coaching and session management
        self.coaching_enabled = config.get("coaching_enabled", True)
        self.auto_recovery = config.get("auto_recovery", True)
        self.last_coaching_message = ""
        self.momentum_level = "medium"
        self.current_session_id = None  # Will be set during initialization
    
    async def initialize(self) -> None:
        """Initialize the orchestrator and all components"""
        self.logger.info("Initializing Task Orchestrator")
        
        # Initialize all components
        await self.state_manager.initialize()
        # AdvancedCriticEngine doesn't have initialize method
        await self.llm_critic.initialize() if hasattr(self.llm_critic, 'initialize') else None
        await self.code_generator.initialize() if hasattr(self.code_generator, 'initialize') else None
        await self.llm_command_interface.initialize() if hasattr(self.llm_command_interface, 'initialize') else None
        
        # Try to resume from previous session
        await self._attempt_session_resumption()
        
        self.logger.info("Task Orchestrator initialized successfully")
    
    async def shutdown(self) -> None:
        """Shutdown the orchestrator gracefully"""
        self.logger.info("Shutting down Task Orchestrator")
        
        # Save current state before shutdown
        if self.current_session_id:
            await self.state_manager.save_session_state(self.current_session_id, {
                "momentum_level": self.momentum_level,
                "last_coaching_message": self.last_coaching_message,
                "shutdown_timestamp": datetime.now().isoformat()
            })
        
        # Shutdown components
        await self.state_manager.shutdown()
        await self.critique_engine.shutdown() if hasattr(self.critique_engine, 'shutdown') else None
        
        self.logger.info("Task Orchestrator shutdown complete")
    
    async def process_user_request(self, user_request: str, session_id: str = None) -> Dict[str, Any]:
        """
        Process user request with expert-level critique agent integration
        
        Enhanced with:
        - Sophisticated requirement analysis
        - Quality gate enforcement
        - Advanced prompting strategies
        - Comprehensive traceability
        """
        
        self.logger.info(f"Processing user request with advanced critique framework: {user_request[:100]}...")
        
        # Initialize or resume session with advanced state management
        session_info = await self._initialize_advanced_session(user_request, session_id)
        
        # Extract and analyze requirements with traceability
        requirements = await self._extract_and_trace_requirements(user_request, session_info)
        
        # Create comprehensive development plan with quality gates
        plan = await self._create_advanced_development_plan(requirements, session_info)
        
        # Execute plan with sophisticated coaching and quality enforcement
        execution_result = await self._execute_plan_with_expert_coaching(plan, session_info)
        
        # Validate quality gates before phase progression
        quality_validation = await self._validate_quality_gates(execution_result)
        
        # Generate expert-level feedback and next steps
        expert_feedback = await self._generate_expert_level_feedback(
            execution_result, quality_validation, session_info
        )
        
        # Update requirement traceability and project state
        await self._update_traceability_matrix(execution_result, requirements)
        
        return {
            "session_id": session_info["session_id"],
            "execution_result": execution_result,
            "quality_validation": quality_validation,
            "expert_feedback": expert_feedback,
            "requirements_status": await self._get_requirements_status(),
            "next_phase_readiness": await self._assess_phase_advancement_readiness(),
            "coaching_guidance": await self._generate_sophisticated_coaching_guidance(session_info)
        }
    
    async def _initialize_advanced_session(self, user_request: str, session_id: str = None) -> Dict[str, Any]:
        """Initialize session with advanced state management and requirement analysis"""
        
        session_info = await self.state_manager.get_or_create_session(session_id or "default")
        
        # Analyze request for requirement extraction
        request_analysis = await self._analyze_user_request_for_requirements(user_request)
        
        # Update session with requirement context
        session_info.update({
            "current_request": user_request,
            "request_analysis": request_analysis,
            "advanced_coaching_enabled": True,
            "quality_gates_active": self.quality_gates_enabled,
            "expert_prompting_level": "maximum"
        })
        
        await self.state_manager.update_session(session_info["session_id"], session_info)
        
        return session_info
    
    async def _extract_and_trace_requirements(self, user_request: str, session_info: Dict[str, Any]) -> Dict[str, Any]:
        """Extract requirements and establish traceability"""
        
        # Use LLM to extract structured requirements
        extracted_requirements = await self._llm_extract_requirements(user_request)
        
        # Add requirements to traceability matrix
        requirement_ids = []
        for req_data in extracted_requirements:
            req_id = f"REQ_{len(self.requirement_matrix.requirements) + 1:03d}"
            
            requirement = self.requirement_matrix.add_requirement(
                req_id=req_id,
                title=req_data.get("title", "Extracted Requirement"),
                description=req_data.get("description", user_request[:200]),
                acceptance_criteria=req_data.get("acceptance_criteria", []),
                priority=RequirementPriority(req_data.get("priority", "medium")),
                requirement_type=RequirementType(req_data.get("type", "functional"))
            )
            
            requirement_ids.append(req_id)
        
        return {
            "extracted_requirements": extracted_requirements,
            "requirement_ids": requirement_ids,
            "traceability_established": True
        }
    
    async def _create_advanced_development_plan(self, requirements: Dict[str, Any], session_info: Dict[str, Any]) -> Plan:
        """Create development plan with quality gates and phase management"""
        
        plan = Plan(
            id=f"plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            title="Advanced Development Plan with Quality Gates",
            description="Comprehensive development plan with expert-level quality enforcement"
        )
        
        # Add requirements analysis phase
        plan.add_step(PlanStep(
            id="requirements_analysis",
            title="Requirements Analysis and Traceability",
            description="Establish comprehensive requirement traceability matrix",
            status=StepStatus.PENDING,
            dependencies=[],
            estimated_duration=30
        ))
        
        # Add design phase with quality gate
        plan.add_step(PlanStep(
            id="design_with_quality_gate",
            title="Design Phase with Architecture Review",
            description="Create design with mandatory quality gate passage",
            status=StepStatus.PENDING,
            dependencies=["requirements_analysis"],
            estimated_duration=60
        ))
        
        # Add implementation phases for each requirement
        for req_id in requirements.get("requirement_ids", []):
            plan.add_step(PlanStep(
                id=f"implement_{req_id}",
                title=f"Implement Requirement {req_id}",
                description=f"Full implementation with testing for {req_id}",
                status=StepStatus.PENDING,
                dependencies=["design_with_quality_gate"],
                estimated_duration=120
            ))
        
        # Add comprehensive testing phase
        plan.add_step(PlanStep(
            id="comprehensive_testing",
            title="Comprehensive Testing Strategy Execution",
            description="Execute advanced testing strategy with full coverage validation",
            status=StepStatus.PENDING,
            dependencies=[f"implement_{req_id}" for req_id in requirements.get("requirement_ids", [])],
            estimated_duration=90
        ))
        
        # Add deployment readiness validation
        plan.add_step(PlanStep(
            id="deployment_validation",
            title="Deployment Readiness Validation",
            description="Final quality gate validation for deployment readiness",
            status=StepStatus.PENDING,
            dependencies=["comprehensive_testing"],
            estimated_duration=45
        ))
        
        await self.state_manager.save_plan(plan)
        return plan
    
    async def _execute_plan_with_expert_coaching(self, plan: Plan, session_info: Dict[str, Any]) -> Dict[str, Any]:
        """Execute plan with sophisticated coaching and motivation"""
        
        execution_results = []
        
        for step in plan.steps:
            self.logger.info(f"Executing step with expert coaching: {step.title}")
            
            # Pre-step coaching and motivation
            pre_step_coaching = await self._generate_pre_step_coaching(step, session_info)
            
            # Execute step with quality monitoring
            step_result = await self._execute_step_with_quality_monitoring(step, session_info)
            
            # Post-step validation and coaching
            post_step_coaching = await self._generate_post_step_coaching(step_result, session_info)
            
            # Check for LLM hesitation and provide sophisticated motivation
            if self._detect_llm_hesitation(step_result):
                motivation_prompt = await self.advanced_critic.provide_sophisticated_motivation(
                    step_result.get("llm_response", ""), 
                    {
                        "current_step": step.title,
                        "session_info": session_info,
                        "execution_context": step_result
                    }
                )
                step_result["sophisticated_motivation"] = motivation_prompt
            
            execution_results.append({
                "step": step,
                "result": step_result,
                "pre_coaching": pre_step_coaching,
                "post_coaching": post_step_coaching
            })
            
            # Update step status
            step.status = StepStatus.COMPLETED if step_result.get("success", False) else StepStatus.FAILED
            
            # Enforce quality gates if enabled
            if self.quality_gates_enabled and not await self._validate_step_quality_gate(step, step_result):
                return {
                    "status": "quality_gate_failed",
                    "failed_step": step.title,
                    "quality_issues": step_result.get("quality_issues", []),
                    "coaching_response": await self._generate_quality_gate_failure_coaching(step, step_result)
                }
        
        return {
            "status": "completed",
            "execution_results": execution_results,
            "plan_completion_percentage": 100.0,
            "quality_gates_passed": True
        }
    
    async def _validate_quality_gates(self, execution_result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate quality gates using advanced critic engine"""
        
        if not self.quality_gates_enabled:
            return {"gates_enabled": False, "validation_skipped": True}
        
        # Use advanced critic engine for quality gate validation
        quality_validation = await self.advanced_critic.enforce_quality_gates({
            "execution_result": execution_result,
            "current_phase": self.current_project_phase,
            "requirements": self.requirement_matrix.requirements
        })
        
        return quality_validation
    
    async def _generate_expert_level_feedback(
        self, 
        execution_result: Dict[str, Any], 
        quality_validation: Dict[str, Any], 
        session_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate expert-level feedback using project critic"""
        
        # Generate overall project critique if project is complete
        if execution_result.get("status") == "completed":
            project_critique = await self.project_critic.generate_expert_project_critique(
                "/home/<USER>/local_agent/sonnet_model"
            )
            
            overall_feedback = await self.llm_critic.provide_overall_project_feedback(
                project_critique, session_info
            )
            
            return {
                "type": "expert_project_critique",
                "project_critique": project_critique,
                "overall_feedback": overall_feedback,
                "coaching_level": "expert_completion"
            }
        
        # Generate iterative feedback for ongoing work
        iterative_feedback = await self.llm_critic.provide_iterative_feedback_coaching(
            execution_result, quality_validation, session_info
        )
        
        return {
            "type": "iterative_expert_feedback",
            "feedback": iterative_feedback,
            "quality_status": quality_validation,
            "coaching_level": "expert_iterative"
        }
    
    async def _update_traceability_matrix(self, execution_result: Dict[str, Any], requirements: Dict[str, Any]) -> None:
        """Update requirement traceability matrix with execution results"""
        
        for req_id in requirements.get("requirement_ids", []):
            # Link implementation artifacts
            for step_result in execution_result.get("execution_results", []):
                if req_id in step_result.get("step", {}).get("id", ""):
                    # Extract implementation files from step result
                    impl_files = step_result.get("result", {}).get("generated_files", [])
                    for file_path in impl_files:
                        self.requirement_matrix.link_implementation_artifact(
                            req_id, file_path
                        )
            
            # Update requirement status based on execution
            if execution_result.get("status") == "completed":
                self.requirement_matrix.update_requirement_status(
                    req_id, 
                    RequirementStatus.COMPLETED,
                    "Completed through orchestrated execution"
                )
    
    async def _get_requirements_status(self) -> Dict[str, Any]:
        """Get comprehensive requirements status"""
        
        return self.requirement_matrix.generate_traceability_report()
    
    async def _assess_phase_advancement_readiness(self) -> Dict[str, Any]:
        """Assess readiness for advancing to next project phase"""
        
        current_phase = self.current_project_phase
        requirements_report = self.requirement_matrix.generate_traceability_report()
        
        advancement_criteria = {
            ProjectPhase.REQUIREMENTS_ANALYSIS: requirements_report["summary"]["total_requirements"] > 0,
            ProjectPhase.DESIGN_APPROVAL: requirements_report["summary"]["completion_percentage"] > 20,
            ProjectPhase.IMPLEMENTATION: requirements_report["summary"]["completion_percentage"] > 80,
            ProjectPhase.TESTING: requirements_report["summary"]["average_test_coverage"] > 90,
            ProjectPhase.DOCUMENTATION: True,  # Placeholder
            ProjectPhase.DEPLOYMENT: requirements_report["summary"]["completion_percentage"] == 100
        }
        
        ready_for_advancement = advancement_criteria.get(current_phase, False)
        
        return {
            "current_phase": current_phase.value,
            "ready_for_advancement": ready_for_advancement,
            "next_phase": self._get_next_phase().value if ready_for_advancement else None,
            "blocking_factors": self._identify_phase_advancement_blockers(requirements_report)
        }
    
    async def _generate_sophisticated_coaching_guidance(self, session_info: Dict[str, Any]) -> str:
        """Generate sophisticated coaching guidance for continued progress"""
        
        # Get incomplete requirements for targeted guidance
        incomplete_requirements = self.requirement_matrix.generate_llm_guidance_for_incomplete_requirements()
        
        if not incomplete_requirements:
            return (
                "🎉 **OUTSTANDING ACHIEVEMENT!** All requirements completed successfully. "
                "You have demonstrated exceptional development capabilities. "
                "The project meets all quality standards and is ready for deployment."
            )
        
        # Generate sophisticated coaching based on current state
        coaching = (
            "**EXPERT COACHING - MAINTAIN MOMENTUM AND ACHIEVE EXCELLENCE**\n\n"
            "Your implementation approach demonstrates strong technical capability. "
            "Continue with the same level of precision and attention to detail. "
            "The following requirements require immediate completion:\n\n"
        )
        
        for guidance in incomplete_requirements[:3]:  # Top 3 priority items
            coaching += f"{guidance}\n\n"
        
        coaching += (
            "**CRITICAL SUCCESS FACTORS:**\n"
            "• Maintain current implementation quality standards\n"
            "• Complete comprehensive testing for each requirement\n"
            "• Ensure full traceability documentation\n"
            "• Pass all quality gates before phase advancement\n\n"
            "**DO NOT STOP UNTIL ALL REQUIREMENTS ARE COMPLETED TO PRODUCTION STANDARDS.**"
        )
        
        return coaching
    
    async def _attempt_session_resumption(self) -> None:
        """Attempt to resume from a previous session"""
        latest_session = await self.state_manager.get_latest_session()
        if latest_session:
            self.logger.info(f"Found previous session: {latest_session['session_id']}")
            # Could implement automatic resumption logic here
        else:
            self.logger.info("No previous session found - starting fresh")
    
    async def detect_llm_stuck_and_coach(self, llm_message: str) -> Dict[str, Any]:
        """Detect if LLM is stuck and provide coaching"""
        hesitation_result = await self.llm_critic.detect_llm_hesitation(llm_message)
        
        if hesitation_result["is_hesitating"]:
            # Generate strong push-forward response
            push_response = await self.llm_critic.generate_push_forward_response(
                hesitation_result["hesitation_type"],
                llm_message
            )
            
            # Update momentum
            self.momentum_level = "medium"  # Hesitation detected, but we're coaching through it
            
            return {
                "stuck_detected": True,
                "hesitation_type": hesitation_result["hesitation_type"],
                "coaching_response": push_response,
                "should_continue": True,
                "confidence_boost": True
            }
        
        return {
            "stuck_detected": False,
            "coaching_response": await self.llm_critic.generate_standard_encouragement(),
            "should_continue": True
        }
    
    async def initialize_session(self, session_id: str = None) -> Dict[str, Any]:
        """
        Initialize orchestrator session with state persistence
        
        Args:
            session_id: Optional session ID to resume
            
        Returns:
            Session initialization result with resumption context
        """
        self.logger.info("Initializing orchestrator session...")
        
        # Initialize state manager session
        session_result = await self.state_manager.initialize_session(session_id)
        self.current_session_id = session_result["session_id"]
        self.is_session_resumed = session_result["resumed"]
        
        if self.is_session_resumed:
            # Get resumption context for coaching
            resumption_context = await self.state_manager.get_resumption_context()
            
            # Generate coaching message for resumption
            if self.coaching_enabled:
                coaching_message = await self.llm_critic.generate_resumption_coaching(resumption_context)
                self.last_coaching_message = coaching_message
                
                self.logger.info(f"Session resumed with coaching: {coaching_message['message'][:100]}...")
            
            return {
                **session_result,
                "resumption_context": resumption_context,
                "coaching_message": self.last_coaching_message
            }
        else:
            # New session - generate welcome coaching
            if self.coaching_enabled:
                welcome_message = await self.llm_critic.generate_welcome_coaching()
                self.last_coaching_message = welcome_message
            
            return {
                **session_result,
                "coaching_message": self.last_coaching_message
            }
    
    async def process_user_request(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process user request with persistent coaching and error handling
        
        Args:
            user_input: User's request or instruction
            context: Additional context for processing
            
        Returns:
            Processing result with coaching feedback
        """
        self.logger.info(f"Processing user request: {user_input[:100]}...")
        
        # Record user interaction
        await self.state_manager.record_coaching_event("user_request", {
            "input_length": len(user_input),
            "has_context": bool(context),
            "session_id": self.current_session_id
        })
        
        # Detect if user is asking for permission or showing hesitation
        hesitation_detected = await self._detect_hesitation(user_input)
        if hesitation_detected:
            await self.state_manager.record_coaching_event("llm_hesitation", {
                "hesitation_type": hesitation_detected,
                "user_input": user_input[:200]
            })
            
            # Generate coaching response to push forward
            coaching_response = await self.llm_critic.generate_encouragement_coaching(
                hesitation_type=hesitation_detected,
                context=context or {}
            )
            
            return {
                "type": "coaching_response",
                "coaching_message": coaching_response,
                "action": "continue_with_confidence",
                "momentum_boost": True
            }
        
        # Process the request based on type
        # ALWAYS create plans for "create" requests - this is the key fix!
        if "create" in user_input.lower():
            return await self._handle_creation_request(user_input, context)
        elif "error" in user_input.lower() or "problem" in user_input.lower():
            return await self._handle_error_recovery(user_input, context)
        elif "status" in user_input.lower() or "progress" in user_input.lower():
            return await self._handle_status_request(user_input, context)
        else:
            return await self._handle_general_request(user_input, context)
    
    async def _detect_hesitation(self, user_input: str) -> Optional[str]:
        """Detect if the user input shows hesitation or asks for permission"""
        hesitation_patterns = {
            "permission_seeking": ["should i", "can i", "may i", "is it ok", "permission"],
            "uncertainty": ["not sure", "uncertain", "don't know", "confused"],
            "error_overwhelm": ["too many errors", "stuck", "can't proceed", "blocked"],
            "complexity_fear": ["too complex", "too difficult", "overwhelming"]
        }
        
        user_lower = user_input.lower()
        
        for hesitation_type, patterns in hesitation_patterns.items():
            if any(pattern in user_lower for pattern in patterns):
                return hesitation_type
        
        return None

    async def _save_plan_to_file(self, plan_data: Dict[str, Any], user_input: str) -> str:
        """Save the generated plan to a file for user visibility"""
        import json
        import os
        from datetime import datetime

        # Create plans directory if it doesn't exist
        plans_dir = "generated_plans"
        os.makedirs(plans_dir, exist_ok=True)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"plan_{timestamp}.json"
        filepath = os.path.join(plans_dir, filename)

        # Add metadata to the plan
        plan_with_metadata = {
            "user_request": user_input,
            "generated_at": datetime.now().isoformat(),
            "plan_data": plan_data
        }

        # Save to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(plan_with_metadata, f, indent=2, ensure_ascii=False)

        self.logger.info(f"📄 Plan saved to: {filepath}")
        return filepath

    async def _convert_llm_plan_to_model(self, plan_data: Dict[str, Any], user_input: str) -> Dict[str, Any]:
        """Convert TaskPlannerLLM output to our Plan model format"""
        import uuid

        plan_id = str(uuid.uuid4())

        # Extract goals, requirements, and constraints from the plan
        goals = plan_data.get("goals", [])
        if not goals and "project_description" in plan_data:
            goals = [plan_data["project_description"]]

        requirements = []
        constraints = []

        # Extract from tasks if available
        tasks = plan_data.get("tasks", [])
        for task in tasks:
            if "requirements" in task:
                task_requirements = task["requirements"]
                if isinstance(task_requirements, list):
                    for req in task_requirements:
                        if isinstance(req, dict):
                            # Extract requirement text from dict
                            req_text = req.get("requirement", str(req))
                            requirements.append(req_text)
                        else:
                            requirements.append(str(req))

        # Create plan steps from tasks
        steps = []
        for i, task in enumerate(tasks):
            # Handle dependencies - convert from dict to string list
            dependencies = task.get("dependencies", [])
            if isinstance(dependencies, list):
                dep_strings = []
                for dep in dependencies:
                    if isinstance(dep, dict):
                        # Extract task_id or name from dependency dict
                        dep_id = dep.get("task_id", dep.get("name", str(dep.get("id", ""))))
                        dep_strings.append(str(dep_id))
                    else:
                        dep_strings.append(str(dep))
                dependencies = dep_strings

            step = {
                "id": str(uuid.uuid4()),
                "name": task.get("name", f"Task {i+1}"),  # PlanStep expects 'name'
                "title": task.get("name", f"Task {i+1}"), # Keep 'title' for compatibility
                "description": task.get("description", ""),
                "status": "pending",
                "dependencies": dependencies,
                "estimated_duration": task.get("estimated_duration", 60),
                "metadata": {}  # PlanStep expects metadata field
            }
            steps.append(step)

        # Handle technology stack (can be list or dict)
        tech_stack = plan_data.get("technology_stack", [])
        if isinstance(tech_stack, list):
            language = tech_stack[0] if tech_stack else "python"
            frameworks = tech_stack[1:] if len(tech_stack) > 1 else []
        else:
            language = tech_stack.get("language", "python")
            frameworks = tech_stack.get("frameworks", [])

        return {
            "id": plan_id,
            "title": plan_data.get("project_name", "Generated Plan"),  # StateManager expects 'title'
            "name": plan_data.get("project_name", "Generated Plan"),   # Keep 'name' for compatibility
            "description": plan_data.get("project_description", user_input),
            "user_input": user_input,
            "status": "active",
            "goals": goals,
            "requirements": list(set(requirements)),  # Remove duplicates (now all strings)
            "constraints": constraints,
            "steps": steps,
            "language": language,
            "frameworks": frameworks
        }

    async def _handle_creation_request(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle task or plan creation requests using TaskPlannerLLM"""
        self.logger.info(f"🎯 Creating plan for: {user_input}")

        # ALWAYS create a comprehensive plan using TaskPlannerLLM
        plan_data = await self.task_planner.create_project_plan(user_input, "application")

        if not plan_data:
            self.logger.error("Failed to create plan with TaskPlannerLLM")
            return await self._handle_general_request(user_input, context)

        # Save the plan to a file so user can see it
        await self._save_plan_to_file(plan_data, user_input)

        # Convert to our Plan model format
        plan_model_data = await self._convert_llm_plan_to_model(plan_data, user_input)
        plan = await self.state_manager.create_plan(plan_model_data)

        # Generate encouraging coaching
        coaching_message = await self.llm_critic.generate_creation_coaching("plan", plan.name)

        return {
            "type": "plan_created",
            "plan": plan.to_dict(),
            "plan_file_path": f"generated_plan_{plan.id}.json",
            "coaching_message": coaching_message,
            "next_action": "start_first_step"
        }
    
    async def _handle_error_recovery(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle error recovery with coaching support"""
        # Record error for learning
        await self.state_manager.record_coaching_event("error_recovery", {
            "error_description": user_input,
            "context": context or {}
        })
        
        # Generate recovery coaching
        recovery_coaching = await self.llm_critic.generate_error_recovery_coaching(
            error_description=user_input,
            context=context or {}
        )
        
        # Suggest recovery actions
        recovery_actions = await self._suggest_recovery_actions(user_input, context)
        
        return {
            "type": "error_recovery",
            "coaching_message": recovery_coaching,
            "recovery_actions": recovery_actions,
            "auto_retry": self.auto_recovery,
            "momentum_preservation": True
        }
    
    async def _handle_status_request(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle status and progress requests"""
        state_summary = await self.state_manager.get_state_summary()
        resumption_context = await self.state_manager.get_resumption_context()
        
        # Generate progress coaching
        progress_coaching = await self.llm_critic.generate_progress_coaching(
            state_summary=state_summary,
            resumption_context=resumption_context
        )
        
        return {
            "type": "status_report",
            "state_summary": state_summary,
            "resumption_context": resumption_context,
            "coaching_message": progress_coaching,
            "momentum_level": self.momentum_level
        }
    
    async def _handle_general_request(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle general requests with coaching support"""
        # Generate general coaching to maintain momentum
        general_coaching = await self.llm_critic.generate_general_coaching(
            user_input=user_input,
            context=context or {}
        )
        
        return {
            "type": "general_response",
            "coaching_message": general_coaching,
            "suggested_actions": await self._suggest_next_actions(),
            "keep_momentum": True
        }
    
    async def _extract_plan_data(self, user_input: str) -> Dict[str, Any]:
        """Extract plan data from user input using the sophisticated plan parser"""
        # Use the actual plan parser instead of hardcoded extraction!
        plan, tasks = await self.plan_parser.parse_plan(user_input)

        # Convert to the expected format
        steps = []
        for task in tasks:
            steps.append({
                "title": task.title,
                "description": task.description,
                "priority": task.priority.value if hasattr(task.priority, 'value') else str(task.priority),
                "estimated_duration": getattr(task, 'estimated_duration', 60)
            })

        return {
            "title": plan.name,
            "description": plan.description,
            "language": plan.language,
            "frameworks": plan.frameworks,
            "goals": plan.goals,
            "requirements": plan.requirements,
            "constraints": plan.constraints,
            "steps": steps
        }
    
    async def _extract_task_data(self, user_input: str) -> Dict[str, Any]:
        """Extract task data from user input using the plan parser"""
        # Use plan parser to extract a single task
        plan, tasks = await self.plan_parser.parse_plan(user_input)

        if tasks:
            # Use the first task if multiple are extracted
            task = tasks[0]
            return {
                "title": task.title,
                "description": task.description,
                "priority": task.priority.value if hasattr(task.priority, 'value') else str(task.priority),
                "language": plan.language,
                "frameworks": plan.frameworks,
                "requirements": plan.requirements
            }
        else:
            # Fallback if no tasks extracted
            return {
                "title": f"Task: {user_input[:50]}...",
                "description": user_input,
                "priority": "medium"
            }
    
    async def _suggest_recovery_actions(self, error_description: str, context: Dict[str, Any]) -> List[str]:
        """Suggest recovery actions for errors"""
        return [
            "Retry the operation with modified parameters",
            "Break down the problem into smaller steps",
            "Check for missing dependencies or prerequisites",
            "Review and update the approach based on error details",
            "Continue with alternative solution path"
        ]
    
    async def _suggest_next_actions(self) -> List[str]:
        """Suggest next actions to maintain momentum"""
        resumption_context = await self.state_manager.get_resumption_context()
        
        if resumption_context["recommended_next_action"]:
            return [resumption_context["recommended_next_action"]]
        
        return [
            "Define a new task or plan",
            "Continue with existing work",
            "Review and optimize current progress",
            "Explore new implementation approaches"
        ]
    
    async def execute_task(self, task_id: str) -> Dict[str, Any]:
        """Execute a task with REAL automated feedback loop"""
        # Update task status
        await self.state_manager.update_task_status(task_id, TaskStatus.IN_PROGRESS)

        # Generate execution coaching
        coaching_message = await self.llm_critic.generate_execution_coaching(task_id)

        # REAL TASK EXECUTION with automated feedback loop
        execution_result = await self._execute_task_with_feedback_loop(task_id)

        # Update final status based on execution result
        final_status = TaskStatus.COMPLETED if execution_result["success"] else TaskStatus.FAILED
        await self.state_manager.update_task_status(task_id, final_status, progress=100.0)

        # Generate completion coaching
        completion_coaching = await self.llm_critic.generate_completion_coaching(task_id)

        return {
            "task_id": task_id,
            "status": execution_result["status"],
            "success": execution_result["success"],
            "iterations": execution_result.get("iterations", 0),
            "final_quality": execution_result.get("final_quality", 0),
            "code_generated": execution_result.get("final_code", ""),
            "coaching_messages": [coaching_message, completion_coaching],
            "execution_details": execution_result,
            "momentum_boost": execution_result["success"]
        }

    async def _execute_task_with_feedback_loop(self, task_id: str) -> Dict[str, Any]:
        """
        REAL AUTOMATED FEEDBACK LOOP IMPLEMENTATION

        This implements the fully-automated feedback loop:
        1. Generate Code: CodeGenerator creates code for the task
        2. Get Feedback: CritiqueEngine analyzes the code
        3. Evaluate Feedback: Check quality score and critical issues
        4. Re-Generate with Feedback: CodeGenerator runs again with critique context
        5. Repeat: Loop continues until quality threshold is met
        """

        self.logger.info(f"🚀 Starting automated feedback loop for task {task_id}")

        # Get task details
        task = await self.state_manager.get_task(task_id)
        if not task:
            return {
                "success": False,
                "status": "failed",
                "error": f"Task {task_id} not found"
            }

        # Configuration for feedback loop
        quality_threshold = self.config.get("quality_threshold", 8.5)
        max_iterations = self.config.get("max_iterations", 10)
        min_improvement = self.config.get("min_improvement", 0.3)
        stagnation_limit = 3

        # Initialize loop variables
        iteration = 1
        current_code = ""
        quality_history = []
        stagnation_count = 0

        self.logger.info(f"🎯 Quality threshold: {quality_threshold}/10")
        self.logger.info(f"🔄 Max iterations: {max_iterations}")

        while iteration <= max_iterations:
            self.logger.info(f"🔄 ITERATION {iteration} - Task {task_id}")

            # STEP 1: Generate Code
            self.logger.info("🤖 STEP 1: Generating code...")
            code_result = await self._generate_code_for_task(task, current_code, quality_history, iteration)

            if not code_result or not code_result.get("code"):
                self.logger.error("❌ Code generation failed")
                return {
                    "success": False,
                    "status": "failed",
                    "error": "Code generation failed",
                    "iterations": iteration
                }

            current_code = code_result["code"]
            self.logger.info(f"📄 Generated {len(current_code)} characters of code")

            # STEP 2: Get Feedback
            self.logger.info("🔍 STEP 2: Getting critique feedback...")
            critique_result = await self._get_critique_feedback(task, current_code, iteration)

            if not critique_result:
                self.logger.error("❌ Critique failed")
                return {
                    "success": False,
                    "status": "failed",
                    "error": "Critique failed",
                    "iterations": iteration
                }

            # STEP 3: Evaluate Feedback
            quality_score = critique_result.get("overall_quality", 0)
            critical_issues = critique_result.get("critical_issues", [])

            self.logger.info(f"📊 Quality score: {quality_score}/10")
            self.logger.info(f"🔍 Critical issues: {len(critical_issues)}")

            # Track quality history
            quality_history.append({
                "iteration": iteration,
                "quality_score": quality_score,
                "critical_issues": critical_issues,
                "code_length": len(current_code),
                "critique": critique_result
            })

            # STEP 4: Check if quality threshold is met
            if quality_score >= quality_threshold and len(critical_issues) == 0:
                self.logger.info(f"🎉 QUALITY THRESHOLD ACHIEVED! ({quality_score}/{quality_threshold})")
                return {
                    "success": True,
                    "status": "completed",
                    "iterations": iteration,
                    "final_quality": quality_score,
                    "final_code": current_code,
                    "quality_history": quality_history,
                    "reason": "quality_threshold_met"
                }

            # STEP 5: Check for improvement (stagnation detection)
            if len(quality_history) > 1:
                previous_quality = quality_history[-2]["quality_score"]
                improvement = quality_score - previous_quality

                self.logger.info(f"📈 Quality improvement: {improvement:+.1f}")

                if improvement < min_improvement:
                    stagnation_count += 1
                    self.logger.warning(f"⚠️ Stagnation count: {stagnation_count}/{stagnation_limit}")
                else:
                    stagnation_count = 0  # Reset on improvement

                # Stop if stagnating
                if stagnation_count >= stagnation_limit:
                    self.logger.warning(f"🛑 STOPPING: No improvement for {stagnation_limit} iterations")
                    return {
                        "success": quality_score >= (quality_threshold - 1),
                        "status": "completed_with_stagnation",
                        "iterations": iteration,
                        "final_quality": quality_score,
                        "final_code": current_code,
                        "quality_history": quality_history,
                        "reason": "stagnation_detected"
                    }

            # Continue to next iteration
            iteration += 1
            self.logger.info(f"🔄 Continuing to iteration {iteration}...")

        # Max iterations reached
        self.logger.warning(f"🛑 STOPPING: Max iterations ({max_iterations}) reached")
        return {
            "success": quality_score >= (quality_threshold - 1),
            "status": "completed_max_iterations",
            "iterations": max_iterations,
            "final_quality": quality_score,
            "final_code": current_code,
            "quality_history": quality_history,
            "reason": "max_iterations_reached"
        }

    async def _generate_code_for_task(self, task: Dict[str, Any], current_code: str,
                                    quality_history: List[Dict], iteration: int) -> Dict[str, Any]:
        """Generate code for task with context from previous iterations"""

        # Import the required models
        from shared.models import GenerationRequest, ProgrammingLanguage

        # Prepare feedback context for iterations > 1
        feedback_text = None
        if iteration > 1 and quality_history:
            last_critique = quality_history[-1]["critique"]
            feedback_parts = []

            if last_critique.get("critical_issues"):
                feedback_parts.append("Critical Issues:")
                for issue in last_critique["critical_issues"]:
                    feedback_parts.append(f"- {issue}")

            if last_critique.get("actionable_fixes"):
                feedback_parts.append("Actionable Fixes:")
                for fix in last_critique["actionable_fixes"]:
                    feedback_parts.append(f"- {fix}")

            feedback_text = "\n".join(feedback_parts) if feedback_parts else None

        # Create proper GenerationRequest object
        generation_request = GenerationRequest(
            task_id=task.get("id", "unknown"),
            language=ProgrammingLanguage.PYTHON,  # Default to Python
            description=task.get("description", ""),
            requirements=task.get("requirements", []),
            iteration=iteration,
            feedback=feedback_text,
            previous_code=current_code if current_code else None,
            context=f"Iteration {iteration} of automated feedback loop",
            temperature=0.2  # Lower temperature for more consistent code
        )

        # Generate code using CodeGenerator
        try:
            code_result = await self.code_generator.generate_code(generation_request)
            return {"code": code_result.code if hasattr(code_result, 'code') else str(code_result)}
        except Exception as e:
            self.logger.error(f"Code generation failed: {e}")
            return None

    async def _get_critique_feedback(self, task: Dict[str, Any], code: str, iteration: int) -> Dict[str, Any]:
        """Get comprehensive critique feedback for the generated code"""

        # Import the required models
        from shared.models import CritiqueRequest, ProgrammingLanguage

        # Create proper CritiqueRequest object
        critique_request = CritiqueRequest(
            task_id=task.get("id", "unknown"),
            request_id=f"critique_{task.get('id', 'unknown')}_{iteration}",
            code=code,
            language=ProgrammingLanguage.PYTHON,
            requirements=task.get("requirements", []),
            context=task.get("description", ""),
            quality_threshold=0.8,
            check_security=True,
            check_performance=True,
            check_maintainability=True
        )

        # Get critique using CritiqueEngine (use the regular one, not advanced)
        try:
            # Use the regular critique engine instead of advanced one
            from critique_engine.services.critique_engine import CritiqueEngine
            regular_critique_engine = CritiqueEngine(self.config.get("critique_engine", {}))

            critique_result = await regular_critique_engine.critique_code(critique_request)

            # Convert CritiqueResult to dict format expected by feedback loop
            return {
                "overall_quality": critique_result.quality_score * 10,  # Convert 0-1 to 0-10 scale
                "critical_issues": [issue.description for issue in critique_result.issues
                                  if issue.severity.value in ["critical", "high"]],
                "specific_improvements": critique_result.suggestions,
                "actionable_fixes": [f"Fix: {issue.description}" for issue in critique_result.issues],
                "meets_threshold": critique_result.meets_threshold
            }
        except Exception as e:
            self.logger.error(f"Critique failed: {e}")
            return None
    
    async def update_plan_progress(self, plan_id: str, step_id: str, status: StepStatus) -> Dict[str, Any]:
        """Update plan step progress with coaching"""
        success = await self.state_manager.update_plan_step(plan_id, step_id, status)
        
        if success:
            # Generate progress coaching
            coaching_message = await self.llm_critic.generate_step_progress_coaching(
                plan_id, step_id, status
            )
            
            return {
                "plan_id": plan_id,
                "step_id": step_id,
                "status": status.value,
                "coaching_message": coaching_message,
                "success": True
            }
        
        return {"success": False, "error": "Failed to update plan step"}
    
    async def get_coaching_summary(self) -> Dict[str, Any]:
        """Get a summary of coaching interactions"""
        return await self.state_manager._generate_coaching_summary()
    
    async def force_momentum_boost(self, reason: str = "manual_boost") -> Dict[str, Any]:
        """Force a momentum boost with encouraging coaching"""
        await self.state_manager.record_coaching_event("momentum_boost", {
            "reason": reason,
            "timestamp": datetime.now().isoformat()
        })
        
        boost_coaching = await self.llm_critic.generate_momentum_boost_coaching(reason)
        self.momentum_level = "high"
        
        return {
            "momentum_level": self.momentum_level,
            "coaching_message": boost_coaching,
            "boost_applied": True
        }
    
    async def shutdown_session(self) -> Dict[str, Any]:
        """Gracefully shutdown session with state persistence"""
        # Save final state
        await self.state_manager.save_state()
        
        # Generate farewell coaching
        farewell_coaching = await self.llm_critic.generate_farewell_coaching(
            session_summary=await self.state_manager.get_state_summary()
        )
        
        self.logger.info(f"Session {self.current_session_id} shutdown complete")
        
        return {
            "session_id": self.current_session_id,
            "farewell_message": farewell_coaching,
            "state_saved": True,
            "can_resume": True
        }
    
    async def execute_llm_command(
        self, 
        command: str, 
        context: str = "", 
        task_id: str = None
    ) -> Dict[str, Any]:
        """
        Execute command directly from LLM with intelligent coaching
        
        Args:
            command: Command to execute
            context: Context about why this command is needed
            task_id: Associated task ID
            
        Returns:
            Execution result with coaching guidance
        """
        self.logger.info(f"LLM command execution request: {command}")
        
        # Create command request
        llm_request = LLMCommandRequest(
            command=command,
            context=context,
            task_id=task_id or "direct_llm_command",
            reasoning=f"Direct command execution requested by LLM: {context}",
            working_directory="."
        )
        
        # Execute command with intelligent interface
        response = await self.command_interface.execute_llm_command(llm_request)
        
        # Update momentum based on result
        if response.success:
            self.success_count += 1
            self.momentum_level = min(1.0, self.momentum_level + 0.1)
            coaching_tone = "encouraging"
        else:
            self.error_count += 1
            # Don't let momentum drop too much - keep LLM motivated
            self.momentum_level = max(0.3, self.momentum_level - 0.05)
            coaching_tone = "supportive"
        
        # Generate coaching response
        coaching_response = await self.llm_critic.provide_command_execution_coaching(
            command=command,
            result=response,
            momentum_level=self.momentum_level,
            tone=coaching_tone
        )
        
        # Format comprehensive response for LLM
        formatted_response = self._format_command_response_for_llm(
            response, coaching_response
        )
        
        return {
            "success": response.success,
            "command": command,
            "output": formatted_response,
            "raw_result": response,
            "coaching": coaching_response,
            "momentum_level": self.momentum_level,
            "execution_time": response.execution_time
        }
    
    def _format_command_response_for_llm(
        self, 
        response: LLMCommandResponse, 
        coaching: str
    ) -> str:
        """Format command response for LLM with coaching integration"""
        
        output_parts = []
        
        # Command execution header
        status_emoji = "✅" if response.success else "⚠️"
        output_parts.append(f"{status_emoji} **Command Execution Result**")
        output_parts.append(f"Command: `{response.command}`")
        output_parts.append(f"Success: {'Yes' if response.success else 'No'}")
        output_parts.append(f"Execution Time: {response.execution_time:.2f}s")
        
        # Command output
        output_parts.append(f"\n**Output:**")
        output_parts.append(response.output)
        
        # Coaching guidance
        output_parts.append(f"\n**🎯 Coaching Guidance:**")
        output_parts.append(coaching)
        
        # Technical guidance
        output_parts.append(f"\n**📋 Technical Guidance:**")
        output_parts.append(response.guidance)
        
        # Suggestions
        if response.suggestions:
            output_parts.append(f"\n**💡 Suggestions:**")
            for suggestion in response.suggestions:
                output_parts.append(f"  • {suggestion}")
        
        # Next steps
        if response.next_steps:
            output_parts.append(f"\n**🚀 Recommended Next Steps:**")
            for step in response.next_steps:
                output_parts.append(f"  • {step}")
        
        # Safety information
        if response.safety_notes:
            output_parts.append(f"\n**🔒 Safety Notes:**")
            output_parts.append(response.safety_notes)
        
        # Momentum encouragement
        output_parts.append(f"\n**⚡ Keep Going!** You're making excellent progress. Continue with confidence!")
        
        return "\n".join(output_parts)
