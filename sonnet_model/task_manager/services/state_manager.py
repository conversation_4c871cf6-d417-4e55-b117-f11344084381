"""
State Manager Service

Manages task state and conversation persistence across sessions
"""

import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from pathlib import Path

from ..models.task import Task, TaskStatus, TaskPriority
from ..models.plan import Plan, PlanStep, StepStatus


class StateManager:
    """
    Manages task state and provides conversation persistence
    
    This service ensures that the system can resume from where it left off
    even across different conversation sessions
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # State persistence configuration
        self.persistence_enabled = config.get("persistence_enabled", True)
        self.state_file_path = Path(config.get("state_file_path", "data/conversation_state.json"))
        self.backup_interval = config.get("backup_interval_minutes", 5)
        self.max_state_history = config.get("max_state_history", 10)
        
        # In-memory state
        self.current_tasks: Dict[str, Task] = {}
        self.current_plans: Dict[str, Plan] = {}
        self.conversation_context = {
            "session_id": None,
            "start_time": None,
            "last_activity": None,
            "total_tasks_completed": 0,
            "current_focus": None,
            "llm_momentum": "high",  # high, medium, low
            "last_encouragement": None
        }
        
        # Coaching state
        self.coaching_history = []
        self.llm_interaction_patterns = {
            "hesitation_count": 0,
            "error_recovery_count": 0,
            "successful_completions": 0,
            "last_stuck_point": None
        }
        
        # Ensure state directory exists
        self.state_file_path.parent.mkdir(parents=True, exist_ok=True)

        # Note: initialize() will be called explicitly when needed
    
    async def initialize(self) -> None:
        """Initialize the state manager and load persisted state"""
        self.logger.info("Initializing State Manager")
        
        # Ensure state directory exists
        self.state_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Load persisted state if available
        if self.persistence_enabled and self.state_file_path.exists():
            await self._load_persisted_state()
        
        # Start background backup task
        if self.persistence_enabled:
            asyncio.create_task(self._periodic_backup())
        
        self.logger.info("State Manager initialized successfully")
    
    async def shutdown(self) -> None:
        """Shutdown the state manager and save final state"""
        self.logger.info("Shutting down State Manager")
        
        if self.persistence_enabled:
            await self._save_state_to_disk()
        
        self.logger.info("State Manager shutdown complete")
    
    async def create_session(self, project_name: str = None) -> str:
        """Create a new conversation session"""
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.conversation_context.update({
            "session_id": session_id,
            "project_name": project_name,
            "start_time": datetime.now().isoformat(),
            "last_activity": datetime.now().isoformat(),
            "total_tasks_completed": 0,
            "current_focus": None,
            "llm_momentum": "high",
            "last_encouragement": "🚀 New session started! Ready to build something amazing!"
        })
        
        self.logger.info(f"Created new session: {session_id} for project: {project_name}")
        
        # Save immediately
        if self.persistence_enabled:
            await self._save_state_to_disk()
        
        return session_id
    
    async def get_latest_session(self) -> Optional[Dict[str, Any]]:
        """Get the most recent session information"""
        if not self.conversation_context.get("session_id"):
            return None
        
        return {
            "session_id": self.conversation_context["session_id"],
            "project_name": self.conversation_context.get("project_name"),
            "start_time": self.conversation_context.get("start_time"),
            "last_activity": self.conversation_context.get("last_activity"),
            "momentum": self.conversation_context.get("llm_momentum", "medium")
        }
    
    async def get_latest_session_for_project(self, project_name: str) -> Optional[Dict[str, Any]]:
        """Get the latest session for a specific project"""
        if (self.conversation_context.get("project_name") == project_name and 
            self.conversation_context.get("session_id")):
            return await self.get_latest_session()
        
        # Could implement project-specific session lookup here
        return None
    
    async def save_session_state(self, session_id: str, state_data: Dict[str, Any]) -> None:
        """Save session-specific state data"""
        self.conversation_context.update(state_data)
        self.conversation_context["last_activity"] = datetime.now().isoformat()
        
        if self.persistence_enabled:
            await self._save_state_to_disk()
        
        self.logger.debug(f"Saved session state for {session_id}")
    
    async def get_session_state(self, session_id: str) -> Dict[str, Any]:
        """Get session-specific state data"""
        if self.conversation_context.get("session_id") == session_id:
            return self.conversation_context.copy()
        
        return {}
    
    async def save_plan(self, session_id: str, plan: Plan) -> None:
        """Save a plan to persistent state"""
        self.current_plans[plan.id] = plan
        
        # Update conversation context
        self.conversation_context.update({
            "current_plan_id": plan.id,
            "current_focus": plan.name,
            "last_activity": datetime.now().isoformat()
        })
        
        if self.persistence_enabled:
            await self._save_state_to_disk()
        
        self.logger.debug(f"Saved plan {plan.id} for session {session_id}")
    
    async def get_plan(self, plan_id: str) -> Optional[Plan]:
        """Get a plan by ID"""
        return self.current_plans.get(plan_id)
    
    async def get_current_plan(self, session_id: str) -> Optional[Plan]:
        """Get the current active plan for a session"""
        current_plan_id = self.conversation_context.get("current_plan_id")
        if current_plan_id:
            return self.current_plans.get(current_plan_id)
        return None
    
    async def save_task(self, task: Task) -> None:
        """Save a task to persistent state"""
        self.current_tasks[task.id] = task
        
        # Update conversation context
        if task.status == TaskStatus.COMPLETED:
            self.conversation_context["total_tasks_completed"] += 1
        
        self.conversation_context["last_activity"] = datetime.now().isoformat()
        
        if self.persistence_enabled:
            await self._save_state_to_disk()
        
        self.logger.debug(f"Saved task {task.id}")
    
    async def get_task(self, task_id: str) -> Optional[Task]:
        """Get a task by ID"""
        return self.current_tasks.get(task_id)
    
    async def get_tasks_by_status(self, status: TaskStatus) -> List[Task]:
        """Get all tasks with a specific status"""
        return [task for task in self.current_tasks.values() if task.status == status]
    
    async def update_coaching_state(self, coaching_data: Dict[str, Any]) -> None:
        """Update coaching-related state"""
        self.coaching_history.append({
            "timestamp": datetime.now().isoformat(),
            "data": coaching_data
        })
        
        # Keep only recent coaching history
        if len(self.coaching_history) > 50:
            self.coaching_history = self.coaching_history[-50:]
        
        # Update conversation context with latest coaching
        self.conversation_context.update({
            "llm_momentum": coaching_data.get("momentum_level", "medium"),
            "last_encouragement": coaching_data.get("message"),
            "last_activity": datetime.now().isoformat()
        })
        
        if self.persistence_enabled:
            await self._save_state_to_disk()
    
    async def get_coaching_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent coaching history"""
        return self.coaching_history[-limit:] if self.coaching_history else []
    
    async def get_momentum_level(self) -> str:
        """Get current LLM momentum level"""
        return self.conversation_context.get("llm_momentum", "medium")
    
    async def set_momentum_level(self, level: str) -> None:
        """Set LLM momentum level (high, medium, low)"""
        self.conversation_context["llm_momentum"] = level
        self.conversation_context["last_activity"] = datetime.now().isoformat()
        
        if self.persistence_enabled:
            await self._save_state_to_disk()
    
    async def save_generated_code(self, session_id: str, code_data: Dict[str, Any]) -> None:
        """Save generated code to session state"""
        if "generated_code" not in self.conversation_context:
            self.conversation_context["generated_code"] = []
        
        self.conversation_context["generated_code"].append({
            "timestamp": datetime.now().isoformat(),
            "filename": code_data.get("filename"),
            "content": code_data.get("content"),
            "language": code_data.get("language"),
            "success": code_data.get("success", True)
        })
        
        # Keep only recent code
        if len(self.conversation_context["generated_code"]) > 20:
            self.conversation_context["generated_code"] = self.conversation_context["generated_code"][-20:]
        
        self.conversation_context["last_activity"] = datetime.now().isoformat()
        
        if self.persistence_enabled:
            await self._save_state_to_disk()
    
    async def get_project_summary(self, project_name: str = None) -> Dict[str, Any]:
        """Get a summary of project state and progress"""
        current_project = project_name or self.conversation_context.get("project_name")
        
        # Count tasks by status
        completed_tasks = len([t for t in self.current_tasks.values() if t.status == TaskStatus.COMPLETED])
        in_progress_tasks = len([t for t in self.current_tasks.values() if t.status == TaskStatus.IN_PROGRESS])
        pending_tasks = len([t for t in self.current_tasks.values() if t.status == TaskStatus.PENDING])
        
        # Get current plan progress
        current_plan = None
        plan_progress = 0
        current_plan_id = self.conversation_context.get("current_plan_id")
        if current_plan_id and current_plan_id in self.current_plans:
            current_plan = self.current_plans[current_plan_id]
            completed_steps = len([s for s in current_plan.steps if s.status == StepStatus.COMPLETED])
            total_steps = len(current_plan.steps)
            plan_progress = (completed_steps / total_steps) * 100 if total_steps > 0 else 0
        
        return {
            "project_name": current_project,
            "session_id": self.conversation_context.get("session_id"),
            "start_time": self.conversation_context.get("start_time"),
            "last_activity": self.conversation_context.get("last_activity"),
            "momentum_level": self.conversation_context.get("llm_momentum"),
            "tasks": {
                "completed": completed_tasks,
                "in_progress": in_progress_tasks,
                "pending": pending_tasks,
                "total": len(self.current_tasks)
            },
            "current_plan": {
                "name": current_plan.name if current_plan else None,
                "progress_percent": plan_progress,
                "total_steps": len(current_plan.steps) if current_plan else 0
            },
            "generated_code_files": len(self.conversation_context.get("generated_code", [])),
            "coaching_interactions": len(self.coaching_history)
        }
    
    async def _load_persisted_state(self) -> None:
        """Load state from disk"""
        if not self.state_file_path.exists():
            return
        
        with open(self.state_file_path, 'r') as f:
            data = json.load(f)
        
        # Restore conversation context
        self.conversation_context.update(data.get("conversation_context", {}))
        
        # Restore tasks
        tasks_data = data.get("tasks", {})
        for task_id, task_data in tasks_data.items():
            task = Task(
                id=task_data["id"],
                project_id=task_data.get("project_id", "unknown"),
                title=task_data.get("title", task_data.get("name", "Untitled")),  # Handle both title and name
                description=task_data["description"],
                status=TaskStatus(task_data["status"]),
                priority=TaskPriority(task_data["priority"]),
                created_at=datetime.fromisoformat(task_data["created_at"]),
                updated_at=datetime.fromisoformat(task_data["updated_at"]) if task_data.get("updated_at") else None
            )
            self.current_tasks[task_id] = task
        
        # Restore plans
        plans_data = data.get("plans", {})
        for plan_id, plan_data in plans_data.items():
            plan = Plan(
                id=plan_data["id"],
                name=plan_data["name"],
                description=plan_data["description"],
                created_at=datetime.fromisoformat(plan_data["created_at"]),
                priority=TaskPriority(plan_data["priority"])
            )
            
            # Restore plan steps
            for step_data in plan_data.get("steps", []):
                step = PlanStep(
                    id=step_data["id"],
                    name=step_data["name"],
                    description=step_data["description"],
                    dependencies=step_data.get("dependencies", []),
                    estimated_duration=step_data.get("estimated_duration", 30),
                    status=StepStatus(step_data["status"])
                )
                plan.add_step(step)
            
            self.current_plans[plan_id] = plan
        
        # Restore coaching history
        self.coaching_history = data.get("coaching_history", [])
        
        self.logger.info("Loaded persisted state from disk")
    
    async def _save_state_to_disk(self) -> None:
        """Save current state to disk"""
        # Prepare data for serialization
        data = {
            "conversation_context": self.conversation_context,
            "tasks": {
                task_id: {
                    "id": task.id,
                    "title": task.title,  # Use title instead of name
                    "description": task.description,
                    "status": task.status.value,
                    "priority": task.priority.value,
                    "created_at": task.created_at.isoformat(),
                    "updated_at": task.updated_at.isoformat() if task.updated_at else None
                }
                for task_id, task in self.current_tasks.items()
            },
            "plans": {
                plan_id: {
                    "id": plan.id,
                    "name": plan.name,
                    "description": plan.description,
                    "created_at": plan.created_at.isoformat(),
                    "priority": plan.priority.value,
                    "steps": [
                        {
                            "id": step.id,
                            "name": step.name,
                            "description": step.description,
                            "dependencies": step.dependencies,
                            "estimated_duration": step.estimated_duration,
                            "status": step.status.value
                        }
                        for step in plan.steps
                    ]
                }
                for plan_id, plan in self.current_plans.items()
            },
            "coaching_history": self.coaching_history,
            "saved_at": datetime.now().isoformat()
        }
        
        # Write to disk atomically
        temp_file = self.state_file_path.with_suffix('.tmp')
        with open(temp_file, 'w') as f:
            json.dump(data, f, indent=2)
        
        temp_file.replace(self.state_file_path)
        self.logger.debug("Saved state to disk")
    
    async def _periodic_backup(self) -> None:
        """Periodically backup state to disk"""
        while True:
            await asyncio.sleep(self.backup_interval * 60)  # Convert minutes to seconds
            await self._save_state_to_disk()
    
    async def cleanup_old_sessions(self, days_old: int = 30) -> None:
        """Clean up old session data"""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        # This would implement cleanup logic for old sessions
        # For now, just log the intent
        self.logger.info(f"Would clean up sessions older than {cutoff_date}")
    
    async def export_session_data(self, session_id: str) -> Dict[str, Any]:
        """Export all data for a specific session"""
        return {
            "session_info": self.conversation_context if self.conversation_context.get("session_id") == session_id else {},
            "tasks": {k: v for k, v in self.current_tasks.items()},
            "plans": {k: v for k, v in self.current_plans.items()},
            "coaching_history": self.coaching_history,
            "exported_at": datetime.now().isoformat()
        }
    
    async def initialize_session(self, session_id: str = None) -> Dict[str, Any]:
        """
        Initialize a new session or resume an existing one
        
        Args:
            session_id: Optional session ID to resume
            
        Returns:
            Session initialization result
        """
        if session_id:
            # Try to resume existing session
            resumed = await self.resume_session(session_id)
            if resumed:
                self.logger.info(f"Resumed session {session_id}")
                return {
                    "session_id": session_id,
                    "resumed": True,
                    "tasks": list(self.current_tasks.keys()),
                    "plans": list(self.current_plans.keys()),
                    "context": self.conversation_context
                }
        
        # Create new session
        new_session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.conversation_context.update({
            "session_id": new_session_id,
            "start_time": datetime.now().isoformat(),
            "last_activity": datetime.now().isoformat(),
            "total_tasks_completed": 0,
            "current_focus": None,
            "llm_momentum": "high"
        })
        
        await self.save_state()
        
        self.logger.info(f"Started new session {new_session_id}")
        return {
            "session_id": new_session_id,
            "resumed": False,
            "tasks": [],
            "plans": [],
            "context": self.conversation_context
        }
    
    async def resume_session(self, session_id: str) -> bool:
        """
        Resume a previous session
        
        Args:
            session_id: Session ID to resume
            
        Returns:
            True if session was successfully resumed
        """
        if not self.persistence_enabled:
            return False
        
        if not self.state_file_path.exists():
            return False
        
        state_data = await self.load_state()
        if not state_data or state_data.get("session_id") != session_id:
            return False
        
        # Restore state
        self.conversation_context = state_data.get("conversation_context", {})
        self.coaching_history = state_data.get("coaching_history", [])
        self.llm_interaction_patterns = state_data.get("llm_interaction_patterns", {})
        
        # Restore tasks
        tasks_data = state_data.get("tasks", {})
        for task_id, task_data in tasks_data.items():
            self.current_tasks[task_id] = Task.from_dict(task_data)
        
        # Restore plans
        plans_data = state_data.get("plans", {})
        for plan_id, plan_data in plans_data.items():
            self.current_plans[plan_id] = Plan.from_dict(plan_data)
        
        # Update last activity
        self.conversation_context["last_activity"] = datetime.now().isoformat()
        
        return True
    
    async def save_state(self) -> None:
        """Save current state to persistent storage"""
        if not self.persistence_enabled:
            return
        
        state_data = {
            "session_id": self.conversation_context.get("session_id"),
            "timestamp": datetime.now().isoformat(),
            "conversation_context": self.conversation_context,
            "coaching_history": self.coaching_history[-50:],  # Keep last 50 entries
            "llm_interaction_patterns": self.llm_interaction_patterns,
            "tasks": {
                task_id: task.to_dict() 
                for task_id, task in self.current_tasks.items()
            },
            "plans": {
                plan_id: plan.to_dict() 
                for plan_id, plan in self.current_plans.items()
            }
        }
        
        # Write to file
        with open(self.state_file_path, 'w') as f:
            json.dump(state_data, f, indent=2, default=str)
        
        self.logger.debug("State saved successfully")
    
    async def load_state(self) -> Optional[Dict[str, Any]]:
        """Load state from persistent storage"""
        if not self.persistence_enabled or not self.state_file_path.exists():
            return None
        
        with open(self.state_file_path, 'r') as f:
            return json.load(f)
    
    async def create_task(self, task_data: Dict[str, Any]) -> Task:
        """Create a new task and add it to state"""
        task = Task(
            id=task_data.get("id", f"task_{len(self.current_tasks) + 1}"),
            title=task_data["title"],
            description=task_data["description"],
            priority=TaskPriority(task_data.get("priority", "medium")),
            status=TaskStatus.PENDING,
            created_at=datetime.now(),
            metadata=task_data.get("metadata", {})
        )
        
        self.current_tasks[task.id] = task
        await self.update_activity()
        await self.save_state()
        
        return task
    
    async def update_task_status(self, task_id: str, status: TaskStatus, progress: float = None) -> bool:
        """Update task status and progress"""
        if task_id not in self.current_tasks:
            return False
        
        task = self.current_tasks[task_id]
        old_status = task.status
        task.status = status
        
        if progress is not None:
            task.progress = progress
        
        task.updated_at = datetime.now()
        
        # Update conversation context
        if status == TaskStatus.COMPLETED:
            self.conversation_context["total_tasks_completed"] += 1
            self.llm_interaction_patterns["successful_completions"] += 1
        
        # Log momentum change
        if old_status != status:
            await self.record_coaching_event("task_status_change", {
                "task_id": task_id,
                "old_status": old_status.value,
                "new_status": status.value,
                "progress": progress
            })
        
        await self.update_activity()
        await self.save_state()
        
        return True
    
    async def create_plan(self, plan_data: Dict[str, Any]) -> Plan:
        """Create a new plan and add it to state"""
        plan = Plan(
            id=plan_data.get("id", f"plan_{len(self.current_plans) + 1}"),
            title=plan_data["title"],
            description=plan_data["description"],
            steps=[
                PlanStep(
                    id=step_data.get("id", f"step_{i}"),
                    title=step_data["title"],
                    description=step_data["description"],
                    status=StepStatus(step_data.get("status", "pending")),
                    dependencies=step_data.get("dependencies", []),
                    estimated_duration=step_data.get("estimated_duration"),
                    metadata=step_data.get("metadata", {})
                )
                for i, step_data in enumerate(plan_data.get("steps", []))
            ],
            created_at=datetime.now(),
            metadata=plan_data.get("metadata", {})
        )
        
        self.current_plans[plan.id] = plan
        await self.update_activity()
        await self.save_state()
        
        return plan
    
    async def update_plan_step(self, plan_id: str, step_id: str, status: StepStatus) -> bool:
        """Update plan step status"""
        if plan_id not in self.current_plans:
            return False
        
        plan = self.current_plans[plan_id]
        step = plan.get_step(step_id)
        
        if not step:
            return False
        
        old_status = step.status
        step.status = status
        step.updated_at = datetime.now()
        
        # Update plan progress
        plan.calculate_progress()
        plan.updated_at = datetime.now()
        
        # Log progress
        await self.record_coaching_event("plan_step_update", {
            "plan_id": plan_id,
            "step_id": step_id,
            "old_status": old_status.value,
            "new_status": status.value,
            "plan_progress": plan.progress
        })
        
        await self.update_activity()
        await self.save_state()
        
        return True
    
    async def record_coaching_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Record a coaching event for analysis"""
        event = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "data": data,
            "session_id": self.conversation_context.get("session_id")
        }
        
        self.coaching_history.append(event)
        
        # Update interaction patterns based on event
        if event_type == "llm_hesitation":
            self.llm_interaction_patterns["hesitation_count"] += 1
            self.llm_interaction_patterns["last_stuck_point"] = data.get("hesitation_type")
        elif event_type == "error_recovery":
            self.llm_interaction_patterns["error_recovery_count"] += 1
        elif event_type == "task_completion":
            self.llm_interaction_patterns["successful_completions"] += 1
    
    async def get_resumption_context(self) -> Dict[str, Any]:
        """
        Get context for resuming work in a new conversation
        
        Returns:
            Context data for resuming work
        """
        incomplete_tasks = [
            task for task in self.current_tasks.values() 
            if task.status not in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]
        ]
        
        incomplete_plans = []
        for plan in self.current_plans.values():
            incomplete_steps = [step for step in plan.steps if step.status != StepStatus.COMPLETED]
            if incomplete_steps:
                incomplete_plans.append({
                    "plan": plan,
                    "incomplete_steps": incomplete_steps,
                    "progress": plan.progress
                })
        
        # Generate resumption message
        resumption_message = await self._generate_resumption_message(incomplete_tasks, incomplete_plans)
        
        return {
            "session_info": self.conversation_context,
            "incomplete_tasks": [task.to_dict() for task in incomplete_tasks],
            "incomplete_plans": incomplete_plans,
            "coaching_summary": await self._generate_coaching_summary(),
            "resumption_message": resumption_message,
            "momentum_level": self._calculate_momentum_level(),
            "recommended_next_action": await self._recommend_next_action(incomplete_tasks, incomplete_plans)
        }
    
    async def _generate_resumption_message(self, tasks: List[Task], plans: List[Dict]) -> str:
        """Generate an encouraging resumption message"""
        if not tasks and not plans:
            return "🎉 Amazing! All tasks are complete! Ready to take on new challenges!"
        
        task_count = len(tasks)
        plan_count = len(plans)
        
        message = f"🚀 Welcome back! You have {task_count} active tasks and {plan_count} plans in progress. "
        message += "Let's pick up where we left off and keep building momentum! "
        
        if self.conversation_context.get("total_tasks_completed", 0) > 0:
            message += f"You've already completed {self.conversation_context['total_tasks_completed']} tasks - excellent progress! "
        
        message += "Ready to continue with full energy and focus! 💪"
        
        return message
    
    async def _generate_coaching_summary(self) -> Dict[str, Any]:
        """Generate a summary of coaching interactions"""
        patterns = self.llm_interaction_patterns
        
        return {
            "total_hesitations": patterns.get("hesitation_count", 0),
            "error_recoveries": patterns.get("error_recovery_count", 0),
            "successful_completions": patterns.get("successful_completions", 0),
            "last_stuck_point": patterns.get("last_stuck_point"),
            "coaching_effectiveness": "high" if patterns.get("successful_completions", 0) > patterns.get("hesitation_count", 0) else "medium"
        }
    
    def _calculate_momentum_level(self) -> str:
        """Calculate current momentum level"""
        patterns = self.llm_interaction_patterns
        
        completions = patterns.get("successful_completions", 0)
        hesitations = patterns.get("hesitation_count", 0)
        
        if completions > hesitations * 2:
            return "high"
        elif completions > hesitations:
            return "medium"
        else:
            return "low"
    
    async def _recommend_next_action(self, tasks: List[Task], plans: List[Dict]) -> str:
        """Recommend the next action to take"""
        if not tasks and not plans:
            return "Start a new task or create a new plan"
        
        # Find highest priority incomplete task
        high_priority_tasks = [t for t in tasks if t.priority == TaskPriority.HIGH]
        if high_priority_tasks:
            task = high_priority_tasks[0]
            return f"Continue with high-priority task: {task.title}"
        
        # Find plan with most progress
        if plans:
            best_plan = max(plans, key=lambda p: p["progress"])
            return f"Continue with plan: {best_plan['plan'].title} ({best_plan['progress']:.1f}% complete)"
        
        # Default to first available task
        if tasks:
            return f"Continue with task: {tasks[0].title}"
        
        return "Review current progress and plan next steps"
    
    async def update_activity(self) -> None:
        """Update last activity timestamp"""
        self.conversation_context["last_activity"] = datetime.now().isoformat()
    
    async def get_state_summary(self) -> Dict[str, Any]:
        """Get a summary of current state"""
        return {
            "session_id": self.conversation_context.get("session_id"),
            "active_tasks": len(self.current_tasks),
            "active_plans": len(self.current_plans),
            "completed_tasks": self.conversation_context.get("total_tasks_completed", 0),
            "momentum_level": self._calculate_momentum_level(),
            "last_activity": self.conversation_context.get("last_activity"),
            "coaching_events": len(self.coaching_history)
        }
