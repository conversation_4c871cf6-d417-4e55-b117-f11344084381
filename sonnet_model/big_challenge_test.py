"""
BIG CHALLENGE TEST

This tests the robust iterative improvement system with a REALLY CHALLENGING task:
- Complex e-commerce platform with multiple components
- High security requirements
- Performance optimization needs
- Comprehensive error handling
- Production-ready code standards

This will prove if the robust system can handle REAL complexity!
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any, List
from working_project_generator import WorkingProjectGenerator

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class BigChallengeTest:
    """Test the robust system with a big, challenging project"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    async def run_big_challenge(self):
        """Run the big challenge test"""
        
        print("🚀 BIG CHALLENGE TEST")
        print("=" * 70)
        print("Testing ROBUST ITERATIVE IMPROVEMENT with complex e-commerce platform")
        print("🎯 This will prove if the system can handle REAL complexity!")
        print("=" * 70)
        
        # Create the challenging project description
        challenge_description = """Create a secure e-commerce platform with user authentication, 
        product management, shopping cart, payment processing, order management, 
        inventory tracking, admin dashboard, and comprehensive security features"""
        
        print(f"📝 CHALLENGE: {challenge_description}")
        print(f"🎯 EXPECTED: Production-ready, secure, scalable code")
        print(f"🔄 QUALITY FOCUS: No arbitrary limits - iterate until excellent!")
        
        # Initialize the robust generator
        generator = WorkingProjectGenerator()
        
        print(f"\n📊 ROBUST SYSTEM SETTINGS:")
        print(f"   Quality Threshold: {generator.minimum_quality_threshold}/10")
        print(f"   Improvement Threshold: {generator.minimum_improvement_threshold}")
        print(f"   Stagnation Limit: {generator.stagnation_limit} iterations")
        print(f"   Safety Limit: {generator.absolute_max_iterations} iterations")
        print(f"   Quality Metrics: {len(generator.quality_metrics)} dimensions")
        
        # Run the challenge
        print(f"\n🚀 STARTING BIG CHALLENGE...")
        print("-" * 70)
        
        start_time = asyncio.get_event_loop().time()
        result = await generator.create_project_from_description(challenge_description)
        end_time = asyncio.get_event_loop().time()
        
        # Analyze results
        await self._analyze_challenge_results(result, end_time - start_time)
        
        return result
    
    async def _analyze_challenge_results(self, result: Dict[str, Any], duration: float):
        """Analyze the results of the big challenge"""
        
        print(f"\n📊 BIG CHALLENGE RESULTS ANALYSIS")
        print("=" * 70)
        
        # Basic metrics
        total_files = result.get('total_files', 0)
        successful_files = result.get('successful_files', 0)
        success_rate = result.get('success_rate', 0)
        total_iterations = result.get('total_iterations', 0)
        
        print(f"⏱️ Duration: {duration:.1f} seconds")
        print(f"📄 Files: {successful_files}/{total_files}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        print(f"🔄 Total Iterations: {total_iterations}")
        print(f"📊 Avg Iterations/File: {total_iterations/total_files:.1f}")
        
        # Detailed file analysis
        print(f"\n📋 DETAILED FILE ANALYSIS:")
        print("-" * 40)
        
        files = result.get('files', [])
        excellent_files = 0
        good_files = 0
        poor_files = 0
        
        for file_info in files:
            path = file_info.get('path', 'unknown')
            success = file_info.get('success', False)
            iterations = file_info.get('iterations', 0)
            size = file_info.get('size', 0)
            
            status = "✅ EXCELLENT" if success else "❌ NEEDS WORK"
            print(f"   {status}: {path}")
            print(f"      Iterations: {iterations}, Size: {size} chars")
            
            if success:
                if iterations <= 3:
                    excellent_files += 1
                else:
                    good_files += 1
            else:
                poor_files += 1
        
        # Quality assessment
        print(f"\n🎯 QUALITY ASSESSMENT:")
        print("-" * 40)
        print(f"🌟 Excellent (≤3 iterations): {excellent_files}")
        print(f"✅ Good (4+ iterations): {good_files}")
        print(f"❌ Needs Work: {poor_files}")
        
        # Check if project is production-ready
        production_ready = success_rate >= 80 and poor_files == 0
        
        print(f"\n🚀 PRODUCTION READINESS:")
        print("-" * 40)
        if production_ready:
            print("✅ PRODUCTION READY!")
            print("   - High success rate (≥80%)")
            print("   - No failed files")
            print("   - Quality-driven improvement worked!")
        else:
            print("⚠️ NEEDS MORE WORK")
            print(f"   - Success rate: {success_rate:.1f}% (need ≥80%)")
            print(f"   - Failed files: {poor_files}")
        
        # Analyze the generated code quality
        await self._analyze_generated_code_quality(result)
        
        # Overall assessment
        self._provide_overall_assessment(result, production_ready, duration)
    
    async def _analyze_generated_code_quality(self, result: Dict[str, Any]):
        """Analyze the quality of generated code"""
        
        print(f"\n🔍 CODE QUALITY ANALYSIS:")
        print("-" * 40)
        
        project_path = Path(result.get('project_path', ''))
        
        if not project_path.exists():
            print("❌ Project directory not found")
            return
        
        # Analyze each Python file
        python_files = list(project_path.glob('*.py'))
        
        if not python_files:
            print("❌ No Python files found")
            return
        
        total_lines = 0
        total_functions = 0
        has_docstrings = 0
        has_error_handling = 0
        
        for py_file in python_files:
            try:
                with open(py_file, 'r') as f:
                    content = f.read()
                
                lines = len(content.split('\n'))
                total_lines += lines
                
                # Count functions
                functions = content.count('def ')
                total_functions += functions
                
                # Check for docstrings
                if '"""' in content or "'''" in content:
                    has_docstrings += 1
                
                # Check for error handling
                if 'try:' in content or 'except' in content or 'raise' in content:
                    has_error_handling += 1
                
                print(f"   📄 {py_file.name}: {lines} lines, {functions} functions")
                
            except Exception as e:
                print(f"   ❌ Error analyzing {py_file.name}: {e}")
        
        # Quality metrics
        print(f"\n📊 CODE QUALITY METRICS:")
        print(f"   📄 Total Lines: {total_lines}")
        print(f"   🔧 Total Functions: {total_functions}")
        print(f"   📝 Files with Docstrings: {has_docstrings}/{len(python_files)}")
        print(f"   🛡️ Files with Error Handling: {has_error_handling}/{len(python_files)}")
        
        # Quality score
        documentation_score = (has_docstrings / len(python_files)) * 100
        error_handling_score = (has_error_handling / len(python_files)) * 100
        
        print(f"\n📈 QUALITY SCORES:")
        print(f"   📝 Documentation: {documentation_score:.1f}%")
        print(f"   🛡️ Error Handling: {error_handling_score:.1f}%")
    
    def _provide_overall_assessment(self, result: Dict[str, Any], 
                                  production_ready: bool, duration: float):
        """Provide overall assessment of the robust system"""
        
        print(f"\n🎉 OVERALL ASSESSMENT")
        print("=" * 70)
        
        success_rate = result.get('success_rate', 0)
        total_iterations = result.get('total_iterations', 0)
        
        if production_ready and success_rate >= 90:
            print("🚀 OUTSTANDING SUCCESS!")
            print("✅ The robust iterative improvement system WORKS!")
            print("✅ Quality-driven approach achieved excellent results")
            print("✅ No arbitrary limits - quality was the only criteria")
            print("✅ Complex project handled successfully")
            
        elif production_ready:
            print("✅ GOOD SUCCESS!")
            print("✅ The robust system shows significant improvement")
            print("✅ Quality-driven approach is working")
            print("⚠️ Some files needed more iterations than expected")
            
        elif success_rate >= 60:
            print("⚠️ PARTIAL SUCCESS")
            print("✅ System shows promise but needs refinement")
            print("⚠️ Some components of robust system working")
            print("🔧 Need to improve critique specificity")
            
        else:
            print("❌ NEEDS SIGNIFICANT WORK")
            print("❌ Robust system not performing as expected")
            print("🔧 Major improvements needed in critique and generation")
        
        print(f"\n📊 KEY METRICS:")
        print(f"   Success Rate: {success_rate:.1f}%")
        print(f"   Total Iterations: {total_iterations}")
        print(f"   Duration: {duration:.1f} seconds")
        print(f"   Production Ready: {'Yes' if production_ready else 'No'}")
        
        print(f"\n🎯 ROBUST SYSTEM VALIDATION:")
        if total_iterations > result.get('total_files', 1) * 2:
            print("✅ Quality-driven iteration working (multiple iterations per file)")
        else:
            print("⚠️ May not be iterating enough for quality")
        
        if success_rate >= 80:
            print("✅ High success rate achieved")
        else:
            print("⚠️ Success rate below target")
        
        print(f"\n🚀 CONCLUSION:")
        if production_ready:
            print("The robust iterative improvement system successfully")
            print("created a complex project with quality-driven iteration!")
        else:
            print("The system shows progress but needs further refinement")
            print("to achieve consistent production-ready results.")


async def main():
    """Run the big challenge test"""
    
    print("🎯 BIG CHALLENGE TEST - ROBUST ITERATIVE IMPROVEMENT")
    print("Testing with complex e-commerce platform")
    print("=" * 70)
    
    tester = BigChallengeTest()
    result = await tester.run_big_challenge()
    
    print(f"\n🎉 BIG CHALLENGE TEST COMPLETED!")
    
    # Final verdict
    success_rate = result.get('success_rate', 0)
    if success_rate >= 80:
        print("🚀 SUCCESS! The robust system handles big challenges!")
    elif success_rate >= 60:
        print("✅ Good progress, system shows promise")
    else:
        print("⚠️ System needs more work for complex challenges")
    
    return result


if __name__ == "__main__":
    asyncio.run(main())
