#!/usr/bin/env python3
"""
Test script to verify the immediate fixes are working correctly
"""

import asyncio
import json
import tempfile
import shutil
from pathlib import Path
from integrated_project_generator import IntegratedProjectGenerator

async def test_simple_request_detection():
    """Test that simple requests are detected correctly"""
    generator = IntegratedProjectGenerator()
    
    # Test simple requests
    simple_requests = [
        "Create a simple math utility function",
        "Basic string helper functions",
        "Simple file utility",
        "Quick calculator function"
    ]
    
    # Test complex requests
    complex_requests = [
        "Create a web application with user authentication",
        "Build a REST API with database integration",
        "Develop a microservice architecture",
        "Create a full-stack application"
    ]
    
    print("🧪 Testing Simple Request Detection")
    print("=" * 50)
    
    for request in simple_requests:
        is_simple = generator._is_simple_request(request)
        print(f"✅ '{request}' -> Simple: {is_simple}")
        assert is_simple, f"Should detect '{request}' as simple"
    
    for request in complex_requests:
        is_simple = generator._is_simple_request(request)
        print(f"🏗️ '{request}' -> Simple: {is_simple}")
        assert not is_simple, f"Should detect '{request}' as complex"
    
    print("✅ Simple request detection working correctly!\n")

async def test_scope_validation():
    """Test scope validation functionality"""
    generator = IntegratedProjectGenerator()
    
    print("🧪 Testing Scope Validation")
    print("=" * 50)
    
    # Test math utility request
    description = "Create a simple math utility function"
    
    # Simulate tasks from LLM (some relevant, some not)
    tasks = [
        {
            "path": "math_utils.py",
            "description": "Mathematical utility functions",
            "requirements": ["Add function", "Multiply function", "Divide function"]
        },
        {
            "path": "backend/models.py", 
            "description": "Database models for the application",
            "requirements": ["User model", "Task model", "Authentication"]
        },
        {
            "path": "backend/api.py",
            "description": "REST API endpoints", 
            "requirements": ["CRUD operations", "Authentication middleware"]
        }
    ]
    
    validated_tasks = generator._validate_scope_alignment(description, tasks)
    
    print(f"Original tasks: {len(tasks)}")
    print(f"Validated tasks: {len(validated_tasks)}")
    
    # Should keep only the math_utils.py task
    assert len(validated_tasks) == 1, f"Expected 1 task, got {len(validated_tasks)}"
    assert validated_tasks[0]['path'] == "math_utils.py", "Should keep only math utility task"
    
    print("✅ Scope validation working correctly!\n")

async def test_simple_task_plan_creation():
    """Test simple task plan creation"""
    generator = IntegratedProjectGenerator()
    
    print("🧪 Testing Simple Task Plan Creation")
    print("=" * 50)
    
    description = "Create a simple math utility function"
    plan = generator._create_simple_task_plan(description)
    
    print(f"Project name: {plan['name']}")
    print(f"Number of tasks: {len(plan['tasks'])}")
    print(f"Task file: {plan['tasks'][0]['path']}")
    
    # Verify plan structure
    assert plan['name'] == "create_a_simple", "Project name should be extracted correctly"
    assert len(plan['tasks']) == 1, "Should create exactly one task for simple request"
    assert 'math_utils.py' in plan['tasks'][0]['path'], "Should create math utility file"
    
    print("✅ Simple task plan creation working correctly!\n")

async def test_end_to_end_simple_request():
    """Test end-to-end processing of a simple request"""
    generator = IntegratedProjectGenerator()
    
    print("🧪 Testing End-to-End Simple Request Processing")
    print("=" * 50)
    
    # Create temporary directory for test
    with tempfile.TemporaryDirectory() as temp_dir:
        original_cwd = Path.cwd()
        
        try:
            # Change to temp directory
            import os
            os.chdir(temp_dir)
            
            # Test simple request (should use simple path)
            description = "Create a simple math utility function"
            
            # Mock the LLM request to avoid actual API calls
            async def mock_llm_request(prompt, component):
                return "Mock response"
            
            generator._send_llm_request = mock_llm_request
            
            # Test task plan creation
            plan = await generator._create_task_plan(description)
            
            print(f"Plan created: {plan['name']}")
            print(f"Tasks: {len(plan['tasks'])}")
            
            # Should use simple task creation (not LLM)
            assert len(plan['tasks']) == 1, "Simple request should create 1 task"
            assert 'math_utils.py' in plan['tasks'][0]['path'], "Should create math utility"
            
            print("✅ End-to-end simple request processing working correctly!\n")
            
        finally:
            os.chdir(original_cwd)

async def main():
    """Run all tests"""
    print("🚀 TESTING IMMEDIATE FIXES")
    print("=" * 70)
    
    try:
        await test_simple_request_detection()
        await test_scope_validation()
        await test_simple_task_plan_creation()
        await test_end_to_end_simple_request()
        
        print("🎉 ALL TESTS PASSED!")
        print("✅ Immediate fixes are working correctly")
        print("✅ System should now handle simple requests properly")
        print("✅ Scope validation will prevent over-engineering")
        print("✅ Enhanced critique engine will check relevance")
        
    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
