"""
Complete End-to-End Feedback Loop Test
Task: Create a small interactive GUI for interactive plotting

This demonstrates the COMPLETE feedback process:
1. User Input: "create a small interactive gui for interactive plotting"
2. Plan Parser: Extracts structured plan and tasks
3. Task Orchestrator: Manages execution with feedback loop
4. Code Generator: Creates code based on requirements
5. Critique Engine: Analyzes code quality and provides feedback
6. Feedback Integration: Passes specific feedback back to code generator
7. Iterative Improvement: Continues until quality threshold met

You'll see each step in detail!
"""

import asyncio
import json
import logging
import os
import shutil
from pathlib import Path
from typing import Dict, Any, List
import httpx

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class InteractivePlottingFeedbackDemo:
    """Complete demonstration of the feedback loop for interactive plotting GUI"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model = "deepseek-coder-v2:16b"
        
        # Project setup
        self.project_name = "interactive_plotting_gui"
        self.project_path = Path(self.project_name)
        
        # User's high-level input
        self.user_input = "create a small interactive gui for interactive plotting"
        
        # Parsed tasks (simulating plan parser output)
        self.tasks = [
            {
                "id": 1,
                "title": "Interactive Plotting GUI",
                "description": "Create a GUI application for interactive data plotting with user controls",
                "filename": "plotting_gui.py",
                "requirements": [
                    "Create a GUI window using tkinter or PyQt",
                    "Add interactive controls for plot customization",
                    "Support multiple plot types (line, scatter, bar)",
                    "Allow users to input data or load from file",
                    "Real-time plot updates based on user input",
                    "Export functionality for saving plots",
                    "Clean, intuitive user interface design"
                ],
                "language": "python",
                "frameworks": ["tkinter", "matplotlib"],
                "priority": "high"
            }
        ]
        
    async def run_complete_feedback_demo(self):
        """Run the complete feedback loop demonstration"""
        
        print("🎯 COMPLETE FEEDBACK LOOP DEMONSTRATION")
        print("=" * 70)
        print(f"📝 User Input: '{self.user_input}'")
        print(f"📁 Project: {self.project_name}")
        print("🔄 Demonstrating: Plan Parsing → Code Generation → Critique → Feedback → Improvement")
        print("=" * 70)
        
        # Step 1: Test LLM Connection
        print("\n🔌 STEP 1: Testing LLM Connection")
        if not await self._test_ollama_connection():
            print("❌ Cannot connect to Ollama. Please start Ollama first.")
            return
        print("✅ Connected to Ollama successfully!")
        
        # Step 2: Plan Parsing Simulation
        print("\n📋 STEP 2: Plan Parsing (Simulated)")
        self._demonstrate_plan_parsing()
        
        # Step 3: Project Setup
        print("\n📁 STEP 3: Project Setup")
        self._setup_project_folder()
        
        # Step 4: Complete Feedback Loop
        print("\n🔄 STEP 4: Complete Feedback Loop Execution")
        for task in self.tasks:
            await self._run_task_feedback_loop(task)
        
        # Step 5: Final Results
        print("\n📊 STEP 5: Final Results")
        self._show_final_results()
        
        print("\n🎉 COMPLETE FEEDBACK LOOP DEMONSTRATION FINISHED!")
        print(f"📁 Check the '{self.project_name}' folder to see the actual generated files!")
        
    def _demonstrate_plan_parsing(self):
        """Demonstrate how plan parsing works"""
        
        print(f"   📝 Original Input: '{self.user_input}'")
        print("   🧠 Plan Parser Analysis:")
        print("      ✅ Detected Language: Python")
        print("      ✅ Detected Frameworks: tkinter, matplotlib")
        print("      ✅ Identified Task Type: GUI Development")
        print("      ✅ Extracted Requirements: 7 requirements found")
        print("      ✅ Priority Assessment: High")
        
        print("\n   📋 Parsed Task Structure:")
        task = self.tasks[0]
        print(f"      Title: {task['title']}")
        print(f"      Description: {task['description']}")
        print(f"      Language: {task['language']}")
        print(f"      Frameworks: {', '.join(task['frameworks'])}")
        print(f"      Requirements: {len(task['requirements'])} items")
        
    def _setup_project_folder(self):
        """Setup the project folder"""
        
        # Remove existing folder if it exists
        if self.project_path.exists():
            shutil.rmtree(self.project_path)
        
        # Create new project folder
        self.project_path.mkdir()
        
        # Create README
        readme_content = f"""# {self.project_name.title().replace('_', ' ')}

An interactive GUI application for data plotting and visualization.

## Features:
- Interactive plotting interface
- Multiple plot types support
- Real-time plot updates
- Data input/loading capabilities
- Plot export functionality

## Requirements:
- Python 3.7+
- tkinter (usually included with Python)
- matplotlib
- numpy (for data handling)

## Usage:
```bash
python plotting_gui.py
```

Generated by Sonnet Model Feedback Loop System.
"""
        
        with open(self.project_path / "README.md", "w") as f:
            f.write(readme_content)
        
        print(f"   📁 Created project folder: {self.project_path}")
        print(f"   📄 Created README.md with project documentation")
        
    async def _run_task_feedback_loop(self, task: Dict):
        """Run the complete feedback loop for a task"""
        
        print(f"\n🎯 TASK: {task['title']}")
        print(f"📄 File: {task['filename']}")
        print("=" * 50)
        
        file_path = self.project_path / task['filename']
        iteration = 1
        max_iterations = 5  # Limit for demo
        quality_threshold = 7.5
        
        # Track feedback history
        feedback_history = []
        
        while iteration <= max_iterations:
            print(f"\n🔄 ITERATION {iteration}")
            print("-" * 30)
            
            # Read existing file content if it exists
            existing_content = ""
            if file_path.exists():
                with open(file_path, 'r') as f:
                    existing_content = f.read()
                print(f"📖 Reading existing file ({len(existing_content)} chars)")
            
            # STEP A: Code Generation
            print("🤖 CODE GENERATOR: Generating/improving code...")
            await self._demonstrate_code_generation_step(task, existing_content, iteration, feedback_history)
            
            new_content = await self._generate_code_with_feedback(task, existing_content, iteration, feedback_history)
            
            if not new_content:
                print("❌ Code generation failed")
                break
            
            # Write to actual file
            with open(file_path, 'w') as f:
                f.write(new_content)
            
            print(f"📄 File written: {file_path}")
            print(f"📊 File size: {len(new_content)} characters")
            
            # Show code preview
            self._show_code_preview(new_content)
            
            # STEP B: Critique Analysis
            print("\n🔍 CRITIQUE ENGINE: Analyzing code quality...")
            await self._demonstrate_critique_step(task, new_content, iteration)
            
            critique = await self._critique_code_with_context(new_content, task, iteration)
            
            if not critique:
                print("❌ Critique failed")
                break
            
            # Show critique results
            self._show_critique_results(critique)
            
            # STEP C: Feedback Processing
            print("\n🔄 FEEDBACK PROCESSOR: Preparing feedback for next iteration...")
            feedback = self._process_critique_into_feedback(critique, iteration)
            feedback_history.append(feedback)
            
            self._show_feedback_processing(feedback)
            
            # STEP D: Decision Point
            quality_score = critique.get('quality_score', 0)
            if quality_score >= quality_threshold:
                print(f"\n✅ QUALITY THRESHOLD REACHED! ({quality_score}/{quality_threshold})")
                print("🎉 Task completed successfully!")
                break
            elif iteration == max_iterations:
                print(f"\n⚠️ Maximum iterations reached ({max_iterations})")
                print(f"📊 Final quality: {quality_score}/{quality_threshold}")
                break
            else:
                print(f"\n🔧 Quality below threshold ({quality_score}/{quality_threshold})")
                print(f"🔄 Preparing iteration {iteration + 1} with feedback...")
                iteration += 1
                await asyncio.sleep(1)  # Brief pause for readability
        
        print(f"\n📋 TASK SUMMARY:")
        print(f"   Iterations: {iteration}")
        print(f"   Final Quality: {critique.get('quality_score', 0)}/10")
        print(f"   File Size: {len(new_content)} characters")
        print(f"   Status: {'✅ Completed' if quality_score >= quality_threshold else '⚠️ Partial'}")

    async def _demonstrate_code_generation_step(self, task: Dict, existing_content: str, iteration: int, feedback_history: List):
        """Demonstrate the code generation step"""

        print("   📝 Code Generation Process:")
        if iteration == 1:
            print("      🆕 First iteration - generating initial code")
            print(f"      📋 Requirements: {len(task['requirements'])} items")
            print(f"      🎯 Target: {task['description']}")
        else:
            print(f"      🔄 Iteration {iteration} - improving based on feedback")
            if feedback_history:
                last_feedback = feedback_history[-1]
                print(f"      🔍 Critical Issues: {len(last_feedback.get('critical_issues', []))}")
                print(f"      🔧 Actionable Fixes: {len(last_feedback.get('actionable_fixes', []))}")

        print(f"      🤖 Using Model: {self.model}")
        print(f"      🌡️ Temperature: 0.2 (focused generation)")

    async def _demonstrate_critique_step(self, task: Dict, content: str, iteration: int):
        """Demonstrate the critique analysis step"""

        print("   🔍 Critique Analysis Process:")
        print(f"      📄 Analyzing {len(content)} characters of code")
        print(f"      📋 Checking {len(task['requirements'])} requirements")
        print("      🎯 Quality Metrics:")
        print("         - Code structure and organization")
        print("         - Requirement fulfillment")
        print("         - Error handling and robustness")
        print("         - Code quality and best practices")
        print("         - User interface design")
        print(f"      🤖 Using Model: {self.model}")
        print(f"      🌡️ Temperature: 0.1 (precise analysis)")

    def _show_code_preview(self, content: str):
        """Show a preview of the generated code"""

        lines = content.split('\n')
        preview_lines = min(15, len(lines))

        print(f"\n📝 CODE PREVIEW (first {preview_lines} lines):")
        print("   " + "─" * 50)

        for i in range(preview_lines):
            line_num = f"{i+1:2d}"
            line_content = lines[i][:60] + "..." if len(lines[i]) > 60 else lines[i]
            print(f"   {line_num}: {line_content}")

        if len(lines) > preview_lines:
            print(f"   ...: ({len(lines) - preview_lines} more lines)")
        print("   " + "─" * 50)

    def _show_critique_results(self, critique: Dict):
        """Show detailed critique results"""

        print(f"\n📊 CRITIQUE RESULTS:")
        print(f"   Quality Score: {critique.get('quality_score', 0)}/10")
        print(f"   Requirements Met: {critique.get('requirements_met', 0)}/{len(self.tasks[0]['requirements'])}")

        issues = critique.get('issues', [])
        if issues:
            print(f"   🔍 Issues Found ({len(issues)}):")
            for i, issue in enumerate(issues[:5], 1):
                print(f"      {i}. {issue}")
            if len(issues) > 5:
                print(f"      ... and {len(issues) - 5} more issues")
        else:
            print("   ✅ No critical issues found")

        suggestions = critique.get('suggestions', [])
        if suggestions:
            print(f"   💡 Suggestions ({len(suggestions)}):")
            for i, suggestion in enumerate(suggestions[:3], 1):
                print(f"      {i}. {suggestion}")
            if len(suggestions) > 3:
                print(f"      ... and {len(suggestions) - 3} more suggestions")

    def _process_critique_into_feedback(self, critique: Dict, iteration: int) -> Dict:
        """Process critique results into structured feedback"""

        issues = critique.get('issues', [])
        suggestions = critique.get('suggestions', [])

        # Categorize issues as critical vs. minor
        critical_issues = []
        minor_issues = []

        for issue in issues:
            if any(keyword in issue.lower() for keyword in ['error', 'missing', 'broken', 'fail', 'critical']):
                critical_issues.append(issue)
            else:
                minor_issues.append(issue)

        # Convert suggestions to actionable fixes
        actionable_fixes = []
        for suggestion in suggestions:
            if suggestion.startswith('Add') or suggestion.startswith('Implement') or suggestion.startswith('Fix'):
                actionable_fixes.append(suggestion)
            else:
                actionable_fixes.append(f"Consider: {suggestion}")

        return {
            'iteration': iteration,
            'quality_score': critique.get('quality_score', 0),
            'critical_issues': critical_issues,
            'minor_issues': minor_issues,
            'actionable_fixes': actionable_fixes,
            'requirements_met': critique.get('requirements_met', 0)
        }

    def _show_feedback_processing(self, feedback: Dict):
        """Show how feedback is processed and formatted"""

        print(f"   📋 Feedback Processing Results:")
        print(f"      Quality Score: {feedback['quality_score']}/10")
        print(f"      Requirements Met: {feedback['requirements_met']}")

        critical = feedback.get('critical_issues', [])
        if critical:
            print(f"      🚨 Critical Issues ({len(critical)}):")
            for issue in critical[:3]:
                print(f"         - {issue}")

        fixes = feedback.get('actionable_fixes', [])
        if fixes:
            print(f"      🔧 Actionable Fixes ({len(fixes)}):")
            for fix in fixes[:3]:
                print(f"         - {fix}")

        print(f"      📤 Feedback will be passed to next iteration")

    async def _test_ollama_connection(self) -> bool:
        """Test Ollama connection"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:11434/api/tags", timeout=5.0)
                return response.status_code == 200
        except:
            return False

    async def _generate_code_with_feedback(self, task: Dict, existing_content: str, iteration: int, feedback_history: List) -> str:
        """Generate code with feedback integration (simulating the real system)"""

        if iteration == 1:
            # First iteration - no feedback
            prompt = f"""You are an expert Python developer. Create a complete interactive GUI application for plotting.

Task: {task['description']}
Filename: {task['filename']}

Requirements:
{chr(10).join(f"- {req}" for req in task['requirements'])}

Create a complete, working Python application with:
- Proper imports (tkinter, matplotlib)
- Main GUI class with interactive controls
- Multiple plot types support
- Data input capabilities
- Real-time plot updates
- Clean, professional interface
- Error handling
- Example usage

Python code:"""
        else:
            # Subsequent iterations - include feedback
            last_feedback = feedback_history[-1] if feedback_history else {}

            feedback_text = ""
            if last_feedback.get('critical_issues'):
                feedback_text += "Critical Issues to Fix:\n"
                for issue in last_feedback['critical_issues']:
                    feedback_text += f"- {issue}\n"

            if last_feedback.get('actionable_fixes'):
                feedback_text += "\nActionable Fixes to Implement:\n"
                for fix in last_feedback['actionable_fixes']:
                    feedback_text += f"- {fix}\n"

            prompt = f"""You are an expert Python developer. Improve this interactive plotting GUI based on feedback.

Task: {task['description']}
Filename: {task['filename']}

Requirements:
{chr(10).join(f"- {req}" for req in task['requirements'])}

Current code:
```python
{existing_content}
```

Feedback from previous iteration:
{feedback_text}

**IMPORTANT**: Address ALL the feedback points above in your improved solution.

Please provide an improved version that fixes all issues and implements the suggested improvements.

Improved Python code:"""

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.2,
                            "top_p": 0.9,
                            "num_predict": 6000
                        }
                    },
                    timeout=300.0
                )

                if response.status_code == 200:
                    result = response.json()
                    generated_text = result.get("response", "")
                    return self._extract_code_from_response(generated_text)
                else:
                    return ""

        except Exception as e:
            self.logger.error(f"Code generation failed: {e}")
            return ""

    async def _critique_code_with_context(self, content: str, task: Dict, iteration: int) -> Dict[str, Any]:
        """Critique code with full context"""

        prompt = f"""You are an expert code reviewer specializing in GUI applications and data visualization.

Analyze this Python interactive plotting GUI application:

Task: {task['description']}
Filename: {task['filename']}
Iteration: {iteration}

Requirements to verify:
{chr(10).join(f"- {req}" for req in task['requirements'])}

Code to analyze:
```python
{content}
```

Provide detailed analysis in JSON format:
{{
    "quality_score": <number 1-10>,
    "requirements_met": <number of requirements satisfied 0-{len(task['requirements'])}>,
    "issues": ["specific issue 1", "specific issue 2"],
    "suggestions": ["specific improvement 1", "specific improvement 2"],
    "strengths": ["what works well 1", "what works well 2"],
    "gui_quality": <number 1-10>,
    "code_structure": <number 1-10>
}}

Focus on:
- GUI functionality and user experience
- Interactive plotting capabilities
- Code organization and structure
- Error handling and robustness
- Requirement fulfillment

Analysis:"""

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.1,
                            "top_p": 0.8,
                            "num_predict": 2048
                        }
                    },
                    timeout=180.0
                )

                if response.status_code == 200:
                    result = response.json()
                    critique_text = result.get("response", "")
                    return self._parse_critique_response(critique_text)
                else:
                    return {"quality_score": 5, "issues": ["Analysis failed"], "suggestions": []}

        except Exception as e:
            self.logger.error(f"Code critique failed: {e}")
            return {"quality_score": 5, "issues": ["Analysis failed"], "suggestions": []}

    def _extract_code_from_response(self, response: str) -> str:
        """Extract Python code from LLM response"""

        if "```python" in response:
            start = response.find("```python") + 9
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()

        elif "```" in response:
            start = response.find("```") + 3
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()

        # Look for Python code patterns
        lines = response.split('\n')
        code_lines = []
        in_code = False

        for line in lines:
            if line.strip().startswith(('import ', 'from ', 'class ', 'def ', '#')):
                in_code = True

            if in_code:
                code_lines.append(line)

        return '\n'.join(code_lines) if code_lines else response.strip()

    def _parse_critique_response(self, response: str) -> Dict[str, Any]:
        """Parse critique JSON response"""
        try:
            start = response.find('{')
            end = response.rfind('}') + 1

            if start != -1 and end > start:
                json_str = response[start:end]
                return json.loads(json_str)
        except:
            pass

        # Fallback parsing
        return {
            "quality_score": 6,
            "requirements_met": 4,
            "issues": ["Manual review needed"],
            "suggestions": ["Check implementation details"],
            "strengths": ["Basic structure present"],
            "gui_quality": 6,
            "code_structure": 6
        }

    def _show_final_results(self):
        """Show the final project results"""

        print("📊 FINAL PROJECT ANALYSIS:")
        print("=" * 40)

        for item in sorted(self.project_path.iterdir()):
            if item.is_file():
                size = item.stat().st_size
                print(f"📄 {item.name} ({size} bytes)")

                # Show preview of Python files
                if item.suffix == '.py':
                    try:
                        with open(item, 'r') as f:
                            content = f.read()

                        lines = content.split('\n')
                        print(f"   📊 Lines of code: {len(lines)}")

                        # Count key elements
                        imports = len([l for l in lines if l.strip().startswith(('import ', 'from '))])
                        classes = len([l for l in lines if l.strip().startswith('class ')])
                        functions = len([l for l in lines if l.strip().startswith('def ')])

                        print(f"   📦 Imports: {imports}")
                        print(f"   🏗️ Classes: {classes}")
                        print(f"   ⚙️ Functions: {functions}")

                        # Show first few lines
                        print("   📝 Preview:")
                        for i, line in enumerate(lines[:8], 1):
                            if line.strip():
                                print(f"      {i:2d}: {line[:60]}{'...' if len(line) > 60 else ''}")
                        print()

                    except Exception as e:
                        print(f"   ❌ Error reading file: {e}")


async def main():
    """Run the complete interactive plotting feedback demonstration"""

    print("🎯 INTERACTIVE PLOTTING GUI - COMPLETE FEEDBACK LOOP DEMO")
    print("=" * 70)
    print("This demonstrates the ENTIRE feedback process:")
    print("User Input → Plan Parsing → Code Generation → Critique → Feedback → Improvement")
    print("=" * 70)

    demo = InteractivePlottingFeedbackDemo()
    await demo.run_complete_feedback_demo()


if __name__ == "__main__":
    asyncio.run(main())
