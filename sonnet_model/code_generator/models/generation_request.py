"""
Generation Request Model
Defines the data structure for code generation requests
"""
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field


class ProgrammingLanguage(str, Enum):
    """Programming language enum"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    CSHARP = "csharp"
    CPP = "cpp"
    GO = "go"
    RUST = "rust"
    PHP = "php"
    RUBY = "ruby"
    SWIFT = "swift"
    KOTLIN = "kotlin"
    SHELL = "shell"
    SQL = "sql"
    HTML = "html"
    CSS = "css"
    OTHER = "other"


class Framework(str, Enum):
    """Framework enum"""
    # General frameworks
    NONE = "none"
    API = "api"
    
    # Python frameworks
    DJANGO = "django"
    FLASK = "flask"
    FASTAPI = "fastapi"
    PYTORCH = "pytorch"
    TENSORFLOW = "tensorflow"
    PANDAS = "pandas"
    NUMPY = "numpy"
    
    # JavaScript/TypeScript frameworks
    REACT = "react"
    ANGULAR = "angular"
    VUE = "vue"
    NEXT = "next"
    NODE = "node"
    EXPRESS = "express"
    
    # Other
    SPRING = "spring"
    DOTNET = "dotnet"
    LARAVEL = "laravel"
    RAILS = "rails"


class GenerationRequest(BaseModel):
    """
    Code generation request model
    
    Represents a request to generate code based on requirements
    """
    task_id: str = Field(..., description="Task ID")
    language: ProgrammingLanguage = Field(
        default=ProgrammingLanguage.PYTHON,
        description="Programming language"
    )
    framework: Optional[Framework] = Field(
        default=None,
        description="Framework to use"
    )
    description: str = Field(
        ...,
        description="Description of the code to generate"
    )
    requirements: List[str] = Field(
        default_factory=list,
        description="List of requirements for the code"
    )
    context: Optional[str] = Field(
        default=None,
        description="Additional context for code generation"
    )
    dependencies: List[str] = Field(
        default_factory=list,
        description="List of task dependencies"
    )
    examples: List[str] = Field(
        default_factory=list,
        description="List of example code snippets"
    )
    constraints: Dict[str, Any] = Field(
        default_factory=dict,
        description="Constraints for code generation"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata"
    )
    max_tokens: int = Field(
        default=4096,
        description="Maximum number of tokens to generate"
    )
    temperature: float = Field(
        default=0.7,
        description="Temperature for generation"
    )
    iteration: int = Field(
        default=1,
        description="Iteration number"
    )
    previous_code: Optional[str] = Field(
        default=None,
        description="Previous code from earlier iteration"
    )
    feedback: Optional[str] = Field(
        default=None,
        description="Feedback from previous iteration"
    )
    
    def get_constraint(self, key: str, default: Any = None) -> Any:
        """
        Get constraint value
        
        Args:
            key: Constraint key
            default: Default value if constraint not found
            
        Returns:
            Constraint value
        """
        return self.constraints.get(key, default)
    
    def has_framework(self) -> bool:
        """
        Check if framework is specified
        
        Returns:
            True if framework is specified, False otherwise
        """
        return self.framework is not None and self.framework != Framework.NONE
