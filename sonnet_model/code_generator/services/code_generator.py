"""
Code Generator Service - Main service for generating code using LLM
"""

import asyncio
import logging
import re
from typing import Dict, List, Optional, Any
from datetime import datetime

from shared.models import GenerationRequest, GenerationResult, ProgrammingLanguage
from ..llm_interface import create_llm_interface
from .prompt_builder import PromptBuilder
from .code_formatter import CodeFormatter


class CodeGenerator:
    """Main code generation service that orchestrates LLM calls and code processing"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Debug: Log the configuration being passed
        self.logger.info(f"CodeGenerator config keys: {list(config.keys())}")
        llm_config = config.get("llm", {})
        self.logger.info(f"LLM config keys: {list(llm_config.keys())}")
        self.logger.info(f"LLM type: {llm_config.get('type', 'NOT_SET')}")
        self.logger.info(f"LLM model: {llm_config.get('model', 'NOT_SET')}")

        # Initialize components
        self.llm_interface = self._create_llm_interface(llm_config)
        self.prompt_builder = PromptBuilder()
        self.code_formatter = CodeFormatter()
        
        # Configuration
        self.max_retries = config.get("max_retries", 3)
        self.default_temperature = config.get("default_temperature", 0.7)
        self.default_max_tokens = config.get("default_max_tokens", 4096)

        # Track initialization state
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the code generator and its components"""
        if not self._initialized:
            await self.llm_interface.initialize()
            self._initialized = True
            self.logger.info("CodeGenerator initialized successfully")

    async def shutdown(self) -> None:
        """Shutdown the code generator and its components"""
        if self._initialized:
            await self.llm_interface.shutdown()
            self._initialized = False
            self.logger.info("CodeGenerator shutdown successfully")

    async def generate_code(self, request: GenerationRequest) -> GenerationResult:
        """Generate code based on the request"""
        start_time = datetime.now()
        self.logger.info(f"Starting code generation for task {request.task_id}, iteration {request.iteration}")

        # Ensure generator is initialized
        if not self._initialized:
            await self.initialize()

        # Validate request
        validation_error = self._validate_request(request)
        if validation_error:
            return GenerationResult(
                task_id=request.task_id,
                code="",
                language=request.language.value,
                success=False,
                error_message=validation_error,
                generation_time=0.0,
                iteration=request.iteration
            )
        
        # Build prompt
        prompt = self.prompt_builder.build_generation_prompt(request)
        
        # Generate code with retries
        generation_result = None
        last_error = ""
        
        for attempt in range(self.max_retries):
            self.logger.info(f"Generation attempt {attempt + 1} for task {request.task_id}")
            
            result = await self._attempt_generation(prompt, request, attempt)
            if result["success"]:
                generation_result = result
                break
            else:
                last_error = result.get("error", "Unknown error")
                self.logger.warning(f"Generation attempt {attempt + 1} failed: {last_error}")
        
        if not generation_result:
            return GenerationResult(
                task_id=request.task_id,
                code="",
                language=request.language.value,
                success=False,
                error_message=f"All generation attempts failed. Last error: {last_error}",
                generation_time=(datetime.now() - start_time).total_seconds(),
                iteration=request.iteration
            )
        
        # Post-process the generated code
        processed_code = await self._post_process_code(
            generation_result["code"],
            request.language
        )
        
        # Extract metadata
        metadata = self._extract_metadata(generation_result["raw_response"], request)
        
        generation_time = (datetime.now() - start_time).total_seconds()
        
        return GenerationResult(
            task_id=request.task_id,
            code=processed_code,
            language=request.language.value,
            success=True,
            metadata=metadata,
            generation_time=generation_time,
            iteration=request.iteration,
            model_used=generation_result.get("model_name", "unknown"),
            tokens_used=generation_result.get("tokens_used", 0)
        )
    
    def _validate_request(self, request: GenerationRequest) -> Optional[str]:
        """Validate the generation request"""
        if not request.task_id:
            return "Task ID is required"
        
        if not request.description and not request.requirements:
            return "Either description or requirements must be provided"
        
        if request.max_tokens <= 0:
            return "max_tokens must be positive"
        
        if not (0.0 <= request.temperature <= 2.0):
            return "temperature must be between 0.0 and 2.0"
        
        return None
    
    async def _attempt_generation(self, prompt: str, request: GenerationRequest, attempt: int) -> Dict[str, Any]:
        """Attempt to generate code with the LLM"""
        # Adjust temperature for retries
        temperature = request.temperature + (attempt * 0.1)
        temperature = min(temperature, 1.0)
        
        response = await self.llm_interface.generate(
            prompt=prompt,
            max_tokens=request.max_tokens,
            temperature=temperature
        )
        
        if not response.get("success", False):
            return {
                "success": False,
                "error": response.get("error", "LLM generation failed")
            }
        
        # Extract code from response
        raw_text = response.get("text", "")
        extracted_code = self._extract_code_from_response(raw_text, request.language)
        
        if not extracted_code.strip():
            return {
                "success": False,
                "error": "No code found in LLM response"
            }
        
        return {
            "success": True,
            "code": extracted_code,
            "raw_response": raw_text,
            "model_name": response.get("model", "unknown"),
            "tokens_used": response.get("tokens_used", 0)
        }
    
    def _get_stop_sequences(self, language: ProgrammingLanguage) -> List[str]:
        """Get appropriate stop sequences for the language"""
        common_stops = ["```", "---", "# End", "// End"]
        
        language_specific = {
            ProgrammingLanguage.PYTHON: ["```python", "```py"],
            ProgrammingLanguage.JAVASCRIPT: ["```javascript", "```js"],
            ProgrammingLanguage.TYPESCRIPT: ["```typescript", "```ts"],
            ProgrammingLanguage.JAVA: ["```java"],
            ProgrammingLanguage.CPP: ["```cpp", "```c++"],
            ProgrammingLanguage.GO: ["```go"],
            ProgrammingLanguage.RUST: ["```rust"],
        }
        
        return common_stops + language_specific.get(language, [])
    
    def _extract_code_from_response(self, response_text: str, language: ProgrammingLanguage) -> str:
        """Extract code blocks from LLM response"""
        # Try to find code blocks with language specification
        lang_patterns = [
            language.value,
            language.value.lower(),
            language.value.upper()
        ]
        
        for lang in lang_patterns:
            pattern = rf"```{lang}\s*(.*?)```"
            matches = re.findall(pattern, response_text, re.DOTALL | re.IGNORECASE)
            if matches:
                return matches[0].strip()
        
        # Try generic code blocks
        generic_pattern = r"```\s*(.*?)```"
        matches = re.findall(generic_pattern, response_text, re.DOTALL)
        if matches:
            return matches[0].strip()
        
        # If no code blocks found, try to extract based on language-specific patterns
        if language == ProgrammingLanguage.PYTHON:
            # Look for Python-like code (indented blocks, def/class keywords)
            lines = response_text.split('\n')
            code_lines = []
            in_code = False
            
            for line in lines:
                if re.match(r'^\s*(def|class|import|from|if|for|while|with|async|@)', line):
                    in_code = True
                    code_lines.append(line)
                elif in_code and (line.strip() == '' or line.startswith('    ') or line.startswith('\t')):
                    code_lines.append(line)
                elif in_code and not line.strip():
                    code_lines.append(line)
                elif in_code:
                    break
            
            if code_lines:
                return '\n'.join(code_lines).strip()
        
        # Last resort: return the entire response if it looks like code
        if self._looks_like_code(response_text, language):
            return response_text.strip()
        
        return ""
    
    def _looks_like_code(self, text: str, language: ProgrammingLanguage) -> bool:
        """Heuristic to determine if text looks like code"""
        if language == ProgrammingLanguage.PYTHON:
            python_keywords = ['def', 'class', 'import', 'from', 'if', 'for', 'while', 'return']
            return any(keyword in text for keyword in python_keywords)
        
        # Add more language-specific heuristics as needed
        return False
    
    async def _post_process_code(self, code: str, language: ProgrammingLanguage) -> str:
        """Post-process the generated code"""
        if not code.strip():
            return code

        # Format the code (async call)
        formatted_code = await self.code_formatter.format_code(code, language.value)

        # Additional language-specific processing
        if language == ProgrammingLanguage.PYTHON:
            return self._post_process_python(formatted_code)

        return formatted_code
    
    def _post_process_python(self, code: str) -> str:
        """Python-specific post-processing"""
        lines = code.split('\n')
        processed_lines = []
        
        for line in lines:
            # Remove trailing whitespace
            line = line.rstrip()
            
            # Ensure proper indentation (4 spaces)
            if line.strip():
                leading_spaces = len(line) - len(line.lstrip())
                if leading_spaces > 0:
                    # Convert tabs to spaces and normalize indentation
                    content = line.lstrip()
                    indent_level = leading_spaces // 4
                    line = '    ' * indent_level + content
            
            processed_lines.append(line)
        
        # Ensure file ends with newline
        result = '\n'.join(processed_lines)
        if result and not result.endswith('\n'):
            result += '\n'
        
        return result
    
    def _extract_metadata(self, raw_response: str, request: GenerationRequest) -> Dict[str, Any]:
        """Extract metadata from the generation process"""
        metadata = {
            "language": request.language.value,
            "framework": request.framework.value if request.framework else None,
            "iteration": request.iteration,
            "has_examples": len(request.examples) > 0,
            "has_context": bool(request.context),
            "requirements_count": len(request.requirements),
            "response_length": len(raw_response),
        }
        
        # Extract code statistics
        if hasattr(self, '_last_generated_code'):
            code = self._last_generated_code
            metadata.update({
                "code_lines": len(code.split('\n')),
                "code_chars": len(code),
                "has_functions": 'def ' in code,
                "has_classes": 'class ' in code,
                "has_imports": any(line.strip().startswith(('import ', 'from ')) for line in code.split('\n'))
            })
        
        return metadata
    
    def _create_llm_interface(self, config: Dict[str, Any]):
        """Create LLM interface using factory"""
        return create_llm_interface(config)
