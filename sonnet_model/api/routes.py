"""
API Routes for the Agentic Code Development System
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, FastAPI, Request
from pydantic import BaseModel, Field

from system_integration import AgenticSystem
from shared.state_manager import get_state_manager

router = APIRouter(prefix="/api/v1")
logger = logging.getLogger(__name__)


class ProjectRequest(BaseModel):
    name: str = Field(..., description="Project name")
    description: str = Field(..., description="Project description/plan")
    user_input: str = Field(..., description="User's detailed requirements")
    language: str = Field(default="python", description="Programming language")
    debug_enabled: bool = Field(default=False, description="Enable debug mode for LLM visibility")


class ProjectResponse(BaseModel):
    project_id: str
    status: str
    message: str


class StatusResponse(BaseModel):
    system_state: str
    current_project: Optional[str]
    config: Dict[str, Any]


def get_system_instance(request: Request) -> AgenticSystem:
    """Get the system instance from app state"""
    system_instance = getattr(request.app.state, "agentic_system", None)
    if system_instance is None:
        raise HTTPException(
            status_code=500,
            detail="System not initialized. Please ensure the application is properly started."
        )
    return system_instance


def set_system_instance(app: FastAPI, system: AgenticSystem) -> None:
    """Set the system instance on the app state"""
    app.state.agentic_system = system


@router.post("/projects", response_model=ProjectResponse)
async def create_project(request: ProjectRequest, background_tasks: BackgroundTasks, system: AgenticSystem = Depends(get_system_instance)):
    """Create a new project and start processing"""

    # Check system state using shared state manager
    state_manager = get_state_manager()
    system_state = await state_manager.get_system_state()

    if system_state.get("state") != "IDLE":
        raise HTTPException(
            status_code=409,
            detail="System is currently processing another project"
        )

    # Acquire lock to prevent race conditions
    lock_acquired = await state_manager.acquire_lock(f"project:{request.name}", timeout=300)
    if not lock_acquired:
        raise HTTPException(
            status_code=409,
            detail="Another worker is already processing this project"
        )

    # Update system state to processing
    await state_manager.update_system_state({
        "state": "PROCESSING",
        "current_project_id": request.name
    })

    # Start processing in background
    background_tasks.add_task(
        process_project_background,
        system,
        request.user_input,
        request.name,
        request.debug_enabled
    )

    return ProjectResponse(
        project_id=request.name,
        status="processing",
        message="Project processing started"
    )


async def process_project_background(system: AgenticSystem, user_input: str, project_name: str, debug_enabled: bool = False):
    """Background task to process the project"""
    state_manager = get_state_manager()

    try:
        logger.info(f"🚀 Starting background processing for project: {project_name}")
        logger.info(f"📝 User input: {user_input}")
        logger.info(f"🔍 Debug enabled: {debug_enabled}")

        # Enable debug mode if requested
        if debug_enabled:
            system.enable_debug_mode(True)
            logger.info(f"🔍 Debug mode enabled for project: {project_name}")

        # Generate complete project using the proper workflow
        logger.info(f"🔧 Calling system.generate_complete_project...")
        result = await system.generate_complete_project(user_input)
        logger.info(f"✅ Project generation completed with result: {result}")

        # Store the result for later retrieval
        await state_manager.set_project_result(project_name, result)

        # Update project status to completed
        await state_manager.update_project_state(project_name, {
            "status": "completed",
            "completed_at": datetime.now().isoformat(),
            "success": result.get("success", False),
            "files_generated": result.get("statistics", {}).get("total_files", 0),
            "quality_score": result.get("statistics", {}).get("success_rate", 0.0)
        })

        logger.info(f"✅ Project {project_name} completed successfully with {result.get('statistics', {}).get('total_files', 0)} files")

    except Exception as e:
        # Store error result
        error_result = {
            "success": False,
            "error": str(e),
            "type": "processing_error"
        }
        await state_manager.set_project_result(project_name, error_result)
        await state_manager.update_project_state(project_name, {
            "status": "failed",
            "completed_at": datetime.now().isoformat(),
            "error": str(e)
        })
        logger.error(f"❌ Project {project_name} failed: {e}")
        import traceback
        logger.error(f"❌ Full traceback: {traceback.format_exc()}")

    finally:
        # Update system state to idle and release lock
        await state_manager.update_system_state({
            "state": "IDLE",
            "current_project_id": None
        })
        await state_manager.release_lock(f"project:{project_name}")
        logger.info(f"🏁 Background processing completed for project: {project_name}")


@router.post("/projects/generate")
async def generate_complete_project(
    request: Dict[str, str],
    system: AgenticSystem = Depends(get_system_instance)
) -> Dict[str, Any]:
    """Generate a complete project from high-level description"""
    try:
        project_description = request.get("description")
        if not project_description:
            raise HTTPException(status_code=400, detail="Project description is required")

        result = await system.generate_complete_project(project_description)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=StatusResponse)
async def get_system_status(system: AgenticSystem = Depends(get_system_instance)):
    """Get current system status"""
    state_manager = get_state_manager()
    system_state = await state_manager.get_system_state()

    return StatusResponse(
        system_state=system_state.get("state", "IDLE"),
        current_project=system_state.get("current_project_id"),
        config=system.config
    )


@router.get("/projects/{project_id}/status")
async def get_project_status(project_id: str, system: AgenticSystem = Depends(get_system_instance)):
    """Get status of a specific project"""

    state_manager = get_state_manager()
    system_state = await state_manager.get_system_state()
    project_state = await state_manager.get_project_state(project_id)

    if system_state.get("current_project_id") != project_id and project_state.get("status") == "created":
        raise HTTPException(status_code=404, detail="Project not found or not active")

    return {
        "project_id": project_id,
        "state": system_state.get("state", "IDLE"),
        "current_project": system_state.get("current_project_id"),
        "project_status": project_state.get("status", "unknown"),
        "completed_at": project_state.get("completed_at"),
        "success": project_state.get("success"),
        "files_generated": project_state.get("files_generated", 0),
        "quality_score": project_state.get("quality_score", 0.0)
    }


@router.get("/projects/{project_id}/results")
async def get_project_results(project_id: str, system: AgenticSystem = Depends(get_system_instance)):
    """Get the results of a completed project"""

    state_manager = get_state_manager()
    project_state = await state_manager.get_project_state(project_id)

    if project_state.get("status") not in ["completed", "failed"]:
        raise HTTPException(
            status_code=400,
            detail=f"Project is not completed yet. Current status: {project_state.get('status', 'unknown')}"
        )

    # Get the stored results
    result = await state_manager.get_project_result(project_id)

    if not result:
        raise HTTPException(status_code=404, detail="Project results not found")

    return {
        "project_id": project_id,
        "status": project_state.get("status"),
        "completed_at": project_state.get("completed_at"),
        "result": result
    }


@router.post("/projects/{project_id}/stop")
async def stop_project(project_id: str, system: AgenticSystem = Depends(get_system_instance)):
    """Stop processing a project"""

    state_manager = get_state_manager()
    system_state = await state_manager.get_system_state()

    if system_state.get("current_project_id") != project_id:
        raise HTTPException(status_code=404, detail="Project not found")

    if system_state.get("state") == "IDLE":
        raise HTTPException(status_code=400, detail="No active project to stop")

    # Reset system state and release lock
    await state_manager.update_system_state({
        "state": "IDLE",
        "current_project_id": None
    })
    await state_manager.release_lock(f"project:{project_id}")

    return {"message": "Project stopped successfully"}


@router.get("/health")
def health_check():
    return {"status": "ok"}


# Additional utility endpoints
@router.get("/config")
async def get_config(system: AgenticSystem = Depends(get_system_instance)):
    """Get current system configuration"""
    return system.config


# Configuration update endpoint removed due to multi-worker safety concerns
# In a multi-worker environment, updating config on one worker creates
# inconsistent state across workers. Use environment variables or restart
# the entire application to update configuration safely.
