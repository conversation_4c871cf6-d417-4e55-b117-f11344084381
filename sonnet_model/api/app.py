"""
FastAPI Application with Multi-Worker State Management

Initializes the application with proper state management for multi-worker environments
"""

import os
import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .routes import router, set_system_instance
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from system_integration import AgenticSystem
from shared.state_manager import init_state_manager
from utils.config_loader import load_config


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application
    
    Returns:
        Configured FastAPI application
    """
    # Load configuration
    config = load_config()
    
    # Initialize state manager
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
    use_redis = os.getenv("USE_REDIS", "true").lower() == "true"
    
    try:
        state_manager = init_state_manager(redis_url, use_redis)
        logging.info(f"State manager initialized: {'Redis' if use_redis else 'Memory'}")
    except Exception as e:
        logging.warning(f"Failed to initialize Redis, falling back to memory store: {e}")
        state_manager = init_state_manager(None, False)
    
    # Create FastAPI app
    app = FastAPI(
        title="Agentic Code Development System",
        description="Multi-worker compatible agentic system for code development",
        version="1.0.0"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Initialize system instance (stateless)
    system = AgenticSystem(config)
    set_system_instance(app, system)
    
    # Include routes
    app.include_router(router)
    
    @app.on_event("startup")
    async def startup_event():
        """Application startup event"""
        logging.info("🚀 Agentic System API starting up...")
        
        # Verify state manager health
        health = await state_manager.health_check()
        logging.info(f"State manager health: {health}")
        
        logging.info("✅ Agentic System API ready for multi-worker operation")
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Application shutdown event"""
        logging.info("🛑 Agentic System API shutting down...")
        
        # Clean up any resources if needed
        await state_manager.cleanup_expired_states()
        
        logging.info("✅ Agentic System API shutdown complete")
    
    return app


# Create the app instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Run the application
    uvicorn.run(
        "api.app:app",
        host="0.0.0.0",
        port=8000,
        workers=int(os.getenv("API_WORKERS", "1")),  # Single worker for development
        reload=os.getenv("RELOAD", "false").lower() == "true"
    )
