"""
Critique Engine - Main service for code critique and analysis
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from shared.models import CritiqueRequest, CritiqueResult, CodeIssue, IssueSeverity, IssueCategory
from ..analyzers.python_analyzer import Python<PERSON>naly<PERSON>
from .llm_critic import LLMCritic
from .static_analyzer import StaticAnalyzer


class CritiqueEngine:
    """Main critique engine service that orchestrates code analysis"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Debug callback for LLM visibility
        self.debug_callback = None
        
        # Initialize components
        self.static_analyzer = StaticAnalyzer(config.get("static_analyzer", {}))
        self.llm_critic = LLMCritic(config.get("llm_critic", {}))
        
        # Language-specific analyzers
        self.python_analyzer = PythonAnalyzer(config.get("python_analyzer", {}))
        
        # Configuration
        self.enable_llm = config.get("enable_llm", True)
        self.enable_static = config.get("enable_static", True)
        self.quality_threshold = config.get("quality_threshold", 0.7)

    def set_debug_callback(self, callback):
        """Set debug callback for LLM visibility"""
        self.debug_callback = callback
        if hasattr(self.llm_critic, 'set_debug_callback'):
            self.llm_critic.set_debug_callback(callback)

    async def critique_code(self, request: CritiqueRequest) -> CritiqueResult:
        """Analyze code and provide critique"""
        start_time = datetime.now()
        self.logger.info(f"Starting code critique for task {request.task_id}")
        
        # Validate request
        validation_error = self._validate_request(request)
        if validation_error:
            return CritiqueResult(
                task_id=request.task_id,
                request_id=request.request_id,
                issues=[],
                quality_score=0.0,
                meets_threshold=False,
                suggestions=[],
                analysis_time=0.0,
                iteration=request.iteration,
                success=False
            )
        
        # Determine language and select appropriate analyzer
        language = request.language.lower()
        issues = []
        
        # Run static analysis based on language
        if self.enable_static:
            if language == "python":
                python_issues = await self._analyze_python(request)
                issues.extend(python_issues)
            else:
                # Default static analysis for other languages
                for file in request.files:
                    static_issues = await self.static_analyzer.analyze_code(
                        file.content,
                        file.language.value,
                        request.categories
                    )
                    issues.extend(static_issues)
        
        # Run LLM-based critique and coaching - ALWAYS ENABLED for persistent coaching
        if True:  # Always run the coaching system
            llm_issues = await self.llm_critic.critique_code(request)
            issues.extend(llm_issues)
        
        # Calculate quality score
        quality_score = self._calculate_quality_score(issues)
        meets_threshold = quality_score >= self.quality_threshold
        
        # Generate suggestions
        suggestions = self._generate_suggestions(issues)
        
        # Create result
        analysis_time = (datetime.now() - start_time).total_seconds()
        
        result = CritiqueResult(
            task_id=request.task_id,
            request_id=request.request_id,
            success=True,
            issues=issues,
            quality_score=quality_score,
            meets_threshold=meets_threshold,
            suggestions=suggestions,
            analysis_time=analysis_time,
            iteration=request.iteration
        )
        
        self.logger.info(
            f"Completed critique for task {request.task_id}: "
            f"found {len(issues)} issues, quality score: {quality_score:.2f}"
        )
        
        return result
    
    async def _analyze_python(self, request: CritiqueRequest) -> List[CodeIssue]:
        """Run Python-specific analysis"""
        issues = []
        
        # Determine if we need to analyze for OpenCL
        analyze_opencl = "opencl" in request.categories
        
        # Run Python analyzer with error handling
        for file in request.files:
            if self._has_valid_syntax(file.content):
                python_issues = await self.python_analyzer.analyze_code(
                    file.content,
                    file.filename,
                    request.categories
                )
                issues.extend(python_issues)
        else:
            # Add syntax error issue using the helper function
            from critique_engine.analyzers.python_analyzer import create_code_issue
            syntax_issue = create_code_issue(
                title="Syntax Error",
                message="Invalid Python syntax detected",
                severity=IssueSeverity.HIGH,
                category=IssueCategory.SYNTAX,
                filename=f"{request.task_id}.py",
                line=1,
                column=1
            )
            issues.append(syntax_issue)

        return issues

    def _has_valid_syntax(self, code: str) -> bool:
        """Check if code has valid Python syntax"""
        if not code.strip():
            return True

        # Check for obvious invalid syntax patterns
        invalid_patterns = [
            "invalid python syntax",
            "!!!",
            "syntax error",
            "def def",
            "class class",
            "import import"
        ]

        code_lower = code.lower()
        for pattern in invalid_patterns:
            if pattern in code_lower:
                return False

        # Simple validation - check for basic syntax issues
        lines = code.splitlines()

        # Check for unclosed parentheses, brackets, braces
        paren_count = 0
        bracket_count = 0
        brace_count = 0

        for line in lines:
            # Skip comments and strings (basic check)
            in_string = False
            escape_next = False

            for char in line:
                if escape_next:
                    escape_next = False
                    continue

                if char == '\\':
                    escape_next = True
                    continue

                if char in ['"', "'"]:
                    in_string = not in_string
                    continue

                if in_string:
                    continue

                if char == '(':
                    paren_count += 1
                elif char == ')':
                    paren_count -= 1
                elif char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1
                elif char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1

        # Check if all brackets are balanced
        return paren_count == 0 and bracket_count == 0 and brace_count == 0
    
    def _validate_request(self, request: CritiqueRequest) -> Optional[str]:
        """Validate critique request"""
        if not request.files:
            return "No files provided"

        # Check if any file has content
        has_content = any(file.content.strip() for file in request.files)
        if not has_content:
            return "All files are empty"

        if not request.language:
            return "Language is required"
        
        return None
    
    def _calculate_quality_score(self, issues: List[CodeIssue]) -> float:
        """Calculate quality score based on issues"""
        if not issues:
            return 1.0  # Perfect score if no issues
        
        # Calculate weighted score based on issue severity
        severity_weights = {
            IssueSeverity.CRITICAL: 1.0,
            IssueSeverity.HIGH: 0.7,
            IssueSeverity.MEDIUM: 0.4,
            IssueSeverity.LOW: 0.2,
            IssueSeverity.INFO: 0.1
        }
        
        # Count issues by severity
        severity_counts = {severity: 0 for severity in IssueSeverity}
        for issue in issues:
            severity_counts[issue.severity] += 1
        
        # Calculate penalty
        total_penalty = 0.0
        for severity, count in severity_counts.items():
            weight = severity_weights.get(severity, 0.1)
            penalty = count * weight
            total_penalty += penalty
        
        # Normalize penalty (max reasonable penalty is 10.0)
        normalized_penalty = min(total_penalty / 10.0, 1.0)
        
        # Calculate score (1.0 - penalty)
        return max(0.0, 1.0 - normalized_penalty)
    
    def _generate_suggestions(self, issues: List[CodeIssue]) -> List[str]:
        """Generate improvement suggestions based on issues"""
        suggestions = []
        
        # Group issues by category
        category_issues = {}
        for issue in issues:
            if issue.category not in category_issues:
                category_issues[issue.category] = []
            category_issues[issue.category].append(issue)
        
        # Generate category-specific suggestions
        for category, category_issues_list in category_issues.items():
            if category == IssueCategory.SYNTAX:
                suggestions.append("Fix syntax errors before proceeding with other improvements")
            
            elif category == IssueCategory.SECURITY:
                high_sec_issues = [i for i in category_issues_list if i.severity in 
                                 (IssueSeverity.CRITICAL, IssueSeverity.HIGH)]
                if high_sec_issues:
                    suggestions.append(f"Address {len(high_sec_issues)} critical security issues")
            
            elif category == IssueCategory.PERFORMANCE:
                if len(category_issues_list) > 2:
                    suggestions.append("Consider optimizing code performance")
            
            elif category == IssueCategory.MAINTAINABILITY:
                if len(category_issues_list) > 3:
                    suggestions.append("Improve code maintainability and readability")
            
            elif category == IssueCategory.OPENCL:
                if category_issues_list:
                    suggestions.append("Fix OpenCL-specific issues for better GPU performance")
        
        # Add general suggestions based on severity distribution
        critical_issues = [i for i in issues if i.severity == IssueSeverity.CRITICAL]
        high_issues = [i for i in issues if i.severity == IssueSeverity.HIGH]
        
        if critical_issues:
            suggestions.insert(0, f"Fix {len(critical_issues)} critical issues immediately")
        
        if high_issues:
            suggestions.insert(1 if critical_issues else 0, 
                             f"Address {len(high_issues)} high-priority issues")
        
        return suggestions
    
    def get_config(self) -> Dict[str, Any]:
        """Get current configuration"""
        return {
            "enable_llm": self.enable_llm,
            "enable_static": self.enable_static,
            "quality_threshold": self.quality_threshold
        }
    
    def update_config(self, new_config: Dict[str, Any]) -> Dict[str, Any]:
        """Update configuration"""
        if "enable_llm" in new_config:
            self.enable_llm = bool(new_config["enable_llm"])
        
        if "enable_static" in new_config:
            self.enable_static = bool(new_config["enable_static"])
        
        if "quality_threshold" in new_config:
            threshold = float(new_config["quality_threshold"])
            self.quality_threshold = max(0.0, min(1.0, threshold))
        
        return self.get_config()
