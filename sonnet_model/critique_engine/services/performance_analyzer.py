"""
Performance Analyzer Service
Analyzes code for performance issues
"""
import os
import logging
import re
from typing import Dict, List, Any, Optional, Set

from critique_engine.models.critique_request import CritiqueRequest, CritiqueCategory
from critique_engine.models.critique_result import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CodeIssue, IssueSeverity
from critique_engine.models.code_issue import IssueCategory
from critique_engine.rules.performance_rules import PerformanceRules


class PerformanceAnalyzer:
    """
    Performance Analyzer Service
    
    Analyzes code for performance issues
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize Performance Analyzer
        
        Args:
            config: Performance analyzer configuration
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.performance_rules = PerformanceRules()
    
    async def analyze(self, request: CritiqueRequest) -> CritiqueResult:
        """
        Analyze code for performance issues
        
        Args:
            request: Critique request
            
        Returns:
            Critique result
        """
        self.logger.info(f"Starting performance analysis for task {request.task_id}")
        
        # Create result object
        result = CritiqueResult(
            task_id=request.task_id,
            request_id=request.request_id
        )
        
        # Skip if performance category not requested
        if not request.should_check_category(CritiqueCategory.PERFORMANCE):
            self.logger.info("Performance category not requested, skipping")
            return result
        
        # Analyze each file
        for file in request.files:
            # Get language-specific rules
            rules = self.performance_rules.get_rules_for_language(file.language.value)
            
            # Apply each rule
            for rule in rules:
                issues = rule.apply(file.filename, file.content)
                
                # Add issues to result
                for issue in issues:
                    result.add_issue(issue)
            
            # Apply common patterns
            issues = self._analyze_common_patterns(file.filename, file.content, file.language.value)
            
            # Add issues to result
            for issue in issues:
                result.add_issue(issue)
        
        self.logger.info(f"Performance analysis complete for task {request.task_id}, found {len(result.issues)} issues")
        
        return result
    
    def _analyze_common_patterns(
        self, 
        filename: str, 
        content: str, 
        language: str
    ) -> List[CodeIssue]:
        """
        Analyze for common performance patterns
        
        Args:
            filename: Filename
            content: File content
            language: Programming language
            
        Returns:
            List of performance issues
        """
        issues = []
        
        # Language-specific checks
        if language == "python":
            self._analyze_python_performance(filename, content, issues)
        elif language in ["javascript", "typescript"]:
            self._analyze_js_performance(filename, content, issues)
        
        return issues
    
    def _analyze_python_performance(
        self, 
        filename: str, 
        content: str, 
        issues: List[CodeIssue]
    ) -> None:
        """
        Analyze Python code for performance issues
        
        Args:
            filename: Filename
            content: File content
            issues: List to add issues to
        """
        # Check for inefficient list operations in loops
        list_in_loop_patterns = [
            (r"for .+ in .+:\s*\n\s+.+\.append\(", "List append in loop"),
            (r"for .+ in .+:\s*\n\s+.+\s*=\s*.+\s*\+\s*.+", "List concatenation in loop"),
        ]
        
        for pattern, issue_type in list_in_loop_patterns:
            for match in re.finditer(pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                line_content = content.split('\n')[line_num - 1].strip()
                
                issues.append(CodeIssue(
                    id=f"perf-{issue_type.lower().replace(' ', '-')}-{line_num}",
                    title=f"{issue_type} performance issue",
                    description=f"{issue_type} may cause performance issues",
                    file_path=filename,
                    line_start=line_num,
                    line_end=line_num + 1,
                    category=IssueCategory.PERFORMANCE,
                    severity=IssueSeverity.MEDIUM,
                    code_snippet=line_content,
                    fix_suggestions=["Consider using list comprehensions or generator expressions"]
                ))
        
        # Check for inefficient string concatenation
        string_concat_pattern = r"for .+:\s*\n\s+.+\s*\+=\s*['\"]"
        
        for match in re.finditer(string_concat_pattern, content):
            line_num = content[:match.start()].count('\n') + 1
            line_content = content.split('\n')[line_num - 1].strip()
            
            issues.append(CodeIssue(
                id=f"perf-string-concat-{line_num}",
                title="Inefficient string concatenation",
                description="Inefficient string concatenation in loop",
                file_path=filename,
                line_start=line_num,
                line_end=line_num + 1,
                category=IssueCategory.PERFORMANCE,
                severity=IssueSeverity.MEDIUM,
                code_snippet=line_content,
                fix_suggestions=["Use ''.join() or string formatting instead"]
            ))
        
        # Check for nested loops
        nested_loops_pattern = r"for .+:\s*\n\s+for .+:"
        
        for match in re.finditer(nested_loops_pattern, content):
            line_num = content[:match.start()].count('\n') + 1
            line_content = content.split('\n')[line_num - 1].strip()
            next_line = content.split('\n')[line_num].strip()
            
            issues.append(CodeIssue(
                id=f"perf-nested-loops-{line_num}",
                title="Nested loops performance issue",
                description="Nested loops may cause performance issues",
                file_path=filename,
                line_start=line_num,
                line_end=line_num + 1,
                category=IssueCategory.PERFORMANCE,
                severity=IssueSeverity.MEDIUM,
                code_snippet=f"{line_content}\n{next_line}",
                fix_suggestions=["Consider restructuring or using more efficient data structures"]
            ))
        
        # Check for repeated function calls in loops
        repeated_calls_pattern = r"for .+:\s*\n\s+.+\(.+\..+\(\)"
        
        for match in re.finditer(repeated_calls_pattern, content):
            line_num = content[:match.start()].count('\n') + 1
            line_content = content.split('\n')[line_num - 1].strip()
            next_line = content.split('\n')[line_num].strip()
            
            issues.append(CodeIssue(
                id=f"perf-repeated-calls-{line_num}",
                filename=filename,
                line_start=line_num,
                line_end=line_num + 1,
                category=CritiqueCategory.PERFORMANCE,
                severity=IssueSeverity.MEDIUM,
                message="Repeated function calls in loop",
                code_snippet=f"{line_content}\n{next_line}",
                suggestion="Cache function results outside the loop"
            ))
    
    def _analyze_js_performance(
        self, 
        filename: str, 
        content: str, 
        issues: List[CodeIssue]
    ) -> None:
        """
        Analyze JavaScript/TypeScript code for performance issues
        
        Args:
            filename: Filename
            content: File content
            issues: List to add issues to
        """
        # Check for DOM operations in loops
        dom_in_loop_patterns = [
            (r"for\s*\(.+\)\s*{\s*\n\s*document\.querySelector", "DOM query in loop"),
            (r"for\s*\(.+\)\s*{\s*\n\s*document\.getElementById", "DOM query in loop"),
            (r"for\s*\(.+\)\s*{\s*\n\s*document\.getElementsBy", "DOM query in loop"),
        ]
        
        for pattern, issue_type in dom_in_loop_patterns:
            for match in re.finditer(pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                line_content = content.split('\n')[line_num - 1].strip()
                next_line = content.split('\n')[line_num].strip()
                
                issues.append(CodeIssue(
                    id=f"perf-{issue_type.lower().replace(' ', '-')}-{line_num}",
                    filename=filename,
                    line_start=line_num,
                    line_end=line_num + 1,
                    category=CritiqueCategory.PERFORMANCE,
                    severity=IssueSeverity.MEDIUM,
                    message=f"{issue_type} may cause performance issues",
                    code_snippet=f"{line_content}\n{next_line}",
                    suggestion="Cache DOM elements outside the loop"
                ))
        
        # Check for array modifications in loops
        array_mod_patterns = [
            (r"for\s*\(.+\)\s*{\s*\n\s*.+\.push\(", "Array push in loop"),
            (r"for\s*\(.+\)\s*{\s*\n\s*.+\s*=\s*.+\.concat\(", "Array concat in loop"),
        ]
        
        for pattern, issue_type in array_mod_patterns:
            for match in re.finditer(pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                line_content = content.split('\n')[line_num - 1].strip()
                next_line = content.split('\n')[line_num].strip()
                
                issues.append(CodeIssue(
                    id=f"perf-{issue_type.lower().replace(' ', '-')}-{line_num}",
                    filename=filename,
                    line_start=line_num,
                    line_end=line_num + 1,
                    category=CritiqueCategory.PERFORMANCE,
                    severity=IssueSeverity.MEDIUM,
                    message=f"{issue_type} may cause performance issues",
                    code_snippet=f"{line_content}\n{next_line}",
                    suggestion="Consider using map, filter, or reduce"
                ))
        
        # Check for inefficient string concatenation
        string_concat_pattern = r"for\s*\(.+\)\s*{\s*\n\s*.+\s*\+=\s*['\"]"
        
        for match in re.finditer(string_concat_pattern, content):
            line_num = content[:match.start()].count('\n') + 1
            line_content = content.split('\n')[line_num - 1].strip()
            next_line = content.split('\n')[line_num].strip()
            
            issues.append(CodeIssue(
                id=f"perf-string-concat-{line_num}",
                filename=filename,
                line_start=line_num,
                line_end=line_num + 1,
                category=CritiqueCategory.PERFORMANCE,
                severity=IssueSeverity.MEDIUM,
                message="Inefficient string concatenation in loop",
                code_snippet=f"{line_content}\n{next_line}",
                suggestion="Use array.join() instead"
            ))
        
        # Check for nested loops
        nested_loops_pattern = r"for\s*\(.+\)\s*{\s*\n\s*for\s*\(.+\)\s*{"
        
        for match in re.finditer(nested_loops_pattern, content):
            line_num = content[:match.start()].count('\n') + 1
            line_content = content.split('\n')[line_num - 1].strip()
            next_line = content.split('\n')[line_num].strip()
            
            issues.append(CodeIssue(
                id=f"perf-nested-loops-{line_num}",
                filename=filename,
                line_start=line_num,
                line_end=line_num + 1,
                category=CritiqueCategory.PERFORMANCE,
                severity=IssueSeverity.MEDIUM,
                message="Nested loops may cause performance issues",
                code_snippet=f"{line_content}\n{next_line}",
                suggestion="Consider restructuring or using more efficient data structures"
            ))
