# filename: csv_reader.py
import pandas as pd
from typing import List, Dict


def read_csv(file_path: str) -> List[Dict]:
    """
    Reads a CSV file and returns its contents as a list of dictionaries.

    Args:
        file_path (str): The path to the CSV file.

    Returns:
        List[Dict]: A list where each element is a dictionary representing a row in the CSV file.

    Raises:
        FileNotFoundError: If the specified file does not exist.
        Exception: For any other errors that occur while reading the file.
    """
    try:
        df = pd.read_csv(file_path)
        return df.to_dict("records")
    except FileNotFoundError:
        raise FileNotFoundError(f"The file at {file_path} does not exist.")
    except Exception as e:
        raise Exception(f"An error occurred while reading the file: {e}")
