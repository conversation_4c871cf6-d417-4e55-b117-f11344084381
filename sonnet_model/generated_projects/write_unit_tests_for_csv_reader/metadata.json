{"task": {"id": "8ef8514a-0985-4339-ac78-5298b7fea7b2", "title": "Write Unit Tests for CSV Reader", "description": "Create unit tests to ensure that the Python script correctly reads a variety of CSV files and returns their contents as expected.", "requirements": ["Implement a Python function that reads a CSV file and returns its contents as a list of dictionaries.", "Write unit tests for the CSV reader function using Python's built-in unittest framework.", "Provide detailed installation instructions for setting up the project environment.", "Ensure the README includes a clear and concise description of the project.", "Convert the DataFrame to a list of dictionaries where each dictionary represents a row in the CSV.", "Include instructions on how to execute tests for the project.", "Handle errors gracefully for cases where the file does not exist or is corrupt.", "Use Pandas for reading the CSV file in one of the test cases.", "Document usage examples demonstrating how to run the function with sample CSV files.", "Implement a function named `read_csv` that takes a file path as an argument."], "language": "Python 3", "frameworks": ["<PERSON><PERSON>"]}, "quality_score": 9.5, "generated_at": "2025-07-04T15:23:25.636407", "language": "Python 3", "file": "generated_projects/write_unit_tests_for_csv_reader/main.txt"}