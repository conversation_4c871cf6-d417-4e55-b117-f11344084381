# filename: read_csv_with_error_handling.py
import pandas as pd
from typing import List, Dict


def read_csv(file_path: str) -> List[Dict]:
    """
    Reads a CSV file and returns the data as a list of dictionaries.

    Args:
        file_path (str): The path to the CSV file.

    Returns:
        List[Dict]: A list where each dictionary represents a row in the CSV file.

    Raises:
        FileNotFoundError: If the specified file does not exist.
        ValueError: If the file is empty or improperly formatted.
    """
    try:
        # Attempt to read the CSV file using Pandas
        df = pd.read_csv(file_path)

        # Convert DataFrame to list of dictionaries
        if len(df.columns) == 0:
            column_names = [f"column_{i+1}" for i in range(len(df.iloc[0]))]
            df.columns = column_names

        return df.to_dict(orient="records")

    except FileNotFoundError:
        raise FileNotFoundError(f"The file at {file_path} does not exist.")
    except pd.errors.EmptyDataError:
        raise ValueError("The CSV file is empty or improperly formatted.")
    except pd.errors.ParserError as e:
        raise ValueError(f"There was a problem parsing the CSV file: {e}")


# Example usage:
if __name__ == "__main__":
    try:
        data = read_csv("example.csv")
        print("CSV data successfully loaded:", data)
    except Exception as e:
        print("Error loading CSV data:", str(e))
