{"task": {"id": "35e477fb-7aa3-4e5a-aa6e-071747dff771", "title": "Create Python Script to Read CSV", "description": "Develop a robust Python script that reads a CSV file and returns its contents as a list of dictionaries using the Pandas library. The script should handle various edge cases such as empty files or files with missing headers.", "requirements": ["Implement a Python function that reads a CSV file and returns its contents as a list of dictionaries.", "Write unit tests for the CSV reader function using Python's built-in unittest framework.", "Provide detailed installation instructions for setting up the project environment.", "Ensure the README includes a clear and concise description of the project.", "Convert the DataFrame to a list of dictionaries where each dictionary represents a row in the CSV.", "Include instructions on how to execute tests for the project.", "Handle errors gracefully for cases where the file does not exist or is corrupt.", "Use Pandas for reading the CSV file in one of the test cases.", "Document usage examples demonstrating how to run the function with sample CSV files.", "Implement a function named `read_csv` that takes a file path as an argument."], "language": "Python 3", "frameworks": ["<PERSON><PERSON>"]}, "quality_score": 8.5, "generated_at": "2025-07-04T15:22:41.130101", "language": "Python 3", "file": "generated_projects/create_python_script_to_read_csv/main.txt"}