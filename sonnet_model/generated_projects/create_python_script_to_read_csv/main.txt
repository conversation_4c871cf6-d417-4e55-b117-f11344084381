# filename: csv_reader.py
"""Module to read a CSV file and return its contents as a list of dictionaries."""
import pandas as pd
from typing import List, Dict


def read_csv(file_path: str) -> List[Dict]:
    """Reads a CSV file at the given path and returns its contents as a list of dictionaries.

    Args:
        file_path (str): The path to the CSV file.

    Returns:
        List[Dict]: A list where each element is a dictionary representing a row in the CSV file.
    """
    try:
        df = pd.read_csv(file_path)
        return df.to_dict("records")
    except FileNotFoundError as e:
        print(f"File not found: {e}")
        raise SystemExit("Please check the file path and try again.") from e
    except pd.errors.EmptyDataError as e:
        print("The CSV file is empty.")
        return []
    except Exception as e:
        print(f"An error occurred while reading the CSV file: {e}")
        raise SystemExit("Please check the file format and try again.") from e


# filename: test_csv_reader.py
import unittest
from csv_reader import read_csv


class TestCSVReader(unittest.TestCase):
    def test_read_valid_file(self):
        data = read_csv("tests/test_data.csv")
        self.assertEqual(len(data), 3)
        self.assertIsInstance(data, list)
        self.assertIsInstance(data[0], dict)

    def test_read_empty_file(self):
        data = read_csv("tests/empty.csv")
        self.assertEqual(len(data), 0)

    def test_read_missing_file(self):
        with self.assertRaises(SystemExit):
            read_csv("nonexistent.csv")

    def test_read_corrupt_file(self):
        with self.assertRaises(SystemExit):
            read_csv("tests/corrupt.csv")


# filename: setup.py
from setuptools import setup, find_packages

setup(
    name="csv_reader",
    version="0.1",
    packages=find_packages(),
    install_requires=["pandas"],
)
