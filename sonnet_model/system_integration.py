"""
Agentic System Integration

Main orchestrator that coordinates all system components with persistent coaching
"""

import logging
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

from task_manager.services.orchestrator import TaskOrchestrator
from critique_engine.services.critique_engine import CritiqueEngine
from code_generator.services.code_generator import CodeGenerator
from shared.state_manager import get_state_manager


class AgenticSystem:
    """
    Main system orchestrator with persistent coaching and state management
    
    This system ensures continuous progress by:
    - Maintaining conversation state across sessions
    - Providing persistent coaching to overcome obstacles
    - Handling errors gracefully with recovery strategies
    - Keeping momentum high through encouragement and guidance
    """
    
    def __init__(self, config: Dict[str, Any], debug_enabled: bool = False):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialize core components with enhanced capabilities
        self.orchestrator = TaskOrchestrator(config.get("task_manager", {}))
        self.critique_engine = CritiqueEngine(config.get("critique_engine", {}))
        self.code_generator = CodeGenerator(config.get("code_generator", {}))

        # Configuration (stateless)
        self.coaching_enabled = config.get("coaching_enabled", True)
        self.auto_recovery = config.get("auto_recovery", True)

        self.logger.info("Agentic System initialized with persistent coaching (stateless mode)")

        # LLM visibility setup - can be overridden by parameter or config
        self.debug_enabled = debug_enabled or config.get("system", {}).get("debug_enabled", False)
        self.debug_dir = None

    def _create_debug_directory(self, project_name: str) -> Path:
        """Create debug directory for LLM visibility"""
        if not self.debug_enabled:
            return None

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        debug_dir = Path("debug") / f"llm_visibility_{project_name}_{timestamp}"
        debug_dir.mkdir(parents=True, exist_ok=True)

        self.debug_dir = debug_dir
        self.logger.info(f"🔍 Debug directory created: {debug_dir}")
        return debug_dir

    def enable_debug_mode(self, enabled: bool = True) -> None:
        """Enable or disable debug mode for LLM visibility"""
        self.debug_enabled = enabled
        if enabled:
            self.logger.info("🔍 Debug mode enabled - LLM interactions will be saved to debug folder")
        else:
            self.logger.info("🔍 Debug mode disabled")

    def _save_debug_file(self, filename: str, content: Any) -> None:
        """Save debug content to file"""
        if not self.debug_dir:
            return

        file_path = self.debug_dir / filename
        if isinstance(content, (dict, list)):
            with open(file_path, "w") as f:
                json.dump(content, f, indent=2, default=str)
        else:
            with open(file_path, "w") as f:
                f.write(str(content))

        self.logger.info(f"📄 Debug file saved: {filename}")

    async def initialize_session(self, session_id: str = None, system_id: str = "default") -> Dict[str, Any]:
        """
        Initialize system session with state persistence and coaching

        Args:
            session_id: Optional session ID to resume
            system_id: System identifier for multi-worker consistency

        Returns:
            Session initialization result with coaching context
        """
        self.logger.info("🚀 Initializing Agentic System session...")

        # Get shared state manager
        state_manager = get_state_manager()

        # Initialize orchestrator session
        session_result = await self.orchestrator.initialize_session(session_id)
        current_session_id = session_result["session_id"]

        # Update shared system state
        await state_manager.update_system_state({
            "current_session_id": current_session_id,
            "is_initialized": True,
            "state": "ACTIVE"
        }, system_id)

        # Log session start
        if session_result["resumed"]:
            self.logger.info(f"✅ Session {current_session_id} resumed successfully")
        else:
            self.logger.info(f"✅ New session {current_session_id} started")

        return {
            "system": "agentic_code_development",
            "session_id": current_session_id,
            "initialized": True,
            "coaching_enabled": self.coaching_enabled,
            **session_result
        }
    
    async def generate_complete_project(self, project_description: str) -> Dict[str, Any]:
        """Generate a complete project from high-level description"""

        self.logger.info(f"Generating complete project: {project_description}")

        # Import the working project generator
        from working_project_generator import WorkingProjectGenerator

        generator = WorkingProjectGenerator()
        result = await generator.create_project_from_description(project_description)

        return {
            "type": "project_generation",
            "success": result.get('success_rate', 0) >= 50,
            "project_name": result.get('project_name'),
            "project_path": result.get('project_path'),
            "statistics": {
                "total_files": result.get('total_files', 0),
                "successful_files": result.get('successful_files', 0),
                "success_rate": result.get('success_rate', 0),
                "total_iterations": result.get('total_iterations', 0)
            },
            "files": result.get('files', [])
        }

    async def process_request(self, user_input: str, context: Dict[str, Any] = None, system_id: str = "default") -> Dict[str, Any]:
        """
        Process user request with full system coordination and coaching

        Args:
            user_input: User's request or instruction
            context: Additional context for processing
            system_id: System identifier for multi-worker consistency

        Returns:
            Comprehensive response with coaching and next steps
        """
        # Create debug directory for LLM visibility
        project_name = context.get("project_name", "unknown_project") if context else "unknown_project"
        debug_dir = self._create_debug_directory(project_name)

        # Save initial request
        self._save_debug_file("user_request.json", {
            "user_input": user_input,
            "context": context,
            "timestamp": datetime.now().isoformat(),
            "system_id": system_id
        })

        # Get shared state manager
        state_manager = get_state_manager()
        system_state = await state_manager.get_system_state(system_id)

        # Initialize session if not already done
        if not system_state.get("is_initialized", False):
            await self.initialize_session(system_id=system_id)

        self.logger.info(f"🎯 Processing request: {user_input[:100]}...")
        print(f"🔍 LLM VISIBILITY ENABLED")
        print(f"📁 Debug directory: {debug_dir}")
        print(f"🎯 TASK: {user_input}")

        # Set debug callback for LLM visibility
        if self.debug_enabled:
            self.orchestrator.set_debug_callback(self._save_debug_file)
            self.code_generator.set_debug_callback(self._save_debug_file)
            self.critique_engine.set_debug_callback(self._save_debug_file)

        # Process through orchestrator first for coaching and state management
        orchestrator_result = await self.orchestrator.process_user_request(user_input, context)

        # Handle coaching responses immediately
        if orchestrator_result.get("type") == "coaching_response":
            return {
                "system_response": orchestrator_result,
                "coaching_active": True,
                "next_action": "continue_with_confidence",
                "message": "💪 Keep going! You've got this!"
            }

        # Process based on request type
        if orchestrator_result.get("type") == "plan_created":
            return await self._handle_plan_creation(orchestrator_result, user_input, context)
        elif orchestrator_result.get("type") == "task_created":
            return await self._handle_task_creation(orchestrator_result, user_input, context)
        elif orchestrator_result.get("type") == "error_recovery":
            return await self._handle_error_recovery(orchestrator_result, user_input, context)
        elif orchestrator_result.get("type") == "status_report":
            return await self._handle_status_request(orchestrator_result, user_input, context)
        else:
            return await self._handle_general_request(orchestrator_result, user_input, context)
    
    async def _handle_plan_creation(self, orchestrator_result: Dict, user_input: str, _context: Dict) -> Dict[str, Any]:
        """Handle plan creation with code generation and critique"""
        plan = orchestrator_result["plan"]
        
        # Generate initial code structure if applicable
        if self._requires_code_generation(user_input):
            code_result = await self.code_generator.generate_code({
                "description": user_input,
                "plan_context": plan,
                "language": self._detect_language(user_input),
                "framework": self._detect_framework(user_input)
            })
            
            # Critique the generated code
            critique_result = await self.critique_engine.analyze_code({
                "code": code_result.get("code", ""),
                "language": code_result.get("language", "python"),
                "context": {"plan": plan, "user_input": user_input}
            })
            
            return {
                "type": "plan_with_code",
                "plan": plan,
                "code_generated": code_result,
                "critique": critique_result,
                "coaching_message": orchestrator_result["coaching_message"],
                "next_steps": [
                    "Review the generated code structure",
                    "Address any critique feedback",
                    "Begin implementing the first step"
                ],
                "momentum": "high"
            }
        
        return {
            "type": "plan_created",
            "plan": plan,
            "coaching_message": orchestrator_result["coaching_message"],
            "next_action": orchestrator_result["next_action"],
            "momentum": "high"
        }
    
    async def _handle_task_creation(self, orchestrator_result: Dict, user_input: str, _context: Dict) -> Dict[str, Any]:
        """Handle task creation with immediate execution support"""
        task = orchestrator_result["task"]

        # If this is a code-related task, prepare for execution
        if self._is_code_task(user_input):
            # Generate code for the task
            code_result = await self.code_generator.generate_code({
                "description": task.get("description", user_input),
                "task_context": task,
                "language": self._detect_language(user_input),
                "framework": self._detect_framework(user_input)
            })

            # Critique the code
            critique_result = await self.critique_engine.analyze_code({
                "code": code_result.get("code", ""),
                "language": code_result.get("language", "python"),
                "context": {"task": task, "user_input": user_input}
            })

            # Execute the task if it has an ID
            execution_result = None
            if task.get("id"):
                try:
                    execution_result = await self.orchestrator.execute_task(task["id"])
                except Exception as e:
                    self.logger.warning(f"Task execution failed: {e}")
                    execution_result = {"success": False, "error": str(e)}

            return {
                "type": "task_executed",
                "task": task,
                "code_generated": code_result,
                "critique": critique_result,
                "execution": execution_result,
                "coaching_messages": [
                    orchestrator_result.get("coaching_message", ""),
                    execution_result.get("coaching_messages", []) if execution_result else []
                ],
                "status": "completed",
                "momentum": "high"
            }

        return {
            "type": "task_created",
            "task": task,
            "coaching_message": orchestrator_result.get("coaching_message", ""),
            "next_action": orchestrator_result.get("next_action", ""),
            "momentum": "high"
        }
    
    async def _handle_error_recovery(self, orchestrator_result: Dict, user_input: str, context: Dict) -> Dict[str, Any]:
        """Handle error recovery with comprehensive support"""
        # Analyze the error for better recovery
        error_analysis = await self.critique_engine.analyze_error({
            "error_description": user_input,
            "context": context or {},
            "recovery_actions": orchestrator_result["recovery_actions"]
        })
        
        # Generate recovery code if applicable
        if self._requires_code_fix(user_input):
            fix_result = await self.code_generator.generate_fix({
                "error_description": user_input,
                "error_analysis": error_analysis,
                "context": context or {}
            })
            
            return {
                "type": "error_recovery_with_fix",
                "coaching_message": orchestrator_result["coaching_message"],
                "error_analysis": error_analysis,
                "code_fix": fix_result,
                "recovery_actions": orchestrator_result["recovery_actions"],
                "auto_retry": orchestrator_result["auto_retry"],
                "momentum": "recovering"
            }
        
        return {
            "type": "error_recovery",
            "coaching_message": orchestrator_result["coaching_message"],
            "error_analysis": error_analysis,
            "recovery_actions": orchestrator_result["recovery_actions"],
            "momentum": "recovering"
        }
    
    async def _handle_status_request(self, orchestrator_result: Dict, _user_input: str, _context: Dict) -> Dict[str, Any]:
        """Handle status requests with comprehensive reporting"""
        return {
            "type": "comprehensive_status",
            "state_summary": orchestrator_result["state_summary"],
            "resumption_context": orchestrator_result["resumption_context"],
            "coaching_message": orchestrator_result["coaching_message"],
            "system_health": await self._get_system_health(),
            "momentum_level": orchestrator_result["momentum_level"],
            "recommendations": await self._get_recommendations()
        }
    
    async def _handle_general_request(self, orchestrator_result: Dict, user_input: str, context: Dict) -> Dict[str, Any]:
        """Handle general requests with full system support"""
        # Determine if this requires code generation
        if self._requires_code_generation(user_input):
            from shared.models import GenerationRequest, ProgrammingLanguage

            # Create proper GenerationRequest object
            generation_request = GenerationRequest(
                task_id=f"task_{context.get('project_name', 'default')}",
                description=user_input,
                language=ProgrammingLanguage(self._detect_language(user_input)),
                requirements=[user_input],
                context=str(context or {}),
                iteration=1
            )

            code_result = await self.code_generator.generate_code(generation_request)
            
            # Critique the generated code
            from shared.models import CritiqueRequest

            critique_request = CritiqueRequest(
                task_id=generation_request.task_id,
                code=code_result.code,
                language=code_result.language,
                categories=["quality", "security", "performance"]
            )

            critique_result = await self.critique_engine.critique_code(critique_request)

            # Save generated code to files
            project_path = await self._save_generated_code(code_result, context)

            return {
                "type": "general_with_code",
                "success": True,
                "coaching_message": orchestrator_result["coaching_message"],
                "code_generated": code_result,
                "critique": critique_result,
                "suggested_actions": orchestrator_result["suggested_actions"],
                "momentum": "high",
                "project_path": str(project_path),
                "files": [{"filename": f"{generation_request.task_id}.py", "content": code_result.code}],
                "quality_score": critique_result.quality_score if hasattr(critique_result, 'quality_score') else 0.0
            }
        
        return {
            "type": "general_response",
            "coaching_message": orchestrator_result["coaching_message"],
            "suggested_actions": orchestrator_result["suggested_actions"],
            "momentum": "maintained"
        }

    async def _save_generated_code(self, code_result, context: Dict):
        """Save generated code to project folder"""
        from pathlib import Path
        from datetime import datetime
        import shutil

        # Create project directory
        project_name = context.get('project_name', 'generated_project')
        project_path = Path(project_name)

        # Remove existing project if it exists
        if project_path.exists():
            shutil.rmtree(project_path)

        # Create new project directory
        project_path.mkdir(parents=True, exist_ok=True)

        # Save the main code file
        main_file = project_path / f"{project_name}.py"
        with open(main_file, 'w') as f:
            f.write(code_result.code)

        # Create a simple README
        readme_content = f"""# {project_name.title().replace('_', ' ')}

Generated by Sonnet Model System

## Usage

```bash
python {project_name}.py
```

## Generated Code

The main application is in `{project_name}.py`.

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        readme_file = project_path / "README.md"
        with open(readme_file, 'w') as f:
            f.write(readme_content)

        self.logger.info(f"✅ Generated code saved to: {project_path}")
        return project_path
    
    def _requires_code_generation(self, user_input: str) -> bool:
        """Determine if the request requires code generation"""
        code_keywords = [
            "create", "implement", "build", "develop", "code", "function",
            "class", "module", "script", "program", "application", "api"
        ]
        return any(keyword in user_input.lower() for keyword in code_keywords)
    
    def _is_code_task(self, user_input: str) -> bool:
        """Determine if this is a code-related task"""
        return self._requires_code_generation(user_input)
    
    def _requires_code_fix(self, user_input: str) -> bool:
        """Determine if the error requires a code fix"""
        fix_keywords = ["error", "bug", "fix", "broken", "not working", "exception"]
        return any(keyword in user_input.lower() for keyword in fix_keywords)
    
    def _detect_language(self, user_input: str) -> str:
        """Detect programming language from user input"""
        if "python" in user_input.lower():
            return "python"
        elif "javascript" in user_input.lower() or "js" in user_input.lower():
            return "javascript"
        elif "java" in user_input.lower():
            return "java"
        elif "c++" in user_input.lower() or "cpp" in user_input.lower():
            return "cpp"
        else:
            return "python"  # Default
    
    def _detect_framework(self, user_input: str) -> str:
        """Detect framework from user input"""
        if "flask" in user_input.lower():
            return "flask"
        elif "django" in user_input.lower():
            return "django"
        elif "react" in user_input.lower():
            return "react"
        elif "vue" in user_input.lower():
            return "vue"
        else:
            return "none"
    
    async def _get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        return {
            "orchestrator": "healthy",
            "critique_engine": "healthy",
            "code_generator": "healthy",
            "state_persistence": "active",
            "coaching": "active" if self.coaching_enabled else "disabled",
            "auto_recovery": "enabled" if self.auto_recovery else "disabled"
        }
    
    async def _get_recommendations(self) -> List[str]:
        """Get system recommendations for next actions"""
        return [
            "Continue with current momentum",
            "Break down complex tasks into smaller steps",
            "Leverage the coaching system for guidance",
            "Use state persistence to maintain progress"
        ]
    
    async def force_momentum_boost(self, reason: str = "user_request") -> Dict[str, Any]:
        """Force a system-wide momentum boost"""
        boost_result = await self.orchestrator.force_momentum_boost(reason)
        
        return {
            "system_boost": True,
            "orchestrator_boost": boost_result,
            "message": "🚀 System momentum boosted! Ready to tackle any challenge!",
            "energy_level": "maximum"
        }
    
    async def get_coaching_summary(self) -> Dict[str, Any]:
        """Get comprehensive coaching summary"""
        orchestrator_summary = await self.orchestrator.get_coaching_summary()
        
        return {
            "orchestrator_coaching": orchestrator_summary,
            "system_coaching": {
                "total_sessions": 1,
                "coaching_enabled": self.coaching_enabled,
                "auto_recovery": self.auto_recovery,
                "current_momentum": "high"
            }
        }
    
    async def shutdown_session(self) -> Dict[str, Any]:
        """Gracefully shutdown the system session"""
        self.logger.info("🛑 Shutting down Agentic System session...")
        
        # Shutdown orchestrator
        shutdown_result = await self.orchestrator.shutdown_session()
        
        # System is stateless - no instance state to reset
        
        self.logger.info("✅ System session shutdown complete")
        
        return {
            "system_shutdown": True,
            "orchestrator_shutdown": shutdown_result,
            "message": "👋 Session ended successfully. All progress saved for future resumption!",
            "can_resume": True
        }
