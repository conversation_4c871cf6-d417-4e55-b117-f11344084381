"""
Shared State Manager for Multi-Worker Environment

Provides thread-safe, multi-worker compatible state management using Redis
"""

import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta


class StateManager:
    """
    Shared state manager for multi-worker environments
    
    Uses Redis for persistent, shared state across multiple worker processes.
    Falls back to in-memory storage for development/testing.
    """
    
    def __init__(self, redis_url: str = None, use_redis: bool = True):
        """
        Initialize state manager
        
        Args:
            redis_url: Redis connection URL (e.g., redis://localhost:6379)
            use_redis: Whether to use Redis or fall back to in-memory storage
        """
        self.logger = logging.getLogger(__name__)
        self.use_redis = use_redis and redis_url
        
        if self.use_redis:
            self._init_redis(redis_url)
        else:
            self._init_memory_store()
            self.logger.warning("Using in-memory state store - not suitable for production multi-worker setup")
    
    def _init_redis(self, redis_url: str):
        """Initialize Redis connection"""
        import redis
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.logger.info(f"Connected to Redis state store: {redis_url}")
    
    def _init_memory_store(self):
        """Initialize in-memory store for development"""
        self._memory_store = {}
        self.redis_client = None
    
    async def get_system_state(self, system_id: str = "default") -> Dict[str, Any]:
        """
        Get current system state
        
        Args:
            system_id: Unique system identifier
            
        Returns:
            Current system state dictionary
        """
        key = f"system_state:{system_id}"
        
        default_state = {
            "state": "IDLE",
            "current_project_id": None,
            "current_session_id": None,
            "is_initialized": False,
            "last_updated": datetime.now().isoformat(),
            "worker_id": None
        }

        if self.use_redis:
            state_json = self.redis_client.get(key)
            if state_json:
                return json.loads(state_json)
        else:
            stored_state = self._memory_store.get(key)
            if stored_state:
                return stored_state

        # Return default state if none exists
        return default_state
    
    async def set_system_state(self, state: Dict[str, Any], system_id: str = "default", ttl: int = 3600):
        """
        Set system state
        
        Args:
            state: State dictionary to store
            system_id: Unique system identifier
            ttl: Time to live in seconds (Redis only)
        """
        key = f"system_state:{system_id}"
        state["last_updated"] = datetime.now().isoformat()
        
        if self.use_redis:
            self.redis_client.setex(key, ttl, json.dumps(state))
        else:
            self._memory_store[key] = state
    
    async def update_system_state(self, updates: Dict[str, Any], system_id: str = "default"):
        """
        Update specific fields in system state
        
        Args:
            updates: Dictionary of fields to update
            system_id: Unique system identifier
        """
        current_state = await self.get_system_state(system_id)
        current_state.update(updates)
        await self.set_system_state(current_state, system_id)
    
    async def get_project_state(self, project_id: str) -> Dict[str, Any]:
        """
        Get project-specific state
        
        Args:
            project_id: Project identifier
            
        Returns:
            Project state dictionary
        """
        key = f"project_state:{project_id}"
        
        if self.use_redis:
            state_json = self.redis_client.get(key)
            if state_json:
                return json.loads(state_json)
        else:
            return self._memory_store.get(key, {})
        
        return {
            "project_id": project_id,
            "status": "created",
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "progress": {},
            "metadata": {}
        }
    
    async def set_project_state(self, project_id: str, state: Dict[str, Any], ttl: int = 86400):
        """
        Set project state
        
        Args:
            project_id: Project identifier
            state: State dictionary to store
            ttl: Time to live in seconds (Redis only)
        """
        key = f"project_state:{project_id}"
        state["last_updated"] = datetime.now().isoformat()
        
        if self.use_redis:
            self.redis_client.setex(key, ttl, json.dumps(state))
        else:
            self._memory_store[key] = state

    async def update_project_state(self, project_id: str, updates: Dict[str, Any]):
        """
        Update project state with new values

        Args:
            project_id: Project identifier
            updates: Dictionary of updates to apply
        """
        current_state = await self.get_project_state(project_id)
        current_state.update(updates)
        await self.set_project_state(project_id, current_state)

    async def set_project_result(self, project_id: str, result: Dict[str, Any], ttl: int = 86400):
        """
        Store project result for later retrieval

        Args:
            project_id: Project identifier
            result: Project result data
            ttl: Time to live in seconds
        """
        key = f"project_result:{project_id}"

        if self.use_redis:
            self.redis_client.setex(key, ttl, json.dumps(result))
        else:
            self._memory_store[key] = result

    async def get_project_result(self, project_id: str) -> Optional[Dict[str, Any]]:
        """
        Get stored project result

        Args:
            project_id: Project identifier

        Returns:
            Project result data or None if not found
        """
        key = f"project_result:{project_id}"

        if self.use_redis:
            result = self.redis_client.get(key)
            return json.loads(result) if result else None
        else:
            return self._memory_store.get(key)
    
    async def acquire_lock(self, lock_name: str, timeout: int = 30) -> bool:
        """
        Acquire a distributed lock
        
        Args:
            lock_name: Name of the lock
            timeout: Lock timeout in seconds
            
        Returns:
            True if lock acquired, False otherwise
        """
        if self.use_redis:
            lock_key = f"lock:{lock_name}"
            result = self.redis_client.set(lock_key, "locked", nx=True, ex=timeout)
            return result is not None
        else:
            # Simple in-memory lock for development
            lock_key = f"lock:{lock_name}"
            if lock_key not in self._memory_store:
                self._memory_store[lock_key] = {
                    "locked": True,
                    "expires_at": datetime.now() + timedelta(seconds=timeout)
                }
                return True
            
            # Check if lock expired
            lock_info = self._memory_store[lock_key]
            if datetime.now() > lock_info["expires_at"]:
                self._memory_store[lock_key] = {
                    "locked": True,
                    "expires_at": datetime.now() + timedelta(seconds=timeout)
                }
                return True
            
            return False
    
    async def release_lock(self, lock_name: str):
        """
        Release a distributed lock
        
        Args:
            lock_name: Name of the lock to release
        """
        lock_key = f"lock:{lock_name}"
        
        if self.use_redis:
            self.redis_client.delete(lock_key)
        else:
            self._memory_store.pop(lock_key, None)
    
    async def cleanup_expired_states(self):
        """Clean up expired states (mainly for in-memory store)"""
        if not self.use_redis:
            # Clean up expired locks
            current_time = datetime.now()
            expired_locks = []
            
            for key, value in self._memory_store.items():
                if key.startswith("lock:") and isinstance(value, dict):
                    if current_time > value.get("expires_at", current_time):
                        expired_locks.append(key)
            
            for key in expired_locks:
                self._memory_store.pop(key, None)
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check state manager health
        
        Returns:
            Health status dictionary
        """
        if self.use_redis:
            redis_healthy = self.redis_client.ping()
            return {
                "status": "healthy" if redis_healthy else "unhealthy",
                "backend": "redis",
                "redis_connected": redis_healthy
            }
        else:
            return {
                "status": "healthy",
                "backend": "memory",
                "warning": "In-memory store not suitable for production"
            }


# Global state manager instance
_state_manager: Optional[StateManager] = None


def get_state_manager() -> StateManager:
    """Get the global state manager instance"""
    global _state_manager
    if _state_manager is None:
        raise RuntimeError("State manager not initialized. Call init_state_manager() first.")
    return _state_manager


def init_state_manager(redis_url: str = None, use_redis: bool = True) -> StateManager:
    """
    Initialize the global state manager
    
    Args:
        redis_url: Redis connection URL
        use_redis: Whether to use Redis
        
    Returns:
        Initialized state manager instance
    """
    global _state_manager
    _state_manager = StateManager(redis_url, use_redis)
    return _state_manager
