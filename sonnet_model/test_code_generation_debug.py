"""
Test Code Generation and Critique Loop
This will specifically test the code generation and feedback process
"""

import asyncio
import json
import os
from datetime import datetime
from pathlib import Path

from task_manager.services.orchestrator import TaskOrchestrator

async def test_code_generation_debug():
    """Test the code generation and critique feedback loop"""
    
    print("🔍 CODE GENERATION & CRITIQUE DEBUG")
    print("=" * 50)
    
    # Create debug directory
    debug_dir = Path(f"code_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    debug_dir.mkdir(exist_ok=True)
    
    print(f"📁 Debug directory: {debug_dir}")
    print()
    
    # Configuration
    config = {
        "llm": {
            "provider": "ollama", 
            "model": "deepseek-coder-v2:16b",
            "base_url": "http://localhost:11434"
        },
        "coaching_enabled": True,
        "enable_quality_gates": True,
        "auto_recovery": True,
        "max_iterations": 2,
        "quality_threshold": 0.7
    }
    
    # Initialize orchestrator
    orchestrator = TaskOrchestrator(config)
    await orchestrator.state_manager.initialize()
    
    try:
        # Simple task for code generation
        task = "create a simple Python function that reads a CSV file and returns the data as a list of dictionaries"
        
        print(f"🎯 TASK: {task}")
        print()
        
        # STEP 1: Create plan
        print("📋 STEP 1: Creating plan...")
        result = await orchestrator.process_user_request(task, {})
        
        with open(debug_dir / "step1_plan_creation.json", 'w') as f:
            json.dump(result, f, indent=2, default=str)
        
        print(f"✅ Plan result: {result.get('type')}")
        
        if result.get("type") == "plan_created":
            plan = result.get("plan", {})
            plan_id = plan.get("id")
            
            print(f"📋 Plan ID: {plan_id}")
            print(f"🔧 Steps: {len(plan.get('steps', []))}")
            print()
            
            # STEP 2: Try to start execution with specific commands
            print("🚀 STEP 2: Starting code generation...")
            
            # Try different approaches to trigger code generation
            commands_to_try = [
                f"start plan {plan_id}",
                f"execute plan {plan_id}",
                f"begin implementation",
                f"generate code for {task}",
                f"implement the first task",
                "start coding"
            ]
            
            for i, command in enumerate(commands_to_try):
                print(f"   🔄 Trying command {i+1}: '{command}'")
                
                try:
                    cmd_result = await orchestrator.process_user_request(command, {})
                    
                    with open(debug_dir / f"command_{i+1}_result.json", 'w') as f:
                        json.dump({
                            "command": command,
                            "result": cmd_result
                        }, f, indent=2, default=str)
                    
                    print(f"      📊 Result type: {cmd_result.get('type')}")
                    
                    # Check if we got code generation
                    if any(key in cmd_result for key in ["code", "generated_code", "implementation"]):
                        print(f"      💻 CODE GENERATED!")
                        
                        code_data = {
                            "command": command,
                            "code": cmd_result.get("code", cmd_result.get("generated_code")),
                            "language": cmd_result.get("language", "python"),
                            "quality_score": cmd_result.get("quality_score", 0)
                        }
                        
                        with open(debug_dir / f"generated_code_{i+1}.json", 'w') as f:
                            json.dump(code_data, f, indent=2, default=str)
                        
                        # Now try to get critique
                        print(f"      🔍 Requesting critique...")
                        critique_result = await orchestrator.process_user_request("review the code", {})
                        
                        with open(debug_dir / f"critique_{i+1}.json", 'w') as f:
                            json.dump(critique_result, f, indent=2, default=str)
                        
                        print(f"      📋 Critique type: {critique_result.get('type')}")
                        break
                    
                    # Check if we got critique/feedback
                    elif any(key in cmd_result for key in ["feedback", "critique", "analysis"]):
                        print(f"      🔍 CRITIQUE PROVIDED!")
                        
                        feedback_data = {
                            "command": command,
                            "feedback": cmd_result.get("feedback", cmd_result.get("critique")),
                            "suggestions": cmd_result.get("suggestions", []),
                            "quality_score": cmd_result.get("quality_score", 0)
                        }
                        
                        with open(debug_dir / f"feedback_{i+1}.json", 'w') as f:
                            json.dump(feedback_data, f, indent=2, default=str)
                    
                    print()
                    
                except Exception as e:
                    print(f"      ❌ Command failed: {e}")
                    with open(debug_dir / f"command_{i+1}_error.json", 'w') as f:
                        json.dump({
                            "command": command,
                            "error": str(e),
                            "type": type(e).__name__
                        }, f, indent=2)
                    print()
            
            # STEP 3: Check what's in the state
            print("📊 STEP 3: Checking system state...")
            final_state = await orchestrator.state_manager.get_state_summary()
            
            with open(debug_dir / "final_state.json", 'w') as f:
                json.dump(final_state, f, indent=2, default=str)
            
            print(f"📋 Active plans: {final_state.get('active_plans', 0)}")
            print(f"🔧 Active tasks: {final_state.get('active_tasks', 0)}")
            print(f"✅ Completed tasks: {final_state.get('completed_tasks', 0)}")
            
            # Check for any generated files
            project_files = []
            if os.path.exists("generated_projects"):
                for root, dirs, files in os.walk("generated_projects"):
                    for file in files:
                        project_files.append(os.path.join(root, file))
            
            if project_files:
                print(f"📁 Generated files: {len(project_files)}")
                with open(debug_dir / "generated_files.json", 'w') as f:
                    json.dump({"files": project_files}, f, indent=2)
                
                for file in project_files:
                    print(f"   📄 {file}")
            else:
                print("📁 No generated files found")
        
        # Create summary
        summary = {
            "task": task,
            "debug_directory": str(debug_dir),
            "timestamp": datetime.now().isoformat(),
            "files_created": [f.name for f in debug_dir.glob("*.json")],
            "project_files": project_files if 'project_files' in locals() else []
        }
        
        with open(debug_dir / "summary.json", 'w') as f:
            json.dump(summary, f, indent=2)
        
        print("\n✅ Code generation debug completed!")
        print(f"📁 All debug data in: {debug_dir}")
        print(f"📄 Files created: {len(summary['files_created'])}")
        
        # Show what files were created
        print("\n📋 DEBUG FILES CREATED:")
        for file in sorted(debug_dir.glob("*.json")):
            print(f"   📄 {file.name}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
        # Save error info
        with open(debug_dir / "error.json", 'w') as f:
            json.dump({
                "error": str(e),
                "type": type(e).__name__,
                "traceback": traceback.format_exc()
            }, f, indent=2)
    
    finally:
        await orchestrator.state_manager.shutdown()

if __name__ == "__main__":
    asyncio.run(test_code_generation_debug())
