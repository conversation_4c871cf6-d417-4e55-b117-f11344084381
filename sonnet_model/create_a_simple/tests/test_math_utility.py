import unittest
from math_utility import add, subtract, multiply, divide  # Assuming these are defined elsewhere

class TestMathUtility(unittest.TestCase):
    """Unit tests for the math utility functions."""

    def test_add(self):
        """Test addition with positive and negative numbers."""
        self.assertEqual(add(1, 2), 3)
        self.assertEqual(add(-1, -2), -3)
        self.assertEqual(add(1, -2), -1)
        self.assertEqual(add(-1, 2), 1)
        with self.assertRaises(TypeError):
            add("1", "2")

    def test_subtract(self):
        """Test subtraction with positive and negative numbers."""
        self.assertEqual(subtract(3, 2), 1)
        self.assertEqual(subtract(-3, -2), -1)
        self.assertEqual(subtract(3, -2), 5)
        self.assertEqual(subtract(-3, 2), -5)
        with self.assertRaises(TypeError):
            subtract("3", "2")

    def test_multiply(self):
        """Test multiplication with positive and negative numbers."""
        self.assertEqual(multiply(1, 2), 2)
        self.assertEqual(multiply(-1, -2), 2)
        self.assertEqual(multiply(1, -2), -2)
        self.assertEqual(multiply(-1, 2), -2)
        with self.assertRaises(TypeError):
            multiply("1", "2")

    def test_divide(self):
        """Test division with positive and negative numbers, including edge cases like dividing by zero."""
        self.assertEqual(divide(4, 2), 2)
        self.assertEqual(divide(-4, -2), 2)
        self.assertEqual(divide(4, -2), -2)
        self.assertEqual(divide(-4, 2), -2)
        with self.assertRaises(ZeroDivisionError):
            divide(4, 0)
        with self.assertRaises(TypeError):
            divide("4", "2")

if __name__ == "__main__":
    unittest.main()