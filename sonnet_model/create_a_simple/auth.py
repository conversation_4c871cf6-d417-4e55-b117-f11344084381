from flask import Flask, request, jsonify, abort
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
import datetime
from functools import wraps

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'  # Replace with a secure secret key
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///database.db'  # Use your database URI

db = SQLAlchemy(app)

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

# Helper function to get user by username
def get_user_by_username(username):
    return User.query.filter_by(username=username).first()

# Decorator for token verification
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers['Authorization']
        if not token:
            return jsonify({'message': 'Token is missing'}), 403
        try:
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user = get_user_by_username(data['username'])
        except Exception as e:
            return jsonify({'message': 'Token is invalid'}), 403
        return f(current_user, *args, **kwargs)
    return decorated

# Registration endpoint
@app.route('/register', methods=['POST'])
def register():
    """
    Register a new user.
    
    ---
    parameters:
      - name: username
        in: formData
        type: string
        required: true
        description: The username for the new account.
      - name: password
        in: formData
        type: string
        required: true
        description: The password for the new account.
    responses:
      201:
        description: User registered successfully.
        schema:
          id: User
          properties:
            username:
              type: string
              description: The username of the registered user.
            id:
              type: integer
              description: The unique identifier for the user.
      400:
        description: Invalid request or username already taken.
    """
    data = request.form
    if not data:
        return jsonify({'message': 'Invalid request'}), 400
    
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'message': 'Username and password are required'}), 400
    
    existing_user = get_user_by_username(username)
    if existing_user:
        return jsonify({'message': 'Username already taken'}), 400
    
    new_user = User(username=username)
    new_user.set_password(password)
    db.session.add(new_user)
    db.session.commit()
    
    return jsonify({'message': 'User registered successfully', 'id': new_user.id, 'username': new_user.username}), 201

# Login endpoint
@app.route('/login', methods=['POST'])
def login():
    """
    Authenticate a user and generate a token.
    
    ---
    parameters:
      - name: username
        in: formData
        type: string
        required: true
        description: The username for the account.
      - name: password
        in: formData
        type: string
        required: true
        description: The password for the account.
    responses:
      200:
        description: User authenticated successfully.
        schema:
          id: Token
          properties:
            token:
              type: string
              description: The JWT token for authentication.
      401:
        description: Invalid username or password.
    """
    data = request.form
    if not data:
        return jsonify({'message': 'Invalid request'}), 400
    
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'message': 'Username and password are required'}), 400
    
    user = get_user_by_username(username)
    if not user or not user.check_password(password):
        return jsonify({'message': 'Invalid username or password'}), 401
    
    token = jwt.encode({
        'username': user.username,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(minutes=30)
    }, app.config['SECRET_KEY'], algorithm='HS256')
    
    return jsonify({'token': token})

if __name__ == '__main__':
    db.create_all()
    app.run(debug=True)