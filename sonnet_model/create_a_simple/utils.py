import os
import jwt
from datetime import datetime, timedelta
from typing import Any, Dict, Optional
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.exc import SQLAlchemyError
from flask import Flask, request, jsonify

# Configuration for JWT
JWT_SECRET = os.getenv('JWT_SECRET', 'default-secret')
JWT_ALGORITHM = os.getenv('JWT_ALGORITHM', 'HS256')
JWT_EXP_DELTA_SECONDS = int(os.getenv('JWT_EXP_DELTA_SECONDS', 300))

# SQLAlchemy setup
DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///app.db')
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()
metadata = MetaData(bind=engine)

# Function to initialize the database if it doesn't exist
def init_db():
    """Initialize the database schema."""
    Base.metadata.create_all(bind=engine)

# Exception Handling for SQLAlchemy operations
def get_db_session():
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

# Utility function to generate a JWT token
def generate_token(data: Dict[str, Any]) -> str:
    """Generate a JWT token."""
    expiration = datetime.utcnow() + timedelta(seconds=JWT_EXP_DELTA_SECONDS)
    payload = {"exp": expiration, **data}
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

# Utility function to decode a JWT token
def decode_token(token: str) -> Dict[str, Any]:
    """Decode a JWT token."""
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

# Flask app setup for demonstration purposes
app = Flask(__name__)

@app.before_request
def before_request():
    """Middleware to check JWT token on each request."""
    if 'Authorization' in request.headers:
        token = request.headers['Authorization'].split(' ')[1]
        payload = decode_token(token)
        request.user = payload  # Assuming the payload contains user information

@app.errorhandler(HTTPException)
def handle_exception(e):
    """Error handler for HTTP exceptions."""
    response = {
        "error": e.name,
        "message": e.description,
    }
    return jsonify(response), e.code

# Example of using the utility functions in a Flask route
@app.route('/protected')
def protected():
    """A protected endpoint that requires a valid JWT token."""
    user_info = request.user  # This will be populated by the middleware if the token is valid
    return jsonify({"user": user_info})

if __name__ == "__main__":
    init_db()
    app.run(debug=True)