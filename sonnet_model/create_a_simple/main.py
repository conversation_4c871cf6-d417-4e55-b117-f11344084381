from flask import Flask, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
import logging

# Initialize the Flask application
app = Flask(__name__)

# Configure the SQLite database using SQLAlchemy
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///database.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize the database connection
db = SQLAlchemy(app)

# Set up CORS to allow requests from different origins
CORS(app, resources={r"/api/*": {"origins": "*"}})

# Configure logging for production environment
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Example model (you can define your own models here)
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)

    def __repr__(self):
        return f'<User {self.username}>'

# Helper function to log exceptions
def log_exception(e):
    logger.error(f'An error occurred: {str(e)}')

# Define a more specific route for the root endpoint
@app.route('/api/')
def home():
    try:
        users = User.query.all()
        logger.info(f'Retrieved {len(users)} users from the database.')
        user_list = [{'id': user.id, 'username': user.username, 'email': user.email} for user in users]
        return jsonify(user_list)
    except Exception as e:
        log_exception(e)
        return jsonify({'error': 'An error occurred while retrieving users.'}), 500

# Example route for getting a specific user by ID
@app.route('/api/users/<int:user_id>')
def get_user(user_id):
    try:
        user = User.query.get_or_404(user_id)
        logger.info(f'Retrieved user {user.username} with ID {user.id}.')
        return jsonify({'user': {'id': user.id, 'username': user.username, 'email': user.email}})
    except Exception as e:
        log_exception(e)
        return jsonify({'error': 'An error occurred while retrieving the user.'}), 500

# Ensure database tables are created when running the app for the first time
@app.before_first_request
def create_tables():
    with app.app_context():
        db.create_all()

# Run the application
if __name__ == '__main__':
    app.run(debug=False, host='0.0.0.0', port=5000)