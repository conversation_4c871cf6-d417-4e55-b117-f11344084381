from flask import Flask
from flask_restful import Api, Resource, reqparse, fields, marshal_with, abort
from flask_sqlalchemy import SQLAlchemy
from flask_httpauth import HTTPBasicAuth
import os

app = Flask(__name__)
api = Api(app)
app.config['SECRET_KEY'] = os.urandom(32)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///tasks.db'
db = SQLAlchemy(app)
auth = HTTPBasicAuth()

# Define the Task model
class Task(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.String(200))

# Define the fields for marshalling
task_fields = {
    'id': fields.Integer,
    'name': fields.String,
    'description': fields.String
}

# User authentication and validation
users = {
    "admin": "password"
}

@auth.verify_password
def verify_password(username, password):
    if username in users:
        return users.get(username) == password
    return False

# Define the TaskResource class for RESTful API
class TaskResource(Resource):
    # Method to get a task by ID with authentication and error handling
    @marshal_with(task_fields)
    @auth.login_required
    def get(self, task_id):
        """
        Get a task by its ID.
        
        Returns:
            Task: The requested task object.
        Raises:
            404 Not Found: If the task does not exist.
        """
        task = Task.query.get(task_id)
        if not task:
            abort(404, message="Task {} doesn't exist".format(task_id))
        return task

    # Method to create a new task with authentication and error handling
    @auth.login_required
    def post(self):
        """
        Create a new task.
        
        Returns:
            Task: The created task object.
        Raises:
            400 Bad Request: If the request data is not valid.
        """
        parser = reqparse.RequestParser()
        parser.add_argument('name', type=str, required=True, help="Name cannot be blank!")
        parser.add_argument('description', type=str)
        args = parser.parse_args()
        new_task = Task(name=args['name'], description=args['description'])
        db.session.add(new_task)
        db.session.commit()
        return new_task, 201

    # Method to update an existing task by ID with authentication and error handling
    @marshal_with(task_fields)
    @auth.login_required
    def put(self, task_id):
        """
        Update a task by its ID.
        
        Returns:
            Task: The updated task object.
        Raises:
            400 Bad Request: If the request data is not valid.
            404 Not Found: If the task does not exist.
        """
        parser = reqparse.RequestParser()
        parser.add_argument('name', type=str)
        parser.add_argument('description', type=str)
        args = parser.parse_args()
        task = Task.query.get(task_id)
        if not task:
            abort(404, message="Task {} doesn't exist".format(task_id))
        if args['name']:
            task.name = args['name']
        if args['description']:
            task.description = args['description']
        db.session.commit()
        return task

    # Method to delete a task by ID with authentication and error handling
    @auth.login_required
    def delete(self, task_id):
        """
        Delete a task by its ID.
        
        Raises:
            404 Not Found: If the task does not exist.
        """
        task = Task.query.get(task_id)
        if not task:
            abort(404, message="Task {} doesn't exist".format(task_id))
        db.session.delete(task)
        db.session.commit()
        return '', 204

# Define the API endpoints
api.add_resource(TaskResource, '/tasks', '/tasks/<int:task_id>')

if __name__ == '__main__':
    app.run(debug=True)