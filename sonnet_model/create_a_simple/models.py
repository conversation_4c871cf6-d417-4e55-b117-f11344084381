from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import MinLengthValidator

class User(AbstractUser):
    """Custom User model that inherits from AbstractUser."""
    
    username = models.CharField(
        max_length=150, 
        unique=True, 
        validators=[MinLengthValidator(2)],
        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
    )
    
    password = models.CharField(max_length=128)
    
    def __str__(self):
        return self.username
    
    class Meta:
        verbose_name = 'User'
        verbose_name_plural = 'Users'

class Task(models.Model):
    """Task model linked to a User."""
    
    title = models.CharField(max_length=200, validators=[MinLengthValidator(3)], help_text="Required. Maximum 200 characters.")
    description = models.TextField(blank=True, null=True)
    due_date = models.DateField(null=True, blank=True)
    completed = models.BooleanField(default=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='tasks', help_text="The user to whom this task is assigned.")
    
    def __str__(self):
        return self.title
    
    class Meta:
        verbose_name = 'Task'
        verbose_name_plural = 'Tasks'
        ordering = ['due_date']

# Ensure you run migrations after defining models