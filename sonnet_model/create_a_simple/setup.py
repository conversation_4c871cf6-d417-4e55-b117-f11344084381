import setuptools

# Define the dependencies
dependencies = [
    'numpy>=1.20',  # Example dependency for numerical operations
    'scipy>=1.6'    # Example dependency for scientific computing
]

setuptools.setup(
    name='math_utility',          # The name of your package
    version='0.1.0',               # Version number
    author='Your Name',           # Your name or organization
    author_email='<EMAIL>',  # Your email address
    description='A collection of mathematical utility functions',  # Short description
    long_description=open('README.md').read(),  # Long description from a README file
    long_description_content_type='text/markdown',  # Content type for the long description
    url='https://github.com/yourusername/math_utility',  # URL to your package's home page
    packages=setuptools.find_packages(),  # Automatically find all packages and modules
    classifiers=[
        'Programming Language :: Python :: 3',
        'License :: OSI Approved :: MIT License',
        'Operating System :: OS Independent',
    ],
    python_requires='>=3.6',  # Minimum required version of Python
    install_requires=dependencies,  # List of dependencies
)