"""
This module provides utility functions to perform basic arithmetic operations.
"""

def add(a: float, b: float) -> float:
    """
    Adds two numbers and returns the result.
    
    Args:
        a (float): The first number.
        b (float): The second number.
        
    Returns:
        float: The sum of the two numbers.
    """
    return a + b

def subtract(a: float, b: float) -> float:
    """
    Subtracts one number from another and returns the result.
    
    Args:
        a (float): The number to be subtracted from.
        b (float): The number to subtract.
        
    Returns:
        float: The result of subtracting b from a.
    """
    return a - b

def multiply(a: float, b: float) -> float:
    """
    Multiplies two numbers and returns the result.
    
    Args:
        a (float): The first number.
        b (float): The second number.
        
    Returns:
        float: The product of the two numbers.
    """
    return a * b

def divide(a: float, b: float) -> float:
    """
    Divides one number by another and returns the result.
    
    Args:
        a (float): The dividend.
        b (float): The divisor.
        
    Returns:
        float: The quotient of the division.
        
    Raises:
        ValueError: If the divisor is zero, it raises a ValueError indicating division by zero is not allowed.
    """
    if b == 0:
        raise ValueError("Division by zero is not allowed.")
    return a / b